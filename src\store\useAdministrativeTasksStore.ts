import { create } from "zustand";
import {
  AdministrativeTask,
  getAdministrativeTasks,
  createAdministrativeTask,
  updateAdministrativeTask,
  deleteAdministrativeTask
} from "@/api/api";

interface AdministrativeTasksState {
  tasks: AdministrativeTask[];
  loading: boolean;
  error: string | null;
  fetchTasks: (clubId: number) => Promise<void>;
  addTask: (clubId: number, task: Omit<AdministrativeTask, "id" | "created_at" | "updated_at">) => Promise<void>;
  updateTask: (clubId: number, id: number, updates: Partial<AdministrativeTask>) => Promise<void>;
  updateTaskStatus: (clubId: number, id: number, status: string) => Promise<void>;
  removeTask: (clubId: number, id: number) => Promise<void>;
  getTasksByStatus: (status: string) => AdministrativeTask[];
}

export const useAdministrativeTasksStore = create<AdministrativeTasksState>((set, get) => ({
  tasks: [],
  loading: false,
  error: null,

  fetchTasks: async (clubId: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const tasks = await getAdministrativeTasks(clubId);
      set({ tasks, loading: false });
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao buscar tarefas", loading: false });
    }
  },

  addTask: async (
    clubId: number,
    task: Omit<AdministrativeTask, "id" | "created_at" | "updated_at">
  ): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const newTask = await createAdministrativeTask(clubId, task);
      set((state) => ({
        tasks: [...state.tasks, newTask],
        loading: false
      }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao adicionar tarefa", loading: false });
    }
  },

  updateTask: async (
    clubId: number,
    id: number,
    updates: Partial<AdministrativeTask>
  ): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const updatedTask = await updateAdministrativeTask(clubId, id, updates);
      set((state) => ({
        tasks: state.tasks.map(task => task.id === id ? updatedTask : task),
        loading: false
      }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao atualizar tarefa", loading: false });
    }
  },

  updateTaskStatus: async (
    clubId: number,
    id: number,
    status: string
  ): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const updatedTask = await updateAdministrativeTask(clubId, id, { status });
      set((state) => ({
        tasks: state.tasks.map(task => task.id === id ? updatedTask : task),
        loading: false
      }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao atualizar status da tarefa", loading: false });
    }
  },

  removeTask: async (clubId: number, id: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      await deleteAdministrativeTask(clubId, id);
      set((state) => ({
        tasks: state.tasks.filter(task => task.id !== id),
        loading: false
      }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao remover tarefa", loading: false });
    }
  },

  getTasksByStatus: (status: string): AdministrativeTask[] => {
    return get().tasks.filter(task => task.status === status);
  }
}));
