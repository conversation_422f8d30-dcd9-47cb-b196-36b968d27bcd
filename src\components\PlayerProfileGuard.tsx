import { ReactNode, useEffect, useState } from "react";
import { Navigate, useParams } from "react-router-dom";
import { usePermission } from "@/hooks/usePermission";
import { useUser } from "@/context/UserContext";
import { getPlayerByUserId } from "@/api/players";
import { useCurrentClubId } from "@/context/ClubContext";

interface PlayerProfileGuardProps {
  children: ReactNode;
  fallbackPath?: string;
}

/**
 * Componente para proteger a página de perfil do jogador
 * Permite acesso apenas se:
 * 1. O usuário tem a permissão players.view (administradores, técnicos, etc.)
 * 2. OU o usuário é o próprio jogador (role=player) e está acessando seu próprio perfil
 */
export function PlayerProfileGuard({ 
  children, 
  fallbackPath = "/dashboard"
}: PlayerProfileGuardProps) {
  const { id } = useParams<{ id: string }>();
  const { isLoaded, can, role } = usePermission();
  const { user } = useUser();
  const clubId = useCurrentClubId();
  const [hasAccess, setHasAccess] = useState(false);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Verificar se o usuário tem permissão para ver qualquer jogador
    if (isLoaded && can("players.view")) {
      setHasAccess(true);
      setLoading(false);
      return;
    }
    
    // Se for um jogador, verificar se está acessando seu próprio perfil
    const checkPlayerAccess = async () => {
      if (isLoaded && role === "player" && user?.id && id) {
        try {
          // Obter o jogador associado ao usuário atual
          const playerData = await getPlayerByUserId(clubId, user.id);
          
          if (playerData && playerData.id === id) {
            // O jogador está acessando seu próprio perfil
            setHasAccess(true);
          } else {
            // O jogador está tentando acessar outro perfil
            setHasAccess(false);
          }
        } catch (error) {
          console.error("Erro ao verificar acesso do jogador:", error);
          setHasAccess(false);
        }
      } else {
        setHasAccess(false);
      }
      
      setLoading(false);
    };
    
    if (isLoaded && user?.id) {
      checkPlayerAccess();
    }
  }, [isLoaded, can, role, user?.id, id, clubId]);
  
  // Enquanto estiver carregando, mostrar indicador de carregamento
  if (loading || !isLoaded) {
    return (
      <div className="flex justify-center items-center h-80">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-team-blue"></div>
      </div>
    );
  }
  
  // Se não tiver acesso, redirecionar
  if (!hasAccess) {
    return <Navigate to={fallbackPath} replace />;
  }
  
  // Se tiver acesso, renderizar o conteúdo
  return <>{children}</>;
}
