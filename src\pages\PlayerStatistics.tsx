import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { getPlayer, getPlayerAggregatedStatistics, getPlayerMatchesWithStats } from "@/api/api";
import { ChevronLeft, BarChart2, TrendingUp, Calendar } from "lucide-react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  RadialLinearScale,
  ArcElement,
} from 'chart.js';
import { Line, Bar, Radar } from 'react-chartjs-2';

// Registrar componentes do Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  RadialLinearScale,
  ArcElement
);

export default function PlayerStatistics() {
  const { playerId } = useParams<{ playerId: string }>();
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { toast } = useToast();
  
  const [player, setPlayer] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<any>(null);
  const [matchStats, setMatchStats] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    const fetchData = async () => {
      if (!clubId || !playerId || !user?.id) return;

      try {
        setLoading(true);
        
        // Carregar dados do jogador
        const playerData = await getPlayer(clubId, playerId);
        setPlayer(playerData);
        
        // Carregar estatísticas agregadas
        const statsData = await getPlayerAggregatedStatistics(clubId, playerId, user.id);
        setStats(statsData);
        
        // Carregar estatísticas por partida
        const matchesWithStats = await getPlayerMatchesWithStats(clubId, playerId);
        setMatchStats(matchesWithStats);
      } catch (error) {
        console.error("Erro ao carregar estatísticas:", error);
        toast({
          title: "Erro",
          description: "Não foi possível carregar as estatísticas do jogador",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [clubId, playerId, user?.id, toast]);

  // Preparar dados para os gráficos
  const prepareChartData = () => {
    if (!matchStats || matchStats.length === 0) return null;

    // Ordenar partidas por data
    const sortedMatches = [...matchStats].sort((a, b) => 
      new Date(a.match_date).getTime() - new Date(b.match_date).getTime()
    );

    // Dados para o gráfico de linha (gols e assistências por partida)
    const lineChartData = {
      labels: sortedMatches.map(match => match.opponent_name),
      datasets: [
        {
          label: 'Gols',
          data: sortedMatches.map(match => match.goals || 0),
          borderColor: 'rgb(53, 162, 235)',
          backgroundColor: 'rgba(53, 162, 235, 0.5)',
        },
        {
          label: 'Assistências',
          data: sortedMatches.map(match => match.assists || 0),
          borderColor: 'rgb(75, 192, 192)',
          backgroundColor: 'rgba(75, 192, 192, 0.5)',
        }
      ],
    };

    // Dados para o gráfico de barras (minutos jogados por partida)
    const barChartData = {
      labels: sortedMatches.map(match => match.opponent_name),
      datasets: [
        {
          label: 'Minutos Jogados',
          data: sortedMatches.map(match => match.minutes_played || 0),
          backgroundColor: 'rgba(255, 159, 64, 0.5)',
        }
      ],
    };

    // Dados para o gráfico radar (estatísticas gerais)
    const radarChartData = {
      labels: ['Gols', 'Assistências', 'Finalizações', 'Passes', 'Desarmes', 'Minutos'],
      datasets: [
        {
          label: player?.name || 'Jogador',
          data: [
            stats?.total_goals || 0,
            stats?.total_assists || 0,
            stats?.total_shots || 0,
            stats?.total_passes_completed || 0,
            stats?.total_tackles || 0,
            stats?.total_minutes ? Math.min(100, stats.total_minutes / 10) : 0, // Normalizar minutos para escala
          ],
          backgroundColor: 'rgba(54, 162, 235, 0.2)',
          borderColor: 'rgb(54, 162, 235)',
          pointBackgroundColor: 'rgb(54, 162, 235)',
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: 'rgb(54, 162, 235)'
        }
      ]
    };

    return { lineChartData, barChartData, radarChartData };
  };

  const chartData = prepareChartData();

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center mb-6">
          <Button variant="ghost" asChild className="mr-4">
            <Link to={`/jogador/${playerId}`}><ChevronLeft className="mr-2 h-4 w-4" /> Voltar</Link>
          </Button>
          <h1 className="text-3xl font-bold">Carregando estatísticas...</h1>
        </div>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded mb-6"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center mb-6">
        <Button variant="ghost" asChild className="mr-4">
          <Link to={`/jogador/${playerId}`}><ChevronLeft className="mr-2 h-4 w-4" /> Voltar</Link>
        </Button>
        <h1 className="text-3xl font-bold">{player?.name} - Estatísticas</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Gols</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-4xl font-bold text-blue-600">{stats?.total_goals || 0}</div>
            <p className="text-sm text-muted-foreground">Total de gols marcados</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Assistências</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-4xl font-bold text-green-600">{stats?.total_assists || 0}</div>
            <p className="text-sm text-muted-foreground">Total de assistências</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Jogos</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-4xl font-bold text-amber-600">{stats?.total_matches || 0}</div>
            <p className="text-sm text-muted-foreground">Total de partidas disputadas</p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview" className="flex items-center">
            <BarChart2 className="mr-2 h-4 w-4" />
            Visão Geral
          </TabsTrigger>
          <TabsTrigger value="trends" className="flex items-center">
            <TrendingUp className="mr-2 h-4 w-4" />
            Tendências
          </TabsTrigger>
          <TabsTrigger value="matches" className="flex items-center">
            <Calendar className="mr-2 h-4 w-4" />
            Partidas
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Estatísticas Gerais</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Ofensivas</h3>
                  <div className="space-y-4">
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span>Minutos jogados</span>
                        <span className="font-medium">{stats?.total_minutes || 0}'</span>
                      </div>
                      <div className="h-2 bg-muted rounded-full overflow-hidden">
                        <div className="h-full bg-blue-600" style={{ width: `${Math.min(100, (stats?.total_minutes || 0) / 10)}%` }} />
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span>Gols</span>
                        <span className="font-medium">{stats?.total_goals || 0}</span>
                      </div>
                      <div className="h-2 bg-muted rounded-full overflow-hidden">
                        <div className="h-full bg-blue-600" style={{ width: `${Math.min(100, (stats?.total_goals || 0) * 20)}%` }} />
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span>Assistências</span>
                        <span className="font-medium">{stats?.total_assists || 0}</span>
                      </div>
                      <div className="h-2 bg-muted rounded-full overflow-hidden">
                        <div className="h-full bg-blue-600" style={{ width: `${Math.min(100, (stats?.total_assists || 0) * 20)}%` }} />
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span>Finalizações</span>
                        <span className="font-medium">{stats?.total_shots || 0}</span>
                      </div>
                      <div className="h-2 bg-muted rounded-full overflow-hidden">
                        <div className="h-full bg-blue-600" style={{ width: `${Math.min(100, (stats?.total_shots || 0) * 5)}%` }} />
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span>Finalizações no gol</span>
                        <span className="font-medium">{stats?.total_shots_on_target || 0}</span>
                      </div>
                      <div className="h-2 bg-muted rounded-full overflow-hidden">
                        <div className="h-full bg-blue-600" style={{ width: `${Math.min(100, (stats?.total_shots_on_target || 0) * 10)}%` }} />
                      </div>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold mb-4">Defensivas e Passes</h3>
                  <div className="space-y-4">
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span>Passes</span>
                        <span className="font-medium">{stats?.total_passes || 0}</span>
                      </div>
                      <div className="h-2 bg-muted rounded-full overflow-hidden">
                        <div className="h-full bg-green-600" style={{ width: `${Math.min(100, (stats?.total_passes || 0) / 5)}%` }} />
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span>Passes completados</span>
                        <span className="font-medium">{stats?.total_passes_completed || 0}</span>
                      </div>
                      <div className="h-2 bg-muted rounded-full overflow-hidden">
                        <div className="h-full bg-green-600" style={{ width: `${Math.min(100, (stats?.total_passes_completed || 0) / 5)}%` }} />
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span>Precisão de passes</span>
                        <span className="font-medium">
                          {stats?.total_passes 
                            ? `${((stats.total_passes_completed / stats.total_passes) * 100).toFixed(1)}%` 
                            : '0%'}
                        </span>
                      </div>
                      <div className="h-2 bg-muted rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-green-600" 
                          style={{ 
                            width: `${stats?.total_passes 
                              ? Math.min(100, (stats.total_passes_completed / stats.total_passes) * 100) 
                              : 0}%` 
                          }} 
                        />
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span>Desarmes</span>
                        <span className="font-medium">{stats?.total_tackles || 0}</span>
                      </div>
                      <div className="h-2 bg-muted rounded-full overflow-hidden">
                        <div className="h-full bg-green-600" style={{ width: `${Math.min(100, (stats?.total_tackles || 0) * 10)}%` }} />
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span>Cartões</span>
                        <span className="font-medium">
                          <span className="text-yellow-500 mr-1">{stats?.total_yellow_cards || 0}</span> / 
                          <span className="text-red-500 ml-1">{stats?.total_red_cards || 0}</span>
                        </span>
                      </div>
                      <div className="h-2 bg-muted rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-yellow-500" 
                          style={{ width: `${Math.min(100, (stats?.total_yellow_cards || 0) * 20)}%` }} 
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          {chartData ? (
            <>
              <Card>
                <CardHeader>
                  <CardTitle>Gols e Assistências por Partida</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px]">
                    <Line 
                      data={chartData.lineChartData} 
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                          y: {
                            beginAtZero: true,
                            ticks: {
                              stepSize: 1
                            }
                          }
                        }
                      }} 
                    />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Minutos Jogados por Partida</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px]">
                    <Bar 
                      data={chartData.barChartData} 
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                          y: {
                            beginAtZero: true
                          }
                        }
                      }} 
                    />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Perfil de Desempenho</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px] flex justify-center">
                    <div className="w-[300px]">
                      <Radar 
                        data={chartData.radarChartData} 
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          scales: {
                            r: {
                              beginAtZero: true
                            }
                          }
                        }} 
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-muted-foreground">Não há dados suficientes para exibir tendências</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="matches" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Estatísticas por Partida</CardTitle>
            </CardHeader>
            <CardContent>
              {matchStats.length === 0 ? (
                <p className="text-center text-muted-foreground">Nenhuma estatística de partida disponível</p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-muted">
                        <th className="border p-2 text-left">Data</th>
                        <th className="border p-2 text-left">Adversário</th>
                        <th className="border p-2 text-center">Min</th>
                        <th className="border p-2 text-center">Gols</th>
                        <th className="border p-2 text-center">Assist</th>
                        <th className="border p-2 text-center">Finalizações</th>
                        <th className="border p-2 text-center">No Gol</th>
                        <th className="border p-2 text-center">Passes</th>
                        <th className="border p-2 text-center">Precisão</th>
                        <th className="border p-2 text-center">Desarmes</th>
                        <th className="border p-2 text-center">CA</th>
                        <th className="border p-2 text-center">CV</th>
                      </tr>
                    </thead>
                    <tbody>
                      {matchStats.map((match, index) => {
                        const passAccuracy = match.passes ? 
                          ((match.passes_completed / match.passes) * 100).toFixed(1) : '0';
                        
                        return (
                          <tr key={index} className="hover:bg-gray-50">
                            <td className="border p-2">{new Date(match.match_date).toLocaleDateString()}</td>
                            <td className="border p-2">{match.opponent_name}</td>
                            <td className="border p-2 text-center">{match.minutes_played || 0}</td>
                            <td className="border p-2 text-center">{match.goals || 0}</td>
                            <td className="border p-2 text-center">{match.assists || 0}</td>
                            <td className="border p-2 text-center">{match.shots || 0}</td>
                            <td className="border p-2 text-center">{match.shots_on_target || 0}</td>
                            <td className="border p-2 text-center">{match.passes_completed || 0}/{match.passes || 0}</td>
                            <td className="border p-2 text-center">{passAccuracy}%</td>
                            <td className="border p-2 text-center">{match.tackles || 0}</td>
                            <td className="border p-2 text-center">{match.yellow_cards || 0}</td>
                            <td className="border p-2 text-center">{match.red_cards || 0}</td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
