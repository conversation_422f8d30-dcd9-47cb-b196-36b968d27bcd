import * as React from "react";
import { Input } from "@/components/ui/input";

export interface TimePickerInputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  picker: "hours" | "minutes" | "seconds";
  date: Date;
  setDate: (date: Date) => void;
  onRightFocus?: () => void;
  onLeftFocus?: () => void;
}

const TimePickerInput = React.forwardRef<
  HTMLInputElement,
  TimePickerInputProps
>(
  (
    {
      className,
      picker,
      date,
      setDate,
      onLeftFocus,
      onRightFocus,
      ...props
    },
    ref
  ) => {
    const [value, setValue] = React.useState<string | number>(() => {
      if (picker === "hours") {
        return date.getHours();
      }
      if (picker === "minutes") {
        return date.getMinutes();
      }
      return date.getSeconds();
    });

    React.useEffect(() => {
      setValue(() => {
        if (picker === "hours") {
          return date.getHours().toString().padStart(2, "0");
        }
        if (picker === "minutes") {
          return date.getMinutes().toString().padStart(2, "0");
        }
        return date.getSeconds().toString().padStart(2, "0");
      });
    }, [date, picker]);

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === "ArrowRight" && onRightFocus) {
        e.preventDefault();
        onRightFocus();
      }
      if (e.key === "ArrowLeft" && onLeftFocus) {
        e.preventDefault();
        onLeftFocus();
      }
      if (e.key === "Tab") {
        return;
      }
      if (e.key === "ArrowUp") {
        e.preventDefault();
        const newValue = getNewValue(true);
        updateDate(newValue);
        return;
      }
      if (e.key === "ArrowDown") {
        e.preventDefault();
        const newValue = getNewValue(false);
        updateDate(newValue);
        return;
      }
      if (!/[0-9]/.test(e.key)) {
        e.preventDefault();
        return;
      }
    };

    const getNewValue = (increase: boolean) => {
      if (picker === "hours") {
        const newValue = increase
          ? (parseInt(value.toString()) + 1) % 24
          : (parseInt(value.toString()) - 1 + 24) % 24;
        return newValue;
      }
      if (picker === "minutes" || picker === "seconds") {
        const newValue = increase
          ? (parseInt(value.toString()) + 1) % 60
          : (parseInt(value.toString()) - 1 + 60) % 60;
        return newValue;
      }
      return 0;
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      let inputValue = e.target.value;
      if (inputValue === "") {
        setValue("");
        return;
      }
      const numericValue = parseInt(inputValue);
      if (isNaN(numericValue)) {
        return;
      }
      if (picker === "hours" && numericValue > 23) {
        return;
      }
      if ((picker === "minutes" || picker === "seconds") && numericValue > 59) {
        return;
      }
      setValue(numericValue);
      updateDate(numericValue);
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      const inputValue = e.target.value;
      if (inputValue === "") {
        setValue("00");
        return;
      }
      const numericValue = parseInt(inputValue);
      setValue(numericValue.toString().padStart(2, "0"));
    };

    const updateDate = (newValue: number) => {
      const newDate = new Date(date);
      if (picker === "hours") {
        newDate.setHours(newValue);
      }
      if (picker === "minutes") {
        newDate.setMinutes(newValue);
      }
      if (picker === "seconds") {
        newDate.setSeconds(newValue);
      }
      setDate(newDate);
    };

    return (
      <Input
        ref={ref}
        className="w-[40px] text-center"
        value={value}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        onBlur={handleBlur}
        {...props}
      />
    );
  }
);

TimePickerInput.displayName = "TimePickerInput";

export { TimePickerInput };
