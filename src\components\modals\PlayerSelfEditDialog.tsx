import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON>T<PERSON>le, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { updatePlayer } from "@/api/players";
import { Player } from "@/api/api";

interface PlayerSelfEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  player: Player | null;
  onSuccess?: () => void;
}

/**
 * Modal para jogadores editarem seu próprio perfil
 * Permite editar apenas campos específicos:
 * - Nome
 * - Apelido
 * - Altura
 * - Peso
 * - Nome do pai
 * - Nome da mãe
 * - Informações de contato (telefone, email, endereço, CEP, cidade, estado)
 */
export function PlayerSelfEditDialog({ 
  open, 
  onOpenChange, 
  player, 
  onSuccess 
}: PlayerSelfEditDialogProps) {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const [activeTab, setActiveTab] = useState("personal");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Dados pessoais
  const [name, setName] = useState("");
  const [nickname, setNickname] = useState("");
  const [height, setHeight] = useState("");
  const [weight, setWeight] = useState("");
  const [fatherName, setFatherName] = useState("");
  const [motherName, setMotherName] = useState("");

  // Dados de contato
  const [phone, setPhone] = useState("");
  const [email, setEmail] = useState("");
  const [address, setAddress] = useState("");
  const [zipCode, setZipCode] = useState("");
  const [city, setCity] = useState("");
  const [state, setState] = useState("");

  // Atualiza os campos quando o jogador muda ou o modal é aberto
  useEffect(() => {
    if (player) {
      // Dados pessoais
      setName(player.name || "");
      setNickname(player.nickname || "");
      setHeight(player.height?.toString() || "");
      setWeight(player.weight?.toString() || "");
      setFatherName(player.father_name || "");
      setMotherName(player.mother_name || "");

      // Dados de contato
      setPhone(player.phone || "");
      setEmail(player.email || "");
      setAddress(player.address || "");
      setZipCode(player.zip_code || "");
      setCity(player.city || "");
      setState(player.state || "");
    }
  }, [player, open]);

  const handleSubmit = async () => {
    if (!player?.id || !user?.id) {
      setError("Dados do jogador não encontrados");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Preparar dados para atualização
      const playerData: Partial<Player> = {
        // Dados pessoais
        name,
        nickname,
        height: height ? parseInt(height) : undefined,
        weight: weight ? parseInt(weight) : undefined,
        father_name: fatherName,
        mother_name: motherName,

        // Dados de contato
        phone,
        email,
        address,
        zip_code: zipCode,
        city,
        state,
      };

      // Remover campos vazios
      Object.keys(playerData).forEach(key => {
        if (playerData[key as keyof typeof playerData] === "") {
          delete playerData[key as keyof typeof playerData];
        }
      });

      // Atualizar jogador
      await updatePlayer(clubId, player.id, playerData, user.id);

      toast({
        title: "Sucesso",
        description: "Perfil atualizado com sucesso",
      });

      onOpenChange(false);

      if (onSuccess) {
        onSuccess();
      }
    } catch (err: any) {
      console.error("Erro ao atualizar perfil:", err);
      setError(err.message || "Erro ao atualizar perfil");
      toast({
        title: "Erro",
        description: err.message || "Erro ao atualizar perfil",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Editar Meu Perfil</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="personal">Dados Pessoais</TabsTrigger>
            <TabsTrigger value="contact">Contato</TabsTrigger>
          </TabsList>

          <TabsContent value="personal" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Nome</Label>
                <Input 
                  id="name"
                  placeholder="Nome completo" 
                  value={name} 
                  onChange={e => setName(e.target.value)} 
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="nickname">Apelido</Label>
                <Input 
                  id="nickname"
                  placeholder="Apelido" 
                  value={nickname} 
                  onChange={e => setNickname(e.target.value)} 
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="height">Altura (cm)</Label>
                <Input 
                  id="height"
                  type="number" 
                  placeholder="Altura em cm" 
                  value={height} 
                  onChange={e => setHeight(e.target.value)} 
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="weight">Peso (kg)</Label>
                <Input 
                  id="weight"
                  type="number" 
                  placeholder="Peso em kg" 
                  value={weight} 
                  onChange={e => setWeight(e.target.value)} 
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="fatherName">Nome do Pai</Label>
                <Input 
                  id="fatherName"
                  placeholder="Nome do pai" 
                  value={fatherName} 
                  onChange={e => setFatherName(e.target.value)} 
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="motherName">Nome da Mãe</Label>
                <Input 
                  id="motherName"
                  placeholder="Nome da mãe" 
                  value={motherName} 
                  onChange={e => setMotherName(e.target.value)} 
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="contact" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phone">Telefone</Label>
                <Input 
                  id="phone"
                  placeholder="Telefone" 
                  value={phone} 
                  onChange={e => setPhone(e.target.value)} 
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input 
                  id="email"
                  type="email"
                  placeholder="Email" 
                  value={email} 
                  onChange={e => setEmail(e.target.value)} 
                />
              </div>

              <div className="space-y-2 col-span-2">
                <Label htmlFor="address">Endereço</Label>
                <Input 
                  id="address"
                  placeholder="Endereço completo" 
                  value={address} 
                  onChange={e => setAddress(e.target.value)} 
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="zipCode">CEP</Label>
                <Input 
                  id="zipCode"
                  placeholder="CEP" 
                  value={zipCode} 
                  onChange={e => setZipCode(e.target.value)} 
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="city">Cidade</Label>
                <Input 
                  id="city"
                  placeholder="Cidade" 
                  value={city} 
                  onChange={e => setCity(e.target.value)} 
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="state">Estado</Label>
                <Input 
                  id="state"
                  placeholder="Estado" 
                  value={state} 
                  onChange={e => setState(e.target.value)} 
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {error && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4 rounded">
            <div className="flex">
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button 
            onClick={handleSubmit} 
            disabled={loading}
            className="bg-team-blue hover:bg-blue-700"
          >
            {loading ? "Salvando..." : "Salvar"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
