import { useState } from "react";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>er, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>Footer 
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/use-toast";
import { createSupplierOrder } from "@/api/api";
import { useUser } from "@/context/UserContext";
import { Checkbox } from "@/components/ui/checkbox";
import { Loader2, Calendar } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { cn } from "@/lib/utils";

interface NovoSupplierOrderDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  supplierId: number;
  supplierName: string;
  onSuccess?: () => void;
}

export function NovoSupplierOrderDialog({ 
  open, 
  onOpenChange, 
  clubId,
  supplierId,
  supplierName,
  onSuccess 
}: NovoSupplierOrderDialogProps) {
  const { user } = useUser();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Campos do pedido
  const [description, setDescription] = useState("");
  const [purchaseDate, setPurchaseDate] = useState<Date>(new Date());
  const [amount, setAmount] = useState("");
  const [createAccount, setCreateAccount] = useState(true);
  const [dueDate, setDueDate] = useState<Date>(new Date(new Date().setMonth(new Date().getMonth() + 1)));

  // Função para limpar o formulário
  const resetForm = () => {
    setDescription("");
    setPurchaseDate(new Date());
    setAmount("");
    setCreateAccount(true);
    setDueDate(new Date(new Date().setMonth(new Date().getMonth() + 1)));
    setError(null);
  };

  // Função para salvar o pedido
  const handleSave = async () => {
    try {
      setLoading(true);
      setError(null);

      // Validar campos obrigatórios
      if (!description) {
        throw new Error("A descrição do pedido é obrigatória");
      }

      if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
        throw new Error("O valor do pedido deve ser um número positivo");
      }

      // Converter valor para número
      const numericAmount = parseFloat(amount);

      // Criar o pedido
      await createSupplierOrder(
        clubId,
        user?.id || "",
        {
          supplier_id: supplierId,
          description,
          purchase_date: format(purchaseDate, "yyyy-MM-dd"),
          amount: numericAmount,
        },
        createAccount,
        format(dueDate, "yyyy-MM-dd")
      );

      toast({
        title: "Sucesso",
        description: "Pedido adicionado com sucesso",
      });

      // Fechar o diálogo e limpar o formulário
      onOpenChange(false);
      resetForm();

      // Chamar callback de sucesso
      if (onSuccess) {
        onSuccess();
      }
    } catch (err) {
      console.error("Erro ao criar pedido:", err);
      setError(err instanceof Error ? err.message : "Erro ao criar pedido");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={(isOpen) => {
      onOpenChange(isOpen);
      if (!isOpen) resetForm();
    }}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Adicionar Pedido</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {error && (
            <div className="text-sm font-medium text-red-500 dark:text-red-400">
              {error}
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="description">Descrição do Pedido</Label>
            <Input
              id="description"
              placeholder="Ex: Material esportivo"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="purchaseDate">Data da Compra</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !purchaseDate && "text-muted-foreground"
                  )}
                >
                  <Calendar className="mr-2 h-4 w-4" />
                  {purchaseDate ? (
                    format(purchaseDate, "dd/MM/yyyy", { locale: ptBR })
                  ) : (
                    <span>Selecione uma data</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <CalendarComponent
                  mode="single"
                  selected={purchaseDate}
                  onSelect={(date) => date && setPurchaseDate(date)}
                  initialFocus
                  locale={ptBR}
                />
              </PopoverContent>
            </Popover>
          </div>

          <div className="space-y-2">
            <Label htmlFor="amount">Valor do Pedido (R$)</Label>
            <Input
              id="amount"
              placeholder="Ex: 1500.00"
              value={amount}
              onChange={(e) => {
                // Permitir apenas números e ponto decimal
                const value = e.target.value.replace(/[^0-9.]/g, "");
                setAmount(value);
              }}
            />
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="createAccount"
              checked={createAccount}
              onCheckedChange={(checked) => setCreateAccount(checked as boolean)}
            />
            <Label
              htmlFor="createAccount"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Criar conta a pagar automaticamente
            </Label>
          </div>

          {createAccount && (
            <div className="space-y-2">
              <Label htmlFor="dueDate">Data de Vencimento</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !dueDate && "text-muted-foreground"
                    )}
                  >
                    <Calendar className="mr-2 h-4 w-4" />
                    {dueDate ? (
                      format(dueDate, "dd/MM/yyyy", { locale: ptBR })
                    ) : (
                      <span>Selecione uma data</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <CalendarComponent
                    mode="single"
                    selected={dueDate}
                    onSelect={(date) => date && setDueDate(date)}
                    initialFocus
                    locale={ptBR}
                  />
                </PopoverContent>
              </Popover>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Salvar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
