-- Create financial_accounts table for managing accounts payable and receivable
CREATE TABLE IF NOT EXISTS financial_accounts (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  description TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('a_pagar', 'a_receber')),
  supplier_client TEXT NOT NULL,
  creation_date DATE NOT NULL DEFAULT CURRENT_DATE,
  due_date DATE NOT NULL,
  amount NUMERIC NOT NULL CHECK (amount > 0),
  status TEXT NOT NULL DEFAULT 'pendente' CHECK (status IN ('pendente', 'pago', 'recebido')),
  receipt_url TEXT,
  category TEXT NOT NULL,
  cost_center TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_financial_accounts_club_id ON financial_accounts(club_id);
CREATE INDEX IF NOT EXISTS idx_financial_accounts_due_date ON financial_accounts(due_date);
CREATE INDEX IF NOT EXISTS idx_financial_accounts_status ON financial_accounts(status);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_financial_accounts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update the updated_at timestamp
CREATE TRIGGER update_financial_accounts_updated_at
BEFORE UPDATE ON financial_accounts
FOR EACH ROW
EXECUTE FUNCTION update_financial_accounts_updated_at();

-- Add RLS policies
ALTER TABLE financial_accounts ENABLE ROW LEVEL SECURITY;

-- Policy for select: users can only see accounts from their club
CREATE POLICY select_financial_accounts ON financial_accounts
  FOR SELECT
  USING (
    club_id IN (
      SELECT club_id FROM club_members WHERE user_id = auth.uid()
    )
  );

-- Policy for insert: users can only insert accounts for their club
CREATE POLICY insert_financial_accounts ON financial_accounts
  FOR INSERT
  WITH CHECK (
    club_id IN (
      SELECT club_id FROM club_members WHERE user_id = auth.uid()
    )
  );

-- Policy for update: users can only update accounts from their club
CREATE POLICY update_financial_accounts ON financial_accounts
  FOR UPDATE
  USING (
    club_id IN (
      SELECT club_id FROM club_members WHERE user_id = auth.uid()
    )
  );

-- Policy for delete: users can only delete accounts from their club
CREATE POLICY delete_financial_accounts ON financial_accounts
  FOR DELETE
  USING (
    club_id IN (
      SELECT club_id FROM club_members WHERE user_id = auth.uid()
    )
  );
