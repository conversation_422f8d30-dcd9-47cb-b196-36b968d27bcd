import { supabase } from "@/integrations/supabase/client";
import { deleteTraining } from "./trainings";

/**
 * Checks and deletes trainings that ended more than 24 hours ago
 * @param clubId The club ID
 * @returns Number of deleted trainings
 */
export async function cleanupExpiredTrainings(clubId: number): Promise<number> {
  try {
    // Get current date and time
    const now = new Date();
    
    // Get all trainings
    const { data, error } = await supabase
      .from("trainings")
      .select("id, date, notes")
      .eq("club_id", clubId);
    
    if (error) {
      console.error("Error fetching trainings for cleanup:", error);
      throw new Error(`Error fetching trainings for cleanup: ${error.message}`);
    }
    
    if (!data || data.length === 0) {
      return 0;
    }
    
    // Filter trainings that ended more than 24 hours ago
    const trainingsToDelete = data.filter(training => {
      try {
        // Parse training date
        const trainingDate = new Date(training.date);
        
        // Extract end time from notes
        const notesParts = training.notes?.split('|') || Array(9).fill('');
        const timeStr = notesParts[2] || '10:00';
        const timeComponents = timeStr.split('-');
        const endTimeStr = timeComponents.length > 1 ? timeComponents[1].trim() : '12:00';
        
        // Create a date object for the end time
        const [endHours, endMinutes] = endTimeStr.split(':').map(Number);
        const endDate = new Date(trainingDate);
        endDate.setHours(endHours, endMinutes, 0, 0);
        
        // Add 24 hours to the end time
        const expirationDate = new Date(endDate);
        expirationDate.setHours(expirationDate.getHours() + 24);
        
        // Check if the training has expired
        return now > expirationDate;
      } catch (err) {
        console.error("Error processing training for cleanup:", err);
        return false;
      }
    });
    
    // Delete expired trainings
    let deletedCount = 0;
    for (const training of trainingsToDelete) {
      try {
        await deleteTraining(clubId, training.id);
        deletedCount++;
      } catch (err) {
        console.error(`Error deleting expired training ${training.id}:`, err);
      }
    }
    
    return deletedCount;
  } catch (error) {
    console.error("Error in cleanupExpiredTrainings:", error);
    throw error;
  }
}
