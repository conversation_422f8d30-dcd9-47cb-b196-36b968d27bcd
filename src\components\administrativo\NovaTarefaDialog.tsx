import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { useAdministrativeTasksStore } from "@/store/useAdministrativeTasksStore";
import { useUser } from "@/context/UserContext";
import { useEffect } from "react";
import { getCollaborators, Collaborator } from "@/api/api";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon, Loader2 } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";

type TaskType = Database['public']['Tables']['task_types']['Row'];

interface NovaTarefaDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  initialCollaboratorId?: number;
}

export function NovaTarefaDialog({
  open,
  onOpenChange,
  clubId,
  initialCollaboratorId
}: NovaTarefaDialogProps) {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [collaboratorId, setCollaboratorId] = useState<number | undefined>(initialCollaboratorId);
  const [dueDate, setDueDate] = useState<Date | undefined>(undefined);
  const [status, setStatus] = useState<string>('a_fazer');
  const [error, setError] = useState("");
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [loadingCollaborators, setLoadingCollaborators] = useState(false);
  const [taskTypes, setTaskTypes] = useState<TaskType[]>([]);
  const [loadingTaskTypes, setLoadingTaskTypes] = useState(false);

  const { addTask, loading } = useAdministrativeTasksStore();
  const { user } = useUser();

  useEffect(() => {
    if (open) {
      fetchCollaborators();
      fetchTaskTypes();
    }
  }, [open, clubId]);

  const fetchTaskTypes = async () => {
    setLoadingTaskTypes(true);
    try {
      const { data, error } = await supabase
        .from("task_types")
        .select("*")
        .eq("club_id", clubId)
        .order("position", { ascending: true });

      if (error) {
        throw new Error(`Erro ao buscar tipos de tarefas: ${error.message}`);
      }

      // If no task types exist, use default ones
      if (data && data.length === 0) {
        setTaskTypes([
          { id: 0, club_id: clubId, name: 'a_fazer', position: 1, created_at: null, updated_at: null, color: null },
          { id: 1, club_id: clubId, name: 'em_andamento', position: 2, created_at: null, updated_at: null, color: null },
          { id: 2, club_id: clubId, name: 'concluido', position: 3, created_at: null, updated_at: null, color: null }
        ]);
      } else {
        setTaskTypes(data || []);
      }
    } catch (err: any) {
      console.error("Erro ao buscar tipos de tarefas:", err);
      // Use default task types if there's an error
      setTaskTypes([
        { id: 0, club_id: clubId, name: 'a_fazer', position: 1, created_at: null, updated_at: null, color: null },
        { id: 1, club_id: clubId, name: 'em_andamento', position: 2, created_at: null, updated_at: null, color: null },
        { id: 2, club_id: clubId, name: 'concluido', position: 3, created_at: null, updated_at: null, color: null }
      ]);
    } finally {
      setLoadingTaskTypes(false);
    }
  };

  const fetchCollaborators = async () => {
    setLoadingCollaborators(true);
    try {
      const clubCollaborators = await getCollaborators(clubId);
      // Sort collaborators alphabetically by name
      clubCollaborators.sort((a, b) => a.full_name.localeCompare(b.full_name));
      // Filter out inactive collaborators
      const activeCollaborators = clubCollaborators.filter(
        collaborator => collaborator.status !== 'inactive'
      );
      setCollaborators(activeCollaborators);
    } catch (error) {
      console.error("Erro ao buscar colaboradores:", error);
    } finally {
      setLoadingCollaborators(false);
    }
  };

  const handleSave = async () => {
    if (!title.trim()) {
      setError("O título é obrigatório.");
      return;
    }

    if (!user) {
      setError("Usuário não autenticado.");
      return;
    }

    if (!collaboratorId) {
      setError("É necessário selecionar um colaborador.");
      return;
    }

    setError("");

    try {
      await addTask(clubId, {
        title,
        description,
        collaborator_id: collaboratorId,
        due_date: dueDate ? dueDate.toISOString() : undefined,
        status,
        created_by: user.id,
        club_id: clubId
      });

      toast({
        title: "Tarefa criada",
        description: "A tarefa foi criada com sucesso.",
      });

      resetForm();
      onOpenChange(false);
    } catch (err) {
      setError("Erro ao criar tarefa.");
      toast({
        title: "Erro ao criar tarefa",
        description: "Ocorreu um erro ao criar a tarefa.",
        variant: "destructive",
      });
    }
  };

  // Helper function to get display name for task status
  const getStatusDisplayName = (statusName: string) => {
    if (statusName === 'a_fazer') return 'A Fazer';
    if (statusName === 'em_andamento') return 'Em Andamento';
    if (statusName === 'concluido') return 'Concluído';

    // For custom status names, capitalize first letter and replace underscores with spaces
    return statusName.charAt(0).toUpperCase() +
           statusName.slice(1).replace(/_/g, ' ');
  };

  const resetForm = () => {
    setTitle("");
    setDescription("");
    setCollaboratorId(undefined);
    setDueDate(undefined);
    // Set status to the first task type or 'a_fazer' if no task types
    setStatus(taskTypes.length > 0 ? taskTypes[0].name : 'a_fazer');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Nova Tarefa</DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="title" className="text-right">
              Título
            </Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="col-span-3"
            />
          </div>

          <div className="grid grid-cols-4 items-start gap-4">
            <Label htmlFor="description" className="text-right pt-2">
              Descrição
            </Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="col-span-3"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="collaborator" className="text-right">
              Colaborador
            </Label>
            <Select
              value={collaboratorId?.toString()}
              onValueChange={(value) => setCollaboratorId(parseInt(value))}
            >
              <SelectTrigger id="collaborator" className="col-span-3">
                <SelectValue placeholder="Selecione o colaborador" />
              </SelectTrigger>
              <SelectContent>
                {loadingCollaborators ? (
                  <SelectItem value="loading" disabled>
                    Carregando colaboradores...
                  </SelectItem>
                ) : (
                  collaborators.map((collaborator) => (
                    <SelectItem key={collaborator.id} value={collaborator.id.toString()}>
                      {collaborator.full_name} - {collaborator.role}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="due-date" className="text-right">
              Data Limite
            </Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="due-date"
                  variant="outline"
                  className={cn(
                    "col-span-3 justify-start text-left font-normal",
                    !dueDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {dueDate ? format(dueDate, "PPP", { locale: ptBR }) : "Selecione uma data"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={dueDate}
                  onSelect={setDueDate}
                  initialFocus
                  locale={ptBR}
                />
              </PopoverContent>
            </Popover>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="status" className="text-right">
              Status
            </Label>
            <Select
              value={status}
              onValueChange={(value) => setStatus(value)}
            >
              <SelectTrigger id="status" className="col-span-3">
                <SelectValue placeholder="Selecione o status" />
              </SelectTrigger>
              <SelectContent>
                {loadingTaskTypes ? (
                  <div className="flex items-center justify-center py-2">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    <span>Carregando...</span>
                  </div>
                ) : taskTypes.length === 0 ? (
                  <>
                    <SelectItem value="a_fazer">A Fazer</SelectItem>
                    <SelectItem value="em_andamento">Em Andamento</SelectItem>
                    <SelectItem value="concluido">Concluído</SelectItem>
                  </>
                ) : (
                  taskTypes.map((type) => (
                    <SelectItem key={type.id} value={type.name}>
                      {getStatusDisplayName(type.name)}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>

          {error && (
            <div className="text-red-500 text-sm col-span-4 text-center">
              {error}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            {loading ? "Salvando..." : "Salvar"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
