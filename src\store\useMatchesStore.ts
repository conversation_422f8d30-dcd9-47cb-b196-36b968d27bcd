import { create } from "zustand";
import type { UpcomingMatch } from "@/api/api";
import { getUpcomingMatches } from "@/api";

interface MatchesStore {
  matches: UpcomingMatch[];
  loading: boolean;
  error: string;
  fetchMatches: (clubId: number, seasonId?: number) => Promise<void>;
}

export const useMatchesStore = create<MatchesStore>((set) => ({
  matches: [],
  loading: false,
  error: "",
  fetchMatches: async (clubId: number, seasonId?: number) => {
    set({ loading: true, error: "" });
    try {
      const matches = await getUpcomingMatches(clubId, seasonId);
      set({ matches, loading: false });
    } catch (err: any) {
      set({ error: err.message || "Erro ao buscar partidas", loading: false });
    }
  },
}));
