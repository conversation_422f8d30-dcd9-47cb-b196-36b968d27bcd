import { supabase } from "@/integrations/supabase/client";
import { withPermission } from "./middleware";

export type AuditLog = {
  id: number;
  club_id: number;
  user_id: string;
  action: string;
  details: Record<string, any>;
  success: boolean;
  created_at: string;
  user_name?: string; // Campo adicional para junção
};

/**
 * Obtém os logs de auditoria de um clube
 * @param clubId ID do clube
 * @param userId ID do usuário que está fazendo a requisição
 * @param filters Filtros opcionais (ação, usuário, data)
 * @param limit Limite de registros a serem retornados
 * @param offset Offset para paginação
 * @returns Lista de logs de auditoria
 */
export async function getAuditLogs(
  clubId: number,
  userId: string,
  filters?: {
    action?: string;
    user_id?: string;
    start_date?: string;
    end_date?: string;
  },
  limit: number = 50,
  offset: number = 0
): Promise<AuditLog[]> {
  return withPermission(
    clubId,
    userId,
    "audit_logs.view",
    async () => {
      let query = supabase
        .from("audit_logs")
        .select(`
          *,
          user_name:users(name)
        `)
        .eq("club_id", clubId)
        .order("created_at", { ascending: false })
        .limit(limit)
        .range(offset, offset + limit - 1);

      // Aplicar filtros, se fornecidos
      if (filters) {
        if (filters.action) {
          query = query.eq("action", filters.action);
        }
        if (filters.user_id) {
          query = query.eq("user_id", filters.user_id);
        }
        if (filters.start_date) {
          query = query.gte("created_at", filters.start_date);
        }
        if (filters.end_date) {
          query = query.lte("created_at", filters.end_date);
        }
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Erro ao buscar logs de auditoria: ${error.message}`);
      }

      // Processar os resultados para extrair o nome do usuário da junção
      return (data || []).map(log => {
        const { users, ...rest } = log as any;
        return {
          ...rest,
          user_name: users?.name || "Usuário Desconhecido"
        } as AuditLog;
      });
    }
  );
}

/**
 * Obtém as ações disponíveis para filtrar logs de auditoria
 * @param clubId ID do clube
 * @param userId ID do usuário que está fazendo a requisição
 * @returns Lista de ações disponíveis
 */
export async function getAuditLogActions(
  clubId: number,
  userId: string
): Promise<string[]> {
  return withPermission(
    clubId,
    userId,
    "audit_logs.view",
    async () => {
      const { data, error } = await supabase
        .from("audit_logs")
        .select("action")
        .eq("club_id", clubId)
        .order("action")
        .distinct();

      if (error) {
        throw new Error(`Erro ao buscar ações de logs de auditoria: ${error.message}`);
      }

      return (data || []).map(item => item.action);
    }
  );
}

/**
 * Obtém os usuários que têm logs de auditoria
 * @param clubId ID do clube
 * @param userId ID do usuário que está fazendo a requisição
 * @returns Lista de usuários com logs
 */
export async function getAuditLogUsers(
  clubId: number,
  userId: string
): Promise<{ id: string; name: string }[]> {
  return withPermission(
    clubId,
    userId,
    "audit_logs.view",
    async () => {
      const { data, error } = await supabase
        .from("audit_logs")
        .select(`
          user_id,
          users:users(id, name)
        `)
        .eq("club_id", clubId)
        .order("user_id")
        .distinct();

      if (error) {
        throw new Error(`Erro ao buscar usuários de logs de auditoria: ${error.message}`);
      }

      // Processar os resultados para extrair o nome do usuário da junção
      return (data || []).map(item => {
        const user = (item as any).users;
        return {
          id: item.user_id,
          name: user?.name || "Usuário Desconhecido"
        };
      });
    }
  );
}
