import { supabase } from "@/integrations/supabase/client";
import {
  sendEmailWithBrevo,
  sendInvitationEmail as sendInvitationWithBrevo,
  sendPasswordResetEmail as sendPasswordResetWithBrevo,
  type EmailData
} from "@/services/brevoEmailService";

/**
 * Envia um email usando o serviço Brevo API
 * @param emailData Dados do email a ser enviado
 * @returns true se o email foi enviado com sucesso
 */
export async function sendEmail(emailData: EmailData): Promise<boolean> {
  return sendEmailWithBrevo(emailData);
}

/**
 * Envia um email de convite para um jogador
 * @param email Email do jogador
 * @param name Nome do jogador
 * @param token Token de convite
 * @param clubName Nome do clube
 * @returns true se o email foi enviado com sucesso
 */
export async function sendPlayerInvitation(
  email: string,
  name: string,
  token: string,
  clubName: string
): Promise<boolean> {
  return sendInvitationWithBrevo(email, name, token, clubName);
}

/**
 * Envia um email de redefinição de senha
 * @param email Email do usuário
 * @param name Nome do usuário
 * @param token Token de redefinição de senha
 * @returns true se o email foi enviado com sucesso
 */
export async function sendPasswordReset(
  email: string,
  name: string,
  token: string
): Promise<boolean> {
  return sendPasswordResetWithBrevo(email, name, token);
}

/**
 * Envia um email de boas-vindas com credenciais de acesso
 * @param email Email do usuário
 * @param name Nome do usuário
 * @param password Senha gerada
 * @param clubName Nome do clube
 * @returns true se o email foi enviado com sucesso
 */
export async function sendWelcomeWithCredentials(
  email: string,
  name: string,
  password: string,
  clubName: string
): Promise<boolean> {
  // Importar a função diretamente para evitar dependência circular
  const { sendWelcomeEmail } = await import('@/services/brevoEmailService');
  return sendWelcomeEmail(email, name, password, clubName);
}
