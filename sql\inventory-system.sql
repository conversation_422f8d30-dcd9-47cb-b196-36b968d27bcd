-- Criação do sistema de estoque (inventory)

-- Tabela de produtos do estoque
CREATE TABLE IF NOT EXISTS inventory_products (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  name TEXT NOT NULL,
  quantity INTEGER NOT NULL DEFAULT 0,
  registration_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  location TEXT,
  department TEXT NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de transações do estoque (entradas e saídas)
CREATE TABLE IF NOT EXISTS inventory_transactions (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  product_id INTEGER REFERENCES inventory_products(id) NOT NULL,
  transaction_type TEXT NOT NULL, -- 'entrada' ou 'saida'
  quantity INTEGER NOT NULL,
  previous_quantity INTEGER NOT NULL,
  new_quantity INTEGER NOT NULL,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de configurações de notificações de estoque
CREATE TABLE IF NOT EXISTS inventory_notifications (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  threshold INTEGER NOT NULL DEFAULT 5,
  notify_users JSONB DEFAULT '[]', -- Array de IDs de usuários para notificar
  email_notifications BOOLEAN DEFAULT TRUE,
  system_notifications BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para melhorar a performance
CREATE INDEX IF NOT EXISTS idx_inventory_products_club_id ON inventory_products(club_id);
CREATE INDEX IF NOT EXISTS idx_inventory_products_department ON inventory_products(department);
CREATE INDEX IF NOT EXISTS idx_inventory_products_quantity ON inventory_products(quantity);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_club_id ON inventory_transactions(club_id);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_product_id ON inventory_transactions(product_id);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_created_at ON inventory_transactions(created_at);

-- Habilitar Row Level Security (RLS)
ALTER TABLE inventory_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_notifications ENABLE ROW LEVEL SECURITY;

-- Políticas de segurança para inventory_products
CREATE POLICY inventory_products_isolation_policy ON inventory_products
  FOR ALL
  USING (club_id IN (
    SELECT club_id FROM club_members WHERE user_id = auth.uid()
  ));

-- Políticas de segurança para inventory_transactions
CREATE POLICY inventory_transactions_isolation_policy ON inventory_transactions
  FOR ALL
  USING (club_id IN (
    SELECT club_id FROM club_members WHERE user_id = auth.uid()
  ));

-- Políticas de segurança para inventory_notifications
CREATE POLICY inventory_notifications_isolation_policy ON inventory_notifications
  FOR ALL
  USING (club_id IN (
    SELECT club_id FROM club_members WHERE user_id = auth.uid()
  ));

-- Adicionar permissões relacionadas ao estoque
INSERT INTO permissions (name, description, category)
VALUES 
  ('inventory.view', 'Ver estoque', 'inventory'),
  ('inventory.create', 'Adicionar produtos ao estoque', 'inventory'),
  ('inventory.edit', 'Editar produtos do estoque', 'inventory'),
  ('inventory.delete', 'Remover produtos do estoque', 'inventory'),
  ('inventory.reports', 'Gerar relatórios de estoque', 'inventory');

-- Função para atualizar a quantidade de um produto e registrar a transação
CREATE OR REPLACE FUNCTION update_inventory_product_quantity(
  p_club_id INTEGER,
  p_product_id INTEGER,
  p_quantity INTEGER,
  p_transaction_type TEXT,
  p_user_id UUID,
  p_notes TEXT DEFAULT NULL
) RETURNS INTEGER AS $$
DECLARE
  v_previous_quantity INTEGER;
  v_new_quantity INTEGER;
BEGIN
  -- Obter a quantidade atual
  SELECT quantity INTO v_previous_quantity
  FROM inventory_products
  WHERE id = p_product_id AND club_id = p_club_id;
  
  -- Calcular nova quantidade
  IF p_transaction_type = 'entrada' THEN
    v_new_quantity := v_previous_quantity + p_quantity;
  ELSIF p_transaction_type = 'saida' THEN
    v_new_quantity := v_previous_quantity - p_quantity;
    -- Verificar se há quantidade suficiente
    IF v_new_quantity < 0 THEN
      RAISE EXCEPTION 'Quantidade insuficiente em estoque';
    END IF;
  ELSE
    RAISE EXCEPTION 'Tipo de transação inválido. Use "entrada" ou "saida"';
  END IF;
  
  -- Atualizar a quantidade do produto
  UPDATE inventory_products
  SET quantity = v_new_quantity,
      updated_at = NOW()
  WHERE id = p_product_id AND club_id = p_club_id;
  
  -- Registrar a transação
  INSERT INTO inventory_transactions (
    club_id,
    product_id,
    transaction_type,
    quantity,
    previous_quantity,
    new_quantity,
    user_id,
    notes
  ) VALUES (
    p_club_id,
    p_product_id,
    p_transaction_type,
    p_quantity,
    v_previous_quantity,
    v_new_quantity,
    p_user_id,
    p_notes
  );
  
  RETURN v_new_quantity;
END;
$$ LANGUAGE plpgsql;

-- Trigger para verificar estoque baixo e criar notificações
CREATE OR REPLACE FUNCTION check_low_stock() RETURNS TRIGGER AS $$
DECLARE
  v_threshold INTEGER;
  v_notify_users JSONB;
  v_email_notifications BOOLEAN;
  v_system_notifications BOOLEAN;
BEGIN
  -- Verificar se a quantidade está abaixo do limite
  IF NEW.quantity <= 5 THEN
    -- Buscar configurações de notificação
    SELECT 
      threshold, 
      notify_users, 
      email_notifications, 
      system_notifications
    INTO 
      v_threshold, 
      v_notify_users, 
      v_email_notifications, 
      v_system_notifications
    FROM inventory_notifications
    WHERE club_id = NEW.club_id;
    
    -- Se não houver configuração, usar valores padrão
    IF v_threshold IS NULL THEN
      v_threshold := 5;
      v_notify_users := '[]';
      v_email_notifications := TRUE;
      v_system_notifications := TRUE;
    END IF;
    
    -- Se a quantidade estiver abaixo do limite, criar notificação
    IF NEW.quantity <= v_threshold THEN
      -- Criar notificação no sistema (implementação simplificada)
      IF v_system_notifications THEN
        INSERT INTO notifications (
          club_id,
          user_id,
          title,
          message,
          type,
          reference_id,
          reference_type
        )
        SELECT 
          NEW.club_id,
          user_id,
          'Estoque Baixo',
          'O produto "' || NEW.name || '" está com estoque baixo (' || NEW.quantity || ' unidades)',
          'inventory_low_stock',
          NEW.id::TEXT,
          'inventory_product'
        FROM unnest(ARRAY(SELECT jsonb_array_elements_text(v_notify_users))) AS user_id;
      END IF;
      
      -- Aqui seria implementado o envio de e-mails (não implementado neste script)
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Adicionar o trigger à tabela inventory_products
CREATE TRIGGER inventory_products_low_stock_trigger
AFTER INSERT OR UPDATE OF quantity ON inventory_products
FOR EACH ROW
EXECUTE FUNCTION check_low_stock();
