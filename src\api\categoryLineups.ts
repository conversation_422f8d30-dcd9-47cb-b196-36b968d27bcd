import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";

// Tipos
export type CategoryLineup = Database["public"]["Tables"]["category_lineups"]["Row"];

/**
 * Busca a escalação de uma categoria específica
 * @param clubId ID do clube
 * @param categoryId ID da categoria
 * @returns Objeto com a escalação da categoria
 */
export async function getCategoryLineup(clubId: number, categoryId: number): Promise<CategoryLineup | null> {
  const { data, error } = await supabase
    .from("category_lineups")
    .select("*")
    .eq("club_id", clubId)
    .eq("category_id", categoryId)
    .single();

  if (error) {
    if (error.code === "PGRST116") {
      // Não encontrou - retorna null em vez de erro
      return null;
    }
    console.error(`Erro ao buscar escalação da categoria ${categoryId}:`, error);
    throw new Error(`Erro ao buscar escalação da categoria: ${error.message}`);
  }

  return data;
}

/**
 * Salva ou atualiza a escalação de uma categoria
 * @param clubId ID do clube
 * @param categoryId ID da categoria
 * @param lineup Objeto com a escalação (posições -> IDs de jogadores)
 * @param formation Formação tática (ex: "4-4-2")
 * @returns Objeto com a escalação atualizada
 */
export async function saveCategoryLineup(
  clubId: number,
  categoryId: number,
  lineup: Record<string, string | null>,
  formation: string
): Promise<CategoryLineup> {
  // Verificar se já existe uma escalação para esta categoria
  const { data: existing } = await supabase
    .from("category_lineups")
    .select("id")
    .eq("club_id", clubId)
    .eq("category_id", categoryId);

  let result;

  if (existing && existing.length > 0) {
    // Atualizar escalação existente
    const { data, error } = await supabase
      .from("category_lineups")
      .update({
        lineup,
        formation,
        updated_at: new Date().toISOString()
      } as any)
      .eq("club_id", clubId)
      .eq("category_id", categoryId)
      .select()
      .single();

    if (error) {
      console.error(`Erro ao atualizar escalação da categoria ${categoryId}:`, error);
      throw new Error(`Erro ao atualizar escalação da categoria: ${error.message}`);
    }

    result = data;
  } else {
    // Criar nova escalação
    const { data, error } = await supabase
      .from("category_lineups")
      .insert({
        club_id: clubId,
        category_id: categoryId,
        lineup,
        formation
      })
      .select()
      .single();

    if (error) {
      console.error(`Erro ao criar escalação da categoria ${categoryId}:`, error);
      throw new Error(`Erro ao criar escalação da categoria: ${error.message}`);
    }

    result = data;
  }

  return result;
}

/**
 * Deleta a escalação de uma categoria
 * @param clubId ID do clube
 * @param categoryId ID da categoria
 * @returns true se a operação foi bem-sucedida
 */
export async function deleteCategoryLineup(clubId: number, categoryId: number): Promise<boolean> {
  const { error } = await supabase
    .from("category_lineups")
    .delete()
    .eq("club_id", clubId)
    .eq("category_id", categoryId);

  if (error) {
    console.error(`Erro ao excluir escalação da categoria ${categoryId}:`, error);
    throw new Error(`Erro ao excluir escalação da categoria: ${error.message}`);
  }

  return true;
}

