import { create } from "zustand";
import {
  MedicalTreatmentEvolution,
  getTreatmentEvolutions,
  createTreatmentEvolution,
  updateTreatmentEvolution,
  deleteTreatmentEvolution,
  addTreatmentSignature,
  TreatmentStatus
} from "@/api/api";

interface TreatmentEvolutionsState {
  evolutions: MedicalTreatmentEvolution[];
  loading: boolean;
  error: string | null;
  fetchEvolutions: (clubId: number, recordId: number) => Promise<void>;
  addEvolution: (
    clubId: number,
    userId: string,
    evolution: Omit<MedicalTreatmentEvolution, "id" | "club_id" | "created_at" | "signature_url">
  ) => Promise<void>;
  updateEvolution: (
    clubId: number,
    userId: string,
    id: number,
    evolution: Partial<Omit<MedicalTreatmentEvolution, "id" | "club_id" | "created_at">>
  ) => Promise<void>;
  deleteEvolution: (clubId: number, userId: string, id: number) => Promise<void>;
  addSignature: (clubId: number, userId: string, evolutionId: number, signatureFile: File) => Promise<void>;
}

export const useTreatmentEvolutionsStore = create<TreatmentEvolutionsState>((set) => ({
  evolutions: [],
  loading: false,
  error: null,

  fetchEvolutions: async (clubId, recordId) => {
    set({ loading: true, error: null });
    try {
      const evolutions = await getTreatmentEvolutions(clubId, recordId);
      set({ evolutions, loading: false });
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao buscar evoluções de tratamento",
        loading: false,
      });
    }
  },

  addEvolution: async (clubId, userId, evolution) => {
    set({ loading: true, error: null });
    try {
      const newEvolution = await createTreatmentEvolution(clubId, userId, evolution);
      set((state) => ({
        evolutions: [newEvolution, ...state.evolutions],
        loading: false,
      }));
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao adicionar evolução de tratamento",
        loading: false,
      });
    }
  },

  updateEvolution: async (clubId, userId, id, evolution) => {
    set({ loading: true, error: null });
    try {
      const updated = await updateTreatmentEvolution(clubId, userId, id, evolution);
      set((state) => ({
        evolutions: state.evolutions.map((e) => (e.id === id ? updated : e)),
        loading: false,
      }));
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao atualizar evolução de tratamento",
        loading: false,
      });
    }
  },

  deleteEvolution: async (clubId, userId, id) => {
    set({ loading: true, error: null });
    try {
      await deleteTreatmentEvolution(clubId, userId, id);
      set((state) => ({
        evolutions: state.evolutions.filter((e) => e.id !== id),
        loading: false,
      }));
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao excluir evolução de tratamento",
        loading: false,
      });
    }
  },

  addSignature: async (clubId, userId, evolutionId, signatureFile) => {
    set({ loading: true, error: null });
    try {
      const signatureUrl = await addTreatmentSignature(clubId, userId, evolutionId, signatureFile);
      set((state) => ({
        evolutions: state.evolutions.map((e) =>
          e.id === evolutionId ? { ...e, signature_url: signatureUrl } : e
        ),
        loading: false,
      }));
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao adicionar assinatura",
        loading: false,
      });
    }
  },
}));
