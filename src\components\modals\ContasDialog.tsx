import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { FinancialAccount } from "@/api/api";
import { useFinancialAccountsStore } from "@/store/useFinancialAccountsStore";
import { toast } from "@/hooks/use-toast";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

interface ContasDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  account?: FinancialAccount | null;
}

export function ContasDialog({ open, onOpenChange, clubId, account }: ContasDialogProps) {
  const [description, setDescription] = useState("");
  const [type, setType] = useState<"a_pagar" | "a_receber">("a_pagar");
  const [supplierClient, setSupplierClient] = useState("");
  const [creationDate, setCreationDate] = useState<Date>(new Date());
  const [dueDate, setDueDate] = useState<Date>(new Date());
  const [amount, setAmount] = useState("");
  const [status, setStatus] = useState<"pendente" | "pago" | "recebido">("pendente");
  const [category, setCategory] = useState("geral");
  const [costCenter, setCostCenter] = useState("");
  const [notes, setNotes] = useState("");
  const { addAccount, updateAccount, loading } = useFinancialAccountsStore();

  useEffect(() => {
    if (account) {
      setDescription(account.description);
      setType(account.type);
      setSupplierClient(account.supplier_client);
      setCreationDate(new Date(account.creation_date));
      setDueDate(new Date(account.due_date));
      setAmount(account.amount.toString());
      setStatus(account.status);
      setCategory(account.category);
      setCostCenter(account.cost_center || "");
      setNotes(account.notes || "");
    } else {
      resetForm();
    }
  }, [account, open]);

  const resetForm = () => {
    setDescription("");
    setType("a_pagar");
    setSupplierClient("");
    setCreationDate(new Date());
    setDueDate(new Date());
    setAmount("");
    setStatus("pendente");
    setCategory("geral");
    setCostCenter("");
    setNotes("");
  };

  const handleSave = async () => {
    if (!description || !supplierClient || !amount) {
      toast({
        title: "Campos obrigatórios",
        description: "Preencha todos os campos obrigatórios.",
        variant: "destructive",
      });
      return;
    }

    // Converte o valor para número
    const numericAmount = parseFloat(amount);

    if (isNaN(numericAmount) || numericAmount <= 0) {
      toast({
        title: "Valor inválido",
        description: "O valor deve ser um número positivo.",
        variant: "destructive",
      });
      return;
    }

    try {
      const accountData = {
        description,
        type,
        supplier_client: supplierClient,
        creation_date: format(creationDate, "yyyy-MM-dd"),
        due_date: format(dueDate, "yyyy-MM-dd"),
        amount: numericAmount,
        status,
        category,
        cost_center: costCenter || undefined,
        notes: notes || undefined,
      };

      if (account) {
        await updateAccount(clubId, account.id, accountData);
        toast({
          title: "Conta atualizada",
          description: "A conta foi atualizada com sucesso.",
        });
      } else {
        await addAccount(clubId, accountData);
        toast({
          title: "Conta adicionada",
          description: "A conta foi adicionada com sucesso.",
        });
      }

      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao salvar conta:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao salvar a conta.",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{account ? "Editar Conta" : "Nova Conta"}</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="grid grid-cols-1 gap-4">
            <div>
              <Label htmlFor="description">Descrição *</Label>
              <Input
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Ex: Pagamento material esportivo"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="type">Tipo *</Label>
                <Select value={type} onValueChange={(value) => setType(value as "a_pagar" | "a_receber")}>
                  <SelectTrigger id="type">
                    <SelectValue placeholder="Selecione o tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="a_pagar">A Pagar</SelectItem>
                    <SelectItem value="a_receber">A Receber</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="supplier_client">Fornecedor/Cliente *</Label>
                <Input
                  id="supplier_client"
                  value={supplierClient}
                  onChange={(e) => setSupplierClient(e.target.value)}
                  placeholder="Nome do fornecedor ou cliente"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="creation_date">Data de Lançamento</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !creationDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {creationDate ? format(creationDate, "PPP", { locale: ptBR }) : <span>Selecione a data</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={creationDate}
                      onSelect={(date) => setCreationDate(date || new Date())}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div>
                <Label htmlFor="due_date">Data de Vencimento *</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !dueDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dueDate ? format(dueDate, "PPP", { locale: ptBR }) : <span>Selecione a data</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={dueDate}
                      onSelect={(date) => setDueDate(date || new Date())}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="amount">Valor *</Label>
                <Input
                  id="amount"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  placeholder="0,00"
                  type="number"
                  step="0.01"
                  min="0.01"
                />
              </div>

              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={status} onValueChange={(value) => setStatus(value as "pendente" | "pago" | "recebido")}>
                  <SelectTrigger id="status">
                    <SelectValue placeholder="Selecione o status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pendente">Pendente</SelectItem>
                    <SelectItem value="pago">Pago</SelectItem>
                    <SelectItem value="recebido">Recebido</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="category">Categoria *</Label>
                <Select value={category} onValueChange={setCategory}>
                  <SelectTrigger id="category">
                    <SelectValue placeholder="Selecione a categoria" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="geral">Geral</SelectItem>
                    <SelectItem value="salários">Salários</SelectItem>
                    <SelectItem value="materiais">Materiais</SelectItem>
                    <SelectItem value="transporte">Transporte</SelectItem>
                    <SelectItem value="patrocínio">Patrocínio</SelectItem>
                    <SelectItem value="alimentação">Alimentação</SelectItem>
                    <SelectItem value="hospedagem">Hospedagem</SelectItem>
                    <SelectItem value="saúde">Saúde</SelectItem>
                    <SelectItem value="impostos">Impostos</SelectItem>
                    <SelectItem value="outros">Outros</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="cost_center">Centro de Custo</Label>
                <Input
                  id="cost_center"
                  value={costCenter}
                  onChange={(e) => setCostCenter(e.target.value)}
                  placeholder="Ex: Sub-15, Sub-17, etc."
                />
              </div>
            </div>

            <div>
              <Label htmlFor="notes">Observações</Label>
              <Textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Observações adicionais"
                rows={3}
              />
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            {account ? "Atualizar" : "Salvar"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
