-- Migração para implementar o sistema de permissões e logs de auditoria

-- Adicionar coluna de permissões à tabela user_departments
ALTER TABLE user_departments ADD COLUMN IF NOT EXISTS permissions JSONB DEFAULT '{}'::jsonb;

-- Adicionar coluna de permissões à tabela departments
ALTER TABLE departments ADD COLUMN IF NOT EXISTS permissions JSONB DEFAULT '{}'::jsonb;

-- <PERSON><PERSON><PERSON> ta<PERSON><PERSON> de logs de auditoria
CREATE TABLE IF NOT EXISTS audit_logs (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL REFERENCES clubs(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  action VARCHAR(255) NOT NULL,
  details JSONB DEFAULT '{}'::jsonb,
  success BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Índices para melhorar a performance das consultas
  CONSTRAINT audit_logs_club_user_idx UNIQUE (id, club_id, user_id)
);

-- <PERSON><PERSON><PERSON> índices para melhorar a performance das consultas
CREATE INDEX IF NOT EXISTS audit_logs_club_id_idx ON audit_logs(club_id);
CREATE INDEX IF NOT EXISTS audit_logs_user_id_idx ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS audit_logs_action_idx ON audit_logs(action);
CREATE INDEX IF NOT EXISTS audit_logs_created_at_idx ON audit_logs(created_at);

-- Adicionar políticas de segurança RLS (Row Level Security)

-- Habilitar RLS na tabela audit_logs
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Política para permitir que usuários vejam apenas logs do seu clube
CREATE POLICY audit_logs_club_policy ON audit_logs
  FOR SELECT
  USING (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'ativo'
    )
  );

-- Política para permitir que usuários criem logs apenas para seu clube
CREATE POLICY audit_logs_insert_policy ON audit_logs
  FOR INSERT
  WITH CHECK (
    club_id IN (
      SELECT club_id FROM club_members 
      WHERE user_id = auth.uid() AND status = 'ativo'
    )
  );

-- Conceder permissões ao papel autenticado
GRANT SELECT, INSERT ON audit_logs TO authenticated;
GRANT USAGE ON SEQUENCE audit_logs_id_seq TO authenticated;

-- Atualizar as permissões padrão para os papéis existentes
UPDATE club_members
SET permissions = permissions || '{"audit_logs.view": true}'::jsonb
WHERE role IN ('president', 'admin');

-- Adicionar permissão de auditoria para gerentes
UPDATE club_members
SET permissions = permissions || '{"audit_logs.view": true}'::jsonb
WHERE role = 'manager';

-- Comentários para documentação
COMMENT ON TABLE audit_logs IS 'Registros de auditoria para ações realizadas no sistema';
COMMENT ON COLUMN audit_logs.club_id IS 'ID do clube ao qual o log pertence';
COMMENT ON COLUMN audit_logs.user_id IS 'ID do usuário que realizou a ação';
COMMENT ON COLUMN audit_logs.action IS 'Ação realizada (ex: player.create, document.verify)';
COMMENT ON COLUMN audit_logs.details IS 'Detalhes adicionais sobre a ação em formato JSON';
COMMENT ON COLUMN audit_logs.success IS 'Indica se a ação foi bem-sucedida';
COMMENT ON COLUMN audit_logs.created_at IS 'Data e hora em que a ação foi registrada';
