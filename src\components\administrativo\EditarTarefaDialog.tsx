import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { useAdministrativeTasksStore } from "@/store/useAdministrativeTasksStore";
import { AdministrativeTask, getCollaborators, Collaborator } from "@/api/api";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { cn } from "@/lib/utils";

interface EditarTarefaDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  task: AdministrativeTask | null;
}

export function EditarTarefaDialog({
  open,
  onOpenChange,
  clubId,
  task
}: EditarTarefaDialogProps) {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [collaboratorId, setCollaboratorId] = useState<number | undefined>(undefined);
  const [dueDate, setDueDate] = useState<Date | undefined>(undefined);
  const [status, setStatus] = useState<'a_fazer' | 'em_andamento' | 'concluido'>('a_fazer');
  const [error, setError] = useState("");
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [loadingCollaborators, setLoadingCollaborators] = useState(false);

  const { updateTask, loading } = useAdministrativeTasksStore();

  useEffect(() => {
    if (open) {
      fetchCollaborators();
    }
  }, [open, clubId]);

  useEffect(() => {
    if (task) {
      setTitle(task.title);
      setDescription(task.description || "");
      setCollaboratorId(task.collaborator_id);
      setDueDate(task.due_date ? new Date(task.due_date) : undefined);
      setStatus(task.status);
    }
  }, [task]);

  const fetchCollaborators = async () => {
    setLoadingCollaborators(true);
    try {
      const clubCollaborators = await getCollaborators(clubId);
      // Sort collaborators alphabetically by name
      clubCollaborators.sort((a, b) => a.full_name.localeCompare(b.full_name));
      setCollaborators(clubCollaborators);
    } catch (error) {
      console.error("Erro ao buscar colaboradores:", error);
    } finally {
      setLoadingCollaborators(false);
    }
  };

  const handleSave = async () => {
    if (!task) {
      return;
    }

    if (!title.trim()) {
      setError("O título é obrigatório.");
      return;
    }

    if (!collaboratorId) {
      setError("É necessário selecionar um colaborador.");
      return;
    }

    setError("");

    try {
      await updateTask(clubId, task.id, {
        title,
        description,
        collaborator_id: collaboratorId,
        due_date: dueDate ? dueDate.toISOString() : undefined,
        status
      });

      toast({
        title: "Tarefa atualizada",
        description: "A tarefa foi atualizada com sucesso.",
      });

      onOpenChange(false);
    } catch (err) {
      setError("Erro ao atualizar tarefa.");
      toast({
        title: "Erro ao atualizar tarefa",
        description: "Ocorreu um erro ao atualizar a tarefa.",
        variant: "destructive",
      });
    }
  };

  if (!task) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Editar Tarefa</DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="title" className="text-right">
              Título
            </Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="col-span-3"
            />
          </div>

          <div className="grid grid-cols-4 items-start gap-4">
            <Label htmlFor="description" className="text-right pt-2">
              Descrição
            </Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="col-span-3"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="collaborator" className="text-right">
              Colaborador
            </Label>
            <Select
              value={collaboratorId?.toString()}
              onValueChange={(value) => setCollaboratorId(parseInt(value))}
            >
              <SelectTrigger id="collaborator" className="col-span-3">
                <SelectValue placeholder="Selecione o colaborador" />
              </SelectTrigger>
              <SelectContent>
                {loadingCollaborators ? (
                  <SelectItem value="loading" disabled>
                    Carregando colaboradores...
                  </SelectItem>
                ) : (
                  collaborators.map((collaborator) => (
                    <SelectItem key={collaborator.id} value={collaborator.id.toString()}>
                      {collaborator.full_name} - {collaborator.role}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="due-date" className="text-right">
              Data Limite
            </Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="due-date"
                  variant="outline"
                  className={cn(
                    "col-span-3 justify-start text-left font-normal",
                    !dueDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {dueDate ? format(dueDate, "PPP", { locale: ptBR }) : "Selecione uma data"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={dueDate}
                  onSelect={setDueDate}
                  initialFocus
                  locale={ptBR}
                />
              </PopoverContent>
            </Popover>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="status" className="text-right">
              Status
            </Label>
            <Select
              value={status}
              onValueChange={(value) => setStatus(value as 'a_fazer' | 'em_andamento' | 'concluido')}
            >
              <SelectTrigger id="status" className="col-span-3">
                <SelectValue placeholder="Selecione o status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="a_fazer">A Fazer</SelectItem>
                <SelectItem value="em_andamento">Em Andamento</SelectItem>
                <SelectItem value="concluido">Concluído</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {error && (
            <div className="text-red-500 text-sm col-span-4 text-center">
              {error}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            {loading ? "Salvando..." : "Salvar"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
