import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { FinancialTransaction } from "@/api/api";
import { useFinancialStore } from "@/store/useFinancialStore";
import { toast } from "@/hooks/use-toast";
import { Upload } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

interface UploadTransactionReceiptDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  transaction: FinancialTransaction | null;
  clubId: number;
}

export function UploadTransactionReceiptDialog({ 
  open, 
  onOpenChange, 
  transaction, 
  clubId 
}: UploadTransactionReceiptDialogProps) {
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const { fetchTransactions } = useFinancialStore();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      
      // Validar tamanho do arquivo (máx. 5MB)
      if (selectedFile.size > 5 * 1024 * 1024) {
        toast({
          title: "Erro",
          description: "O arquivo deve ter no máximo 5MB.",
          variant: "destructive",
        });
        return;
      }
      
      setFile(selectedFile);
    }
  };

  const handleUpload = async () => {
    if (!transaction) {
      toast({
        title: "Erro",
        description: "Nenhuma transação selecionada.",
        variant: "destructive",
      });
      return;
    }

    if (!file) {
      toast({
        title: "Erro",
        description: "Selecione um arquivo para upload.",
        variant: "destructive",
      });
      return;
    }

    try {
      setUploading(true);
      
      // Gerar nome único para o arquivo
      const fileExt = file.name.split('.').pop();
      const fileName = `${clubId}_${transaction.id}_${Date.now()}.${fileExt}`;
      const filePath = `financial_receipts/${fileName}`;

      // Upload do arquivo
      const { error: uploadError } = await supabase.storage
        .from("playerdocuments")
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        console.error("Erro no upload:", uploadError);
        toast({
          title: "Erro",
          description: "Erro ao fazer upload do comprovante.",
          variant: "destructive"
        });
        return;
      }

      // Obter URL pública
      const { data: urlData } = supabase.storage
        .from("playerdocuments")
        .getPublicUrl(filePath);

      // Atualizar a transação com a URL do comprovante
      const { error: updateError } = await supabase
        .from("financial_transactions")
        .update({ receipt_url: urlData.publicUrl })
        .eq("id", transaction.id)
        .eq("club_id", clubId);

      if (updateError) {
        console.error("Erro ao atualizar URL do comprovante:", updateError);
        toast({
          title: "Erro",
          description: "Erro ao salvar o comprovante na transação.",
          variant: "destructive"
        });
        return;
      }

      toast({
        title: "Comprovante enviado",
        description: "O comprovante foi enviado com sucesso.",
      });
      
      // Recarregar transações para atualizar a lista
      await fetchTransactions(clubId);
      
      // Limpar estado e fechar modal
      setFile(null);
      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao fazer upload do comprovante:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao fazer upload do comprovante.",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  // Limpar arquivo quando o modal fechar
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setFile(null);
    }
    onOpenChange(open);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Upload de Comprovante - Transação</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {transaction && (
            <div className="space-y-2">
              <p className="text-sm font-medium">
                Descrição: <span className="font-normal">{transaction.description}</span>
              </p>
              <p className="text-sm font-medium">
                Valor: <span className="font-normal">
                  R$ {typeof transaction.amount === 'string' 
                    ? parseFloat(transaction.amount).toLocaleString('pt-BR', { minimumFractionDigits: 2 })
                    : transaction.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })
                  }
                </span>
              </p>
              <p className="text-sm font-medium">
                Data: <span className="font-normal">
                  {new Date(transaction.date).toLocaleDateString('pt-BR')}
                </span>
              </p>
              <p className="text-sm font-medium">
                Categoria: <span className="font-normal">{transaction.category}</span>
              </p>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="receipt">Selecione o arquivo do comprovante</Label>
            <div className="flex items-center gap-2">
              <Input
                id="receipt"
                type="file"
                onChange={handleFileChange}
                accept=".pdf,.jpg,.jpeg,.png"
                disabled={uploading}
              />
            </div>
            {file && (
              <p className="text-sm text-muted-foreground">
                Arquivo selecionado: {file.name} ({(file.size / 1024).toFixed(2)} KB)
              </p>
            )}
            <p className="text-xs text-muted-foreground">
              Formatos aceitos: PDF, JPG, JPEG, PNG (máx. 5MB)
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => handleOpenChange(false)} disabled={uploading}>
            Cancelar
          </Button>
          <Button onClick={handleUpload} disabled={!file || uploading}>
            {uploading ? (
              <>
                <Upload className="h-4 w-4 mr-2 animate-spin" />
                Enviando...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-2" />
                Enviar Comprovante
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
