import { supabase } from "@/integrations/supabase/client";
import { withPermission, withAuditLog } from "./middleware";
import { v4 as uuidv4 } from "uuid";
import { uploadMedicalCertificate } from "./storage";

// Tipos
export type MedicalProfessional = {
  id: number;
  club_id: number;
  user_id?: string;
  name: string;
  cpf?: string;
  birth_date?: string;
  zip_code?: string;
  state?: string;
  city?: string;
  address?: string;
  credential?: string;
  role: string; // 'médico', 'fisioterapeuta', 'enfermeiro', 'massagista', 'outros'
  certificate_url?: string;
  created_at: string;
};

export type MedicalProfessionalRole =
  | "médico"
  | "fisioterapeuta"
  | "enfermeiro"
  | "massagista"
  | "outros";

// Permissões
export const MEDICAL_PROFESSIONAL_PERMISSIONS = {
  VIEW: "medical_professionals.view",
  CREATE: "medical_professionals.create",
  EDIT: "medical_professionals.edit",
  DELETE: "medical_professionals.delete",
};

/**
 * Busca todos os profissionais médicos de um clube
 * @param clubId ID do clube
 * @returns Lista de profissionais médicos
 */
export async function getMedicalProfessionals(clubId: number): Promise<MedicalProfessional[]> {
  try {
    const { data, error } = await supabase
      .from("medical_professionals")
      .select("*")
      .eq("club_id", clubId)
      .order("name");

    if (error) {
      throw new Error(`Erro ao buscar profissionais médicos: ${error.message}`);
    }

    return data || [];
  } catch (error: any) {
    console.error("Erro ao buscar profissionais médicos:", error);
    throw new Error(error.message || "Erro ao buscar profissionais médicos");
  }
}

/**
 * Busca um profissional médico pelo ID
 * @param clubId ID do clube
 * @param id ID do profissional médico
 * @returns Profissional médico
 */
export async function getMedicalProfessionalById(clubId: number, id: number): Promise<MedicalProfessional> {
  try {
    const { data, error } = await supabase
      .from("medical_professionals")
      .select("*")
      .eq("club_id", clubId)
      .eq("id", id)
      .single();

    if (error) {
      throw new Error(`Erro ao buscar profissional médico: ${error.message}`);
    }

    if (!data) {
      throw new Error("Profissional médico não encontrado");
    }

    return data;
  } catch (error: any) {
    console.error("Erro ao buscar profissional médico:", error);
    throw new Error(error.message || "Erro ao buscar profissional médico");
  }
}

/**
 * Busca um profissional médico pelo ID do usuário
 * @param clubId ID do clube
 * @param userId ID do usuário
 * @returns Profissional médico
 */
export async function getMedicalProfessionalByUserId(clubId: number, userId: string): Promise<MedicalProfessional | null> {
  try {
    const { data, error } = await supabase
      .from("medical_professionals")
      .select("*")
      .eq("club_id", clubId)
      .eq("user_id", userId)
      .maybeSingle();

    if (error) {
      throw new Error(`Erro ao buscar profissional médico: ${error.message}`);
    }

    return data;
  } catch (error: any) {
    console.error("Erro ao buscar profissional médico:", error);
    throw new Error(error.message || "Erro ao buscar profissional médico");
  }
}

/**
 * Cria um novo profissional médico
 * @param clubId ID do clube
 * @param userId ID do usuário que está criando
 * @param professional Dados do profissional médico
 * @returns Profissional médico criado
 */
export async function createMedicalProfessional(
  clubId: number,
  userId: string,
  professional: Omit<MedicalProfessional, "id" | "club_id" | "created_at">
): Promise<MedicalProfessional> {
  return withPermission(
    clubId,
    userId,
    MEDICAL_PROFESSIONAL_PERMISSIONS.CREATE,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "medical_professional.create",
        { name: professional.name, role: professional.role },
        async () => {
          try {
            const { data, error } = await supabase
              .from("medical_professionals")
              .insert({
                club_id: clubId,
                ...professional,
              })
              .select()
              .single();

            if (error) {
              throw new Error(`Erro ao criar profissional médico: ${error.message}`);
            }

            return data;
          } catch (error: any) {
            console.error("Erro ao criar profissional médico:", error);
            throw new Error(error.message || "Erro ao criar profissional médico");
          }
        }
      );
    }
  );
}

/**
 * Atualiza um profissional médico
 * @param clubId ID do clube
 * @param userId ID do usuário que está atualizando
 * @param id ID do profissional médico
 * @param professional Dados do profissional médico
 * @returns Profissional médico atualizado
 */
export async function updateMedicalProfessional(
  clubId: number,
  userId: string,
  id: number,
  professional: Partial<Omit<MedicalProfessional, "id" | "club_id" | "created_at">>
): Promise<MedicalProfessional> {
  return withPermission(
    clubId,
    userId,
    MEDICAL_PROFESSIONAL_PERMISSIONS.EDIT,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "medical_professional.update",
        { id, ...professional },
        async () => {
          try {
            const { data, error } = await supabase
              .from("medical_professionals")
              .update(professional)
              .eq("club_id", clubId)
              .eq("id", id)
              .select()
              .single();

            if (error) {
              throw new Error(`Erro ao atualizar profissional médico: ${error.message}`);
            }

            return data;
          } catch (error: any) {
            console.error("Erro ao atualizar profissional médico:", error);
            throw new Error(error.message || "Erro ao atualizar profissional médico");
          }
        }
      );
    }
  );
}

/**
 * Exclui um profissional médico
 * @param clubId ID do clube
 * @param userId ID do usuário que está excluindo
 * @param id ID do profissional médico
 */
export async function deleteMedicalProfessional(
  clubId: number,
  userId: string,
  id: number
): Promise<void> {
  return withPermission(
    clubId,
    userId,
    MEDICAL_PROFESSIONAL_PERMISSIONS.DELETE,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "medical_professional.delete",
        { id },
        async () => {
          try {
            const { error } = await supabase
              .from("medical_professionals")
              .delete()
              .eq("club_id", clubId)
              .eq("id", id);

            if (error) {
              throw new Error(`Erro ao excluir profissional médico: ${error.message}`);
            }
          } catch (error: any) {
            console.error("Erro ao excluir profissional médico:", error);
            throw new Error(error.message || "Erro ao excluir profissional médico");
          }
        }
      );
    }
  );
}

/**
 * Faz upload do certificado de um profissional médico
 * @param clubId ID do clube
 * @param userId ID do usuário que está fazendo upload
 * @param professionalId ID do profissional médico
 * @param file Arquivo do certificado
 * @returns URL do certificado
 */
export async function uploadMedicalProfessionalCertificate(
  clubId: number,
  userId: string,
  professionalId: number,
  file: File
): Promise<string> {
  return withPermission(
    clubId,
    userId,
    MEDICAL_PROFESSIONAL_PERMISSIONS.EDIT,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "medical_professional.upload_certificate",
        { professionalId, fileName: file.name },
        async () => {
          try {
            // Fazer upload do arquivo
            const certificateUrl = await uploadMedicalCertificate(clubId, professionalId, file);

            // Atualizar o profissional médico com a URL do certificado
            await supabase
              .from("medical_professionals")
              .update({ certificate_url: certificateUrl })
              .eq("club_id", clubId)
              .eq("id", professionalId);

            return certificateUrl;
          } catch (error: any) {
            console.error("Erro ao fazer upload do certificado:", error);
            throw new Error(error.message || "Erro ao fazer upload do certificado");
          }
        }
      );
    }
  );
}

/**
 * Interface para criação de conta de profissional médico
 */
export interface MedicalProfessionalAccountOptions {
  email: string;
  password?: string;
  sendInvite?: boolean;
}

/**
 * Cria uma conta de usuário para um profissional médico
 * @param clubId ID do clube
 * @param professionalId ID do profissional médico
 * @param options Opções para criação da conta
 * @returns Profissional médico atualizado
 */
export async function createMedicalProfessionalAccount(
  clubId: number,
  professionalId: number,
  options: MedicalProfessionalAccountOptions
): Promise<MedicalProfessional> {
  try {
    // Importar a função de criação de conta simplificada
    const { createMedicalAccount } = await import('./medicalAccountCreator');

    // Usar a função simplificada
    const result = await createMedicalAccount(clubId, professionalId, options);

    if (!result.success) {
      throw new Error(result.message);
    }

    // Buscar o profissional médico atualizado
    try {
      const { data, error } = await supabase
        .from("medical_professionals")
        .select("*")
        .eq("club_id", clubId as any)
        .eq("id", professionalId as any)
        .single();

      if (error || !data) {
        throw new Error(`Erro ao buscar profissional médico atualizado: ${error?.message || "Profissional não encontrado"}`);
      }

      return data as MedicalProfessional;
    } catch (fetchError) {
      console.error("Erro ao buscar profissional médico atualizado:", fetchError);

      // Retornar um objeto mínimo para satisfazer o tipo
      return {
        id: professionalId as number,
        club_id: clubId as number,
        name: "Profissional Médico",
        role: "médico",
        created_at: new Date().toISOString()
      } as MedicalProfessional;
    }
  } catch (error: any) {
    console.error("Erro ao criar conta para profissional médico:", error);
    throw new Error(error.message || "Erro ao criar conta para profissional médico");
  }
}
