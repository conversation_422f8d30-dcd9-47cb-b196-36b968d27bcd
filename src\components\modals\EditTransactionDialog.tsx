import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useFinancialStore } from "@/store/useFinancialStore";
import { Label } from "@/components/ui/label";
import { FinancialTransaction } from "@/api/api";
import { toast } from "@/hooks/use-toast";

interface EditTransactionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  transaction: FinancialTransaction | null;
  clubId: number;
}

export function EditTransactionDialog({ open, onOpenChange, transaction, clubId }: EditTransactionDialogProps) {
  const [description, setDescription] = useState("");
  const [amount, setAmount] = useState("");
  const [category, setCategory] = useState("");
  const [type, setType] = useState("");
  const [date, setDate] = useState("");
  const { updateTransaction, loading } = useFinancialStore();

  // Initialize form with transaction data when it changes
  useEffect(() => {
    if (transaction) {
      setDescription(transaction.description || "");
      setAmount(transaction.amount?.toString() || "");
      setCategory(transaction.category || "");
      setType(transaction.type || "");
      setDate(transaction.date || "");
    }
  }, [transaction]);

  const handleSave = async () => {
    if (!transaction || !description || !amount || !date) {
      toast({
        title: "Erro",
        description: "Preencha todos os campos obrigatórios",
        variant: "destructive",
      });
      return;
    }

    // Convert amount to number
    const numericAmount = parseFloat(amount);

    if (isNaN(numericAmount)) {
      toast({
        title: "Erro",
        description: "O valor deve ser um número válido",
        variant: "destructive",
      });
      return;
    }

    try {
      await updateTransaction(clubId, transaction.id, {
        description,
        amount: numericAmount,
        category,
        type,
        date,
      });

      toast({
        title: "Sucesso",
        description: "Transação atualizada com sucesso",
      });
      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao atualizar transação:", error);
      toast({
        title: "Erro",
        description: "Não foi possível atualizar a transação",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Editar Transação</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="description">Descrição</Label>
            <Input
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Descrição da transação"
            />
          </div>

          <div>
            <Label htmlFor="amount">Valor (R$)</Label>
            <Input
              id="amount"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              placeholder="0,00"
              type="number"
              step="0.01"
            />
          </div>

          <div>
            <Label htmlFor="date">Data</Label>
            <Input
              id="date"
              value={date}
              onChange={(e) => setDate(e.target.value)}
              type="date"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="category">Categoria</Label>
              <Select value={category} onValueChange={setCategory}>
                <SelectTrigger id="category">
                  <SelectValue placeholder="Categoria" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="salários">Salários</SelectItem>
                  <SelectItem value="transferências">Transferências</SelectItem>
                  <SelectItem value="patrocínios">Patrocínios</SelectItem>
                  <SelectItem value="bilheteria">Bilheteria</SelectItem>
                  <SelectItem value="material">Material Esportivo</SelectItem>
                  <SelectItem value="viagens">Viagens</SelectItem>
                  <SelectItem value="alimentação">Alimentação</SelectItem>
                  <SelectItem value="infraestrutura">Infraestrutura</SelectItem>
                  <SelectItem value="outros">Outros</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="type">Tipo</Label>
              <Select value={type} onValueChange={setType}>
                <SelectTrigger id="type">
                  <SelectValue placeholder="Tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="receita">Receita</SelectItem>
                  <SelectItem value="despesa">Despesa</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSave} disabled={!description || !amount || !date || loading}>
            Salvar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
