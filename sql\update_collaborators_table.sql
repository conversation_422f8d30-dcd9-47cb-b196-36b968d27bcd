-- Add new fields to collaborators table
ALTER TABLE collaborators
ADD COLUMN IF NOT EXISTS image TEXT,
ADD COLUMN IF NOT EXISTS salary DECIMAL(10, 2),
ADD COLUMN IF NOT EXISTS bonus DECIMAL(10, 2),
ADD COLUMN IF NOT EXISTS bank_info JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS entry_date DATE,
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'available',
ADD COLUMN IF NOT EXISTS document_id TEXT,
ADD COLUMN IF NOT EXISTS document_id_url TEXT,
ADD COLUMN IF NOT EXISTS certificate_url TEXT,
ADD COLUMN IF NOT EXISTS medical_certificate_url TEXT,
ADD COLUMN IF NOT EXISTS resume_url TEXT,
ADD COLUMN IF NOT EXISTS criminal_record_url TEXT;

-- Update the collaborators_view to include the new fields
CREATE OR REPLACE VIEW collaborators_view AS
SELECT 
  c.*,
  u.name as user_name,
  u.email as user_email
FROM collaborators c
LEFT JOIN users u ON c.user_id = u.id;

-- Create a function to calculate age from birth_date
CREATE OR REPLACE FUNCTION calculate_age(birth_date DATE)
RETURNS INTEGER AS $$
BEGIN
  RETURN EXTRACT(YEAR FROM AGE(CURRENT_DATE, birth_date));
END;
$$ LANGUAGE plpgsql;
