import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DataTable } from "@/components/ui/data-table";
import { useToast } from "@/components/ui/use-toast";
import { Badge } from "@/components/ui/badge";
import {
  Plus,
  Search,
  Package,
  AlertTriangle,
  Bell,
  FileText,
  Pencil,
  Trash2,
  ArrowUpCircle,
  ArrowDownCircle,
  Download,
  Loader2,
  ShoppingCart,
  ClipboardList,
  Check,
  RefreshCcw,
  Image,
  X
} from "lucide-react";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { useClubInfoStore } from "@/store/useClubInfoStore";
import {
  InventoryProduct,
  InventoryRequest,
  InventoryRequestItem,
  INVENTORY_DEPARTMENTS,
  getInventoryProducts,
  getLowStockProducts,
  getInventoryNotificationSettings,
  updateInventoryNotificationSettings,
  deleteInventoryProduct,
  getInventoryRequests,
  getInventoryRequestItems,
  deleteInventoryRequest,
  cancelInventoryRequest,
  getLowStockShoppingList
} from "@/api/api";
import { generateInventoryReport, generateInventoryReportByDepartments } from "@/utils/inventoryReportGenerator";
import { generateInventoryRequestReport, generateShoppingListReport, generateShoppingListReportByDepartment } from "@/utils/inventoryRequestReportGenerator";
import { CadastrarProdutoDialog } from "@/components/estoque/CadastrarProdutoDialog";
import { MovimentarEstoqueDialog } from "@/components/estoque/MovimentarEstoqueDialog";
import { SolicitacaoEstoqueDialog } from "@/components/estoque/SolicitacaoEstoqueDialog";
import { ProcessarSolicitacaoDialog } from "@/components/estoque/ProcessarSolicitacaoDialog";
import { DevolverItemDialog } from "@/components/estoque/DevolverItemDialog";
import { UploadImagemProdutoDialog } from "@/components/estoque/UploadImagemProdutoDialog";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";

export default function Estoque() {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { toast } = useToast();
  const { clubInfo, fetchClubInfo } = useClubInfoStore();
  const [activeTab, setActiveTab] = useState("cadastro");
  const [products, setProducts] = useState<InventoryProduct[]>([]);
  const [lowStockProducts, setLowStockProducts] = useState<InventoryProduct[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [movimentarDialogOpen, setMovimentarDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<InventoryProduct | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [transactionType, setTransactionType] = useState<'entrada' | 'saida'>('entrada');
  const [notificationThreshold, setNotificationThreshold] = useState(5);
  const [notifyUsers, setNotifyUsers] = useState<string[]>([]);
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [systemNotifications, setSystemNotifications] = useState(true);

  // Estado para solicitações de estoque
  const [requests, setRequests] = useState<InventoryRequest[]>([]);
  const [selectedRequest, setSelectedRequest] = useState<InventoryRequest | null>(null);
  const [selectedRequestItems, setSelectedRequestItems] = useState<InventoryRequestItem[]>([]);
  const [solicitacaoDialogOpen, setSolicitacaoDialogOpen] = useState(false);
  const [processarDialogOpen, setProcessarDialogOpen] = useState(false);
  const [deleteRequestDialogOpen, setDeleteRequestDialogOpen] = useState(false);
  const [devolverItemDialogOpen, setDevolverItemDialogOpen] = useState(false);
  const [selectedRequestItem, setSelectedRequestItem] = useState<InventoryRequestItem | null>(null);
  const [uploadImageDialogOpen, setUploadImageDialogOpen] = useState(false);
  const [selectedProductName, setSelectedProductName] = useState("");

  // Carregar produtos e informações do clube
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);

        // Carregar produtos
        const data = await getInventoryProducts(clubId);
        setProducts(data);

        // Carregar produtos com estoque baixo
        const lowStock = await getLowStockProducts(clubId);
        setLowStockProducts(lowStock);

        // Carregar solicitações de estoque
        const requestsData = await getInventoryRequests(clubId, undefined, user?.id);
        setRequests(requestsData);

        // Carregar informações do clube (para os relatórios)
        if (!clubInfo) {
          fetchClubInfo(clubId);
        }
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        toast({
          title: "Erro",
          description: "Não foi possível carregar os dados do estoque.",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [clubId, toast, clubInfo, fetchClubInfo, user?.id]);

  // Filtrar produtos por departamento e termo de busca
  const filteredProducts = products.filter(product => {
    const matchesDepartment = !selectedDepartment || product.department === selectedDepartment;
    const matchesSearch = !searchTerm ||
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.location?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.description?.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesDepartment && matchesSearch;
  });

  // Filtrar produtos por departamento para relatórios
  const productsByDepartment = INVENTORY_DEPARTMENTS.map(department => {
    const departmentProducts = products.filter(product => product.department === department);
    return {
      department,
      products: departmentProducts
    };
  });

  // Colunas para a tabela de produtos
  const productColumns = [
    {
      key: "image_url",
      header: "Imagem",
      searchable: false,
      render: (row: InventoryProduct) => (
        row.image_url ? (
          <div className="w-10 h-10 rounded-md overflow-hidden">
            <img
              src={row.image_url}
              alt={row.name}
              className="w-full h-full object-cover"
              onError={(e) => {
                (e.target as HTMLImageElement).style.display = 'none';
              }}
            />
          </div>
        ) : null
      )
    },
    { key: "name", header: "Nome do Produto", searchable: true },
    {
      key: "quantity",
      header: "Quantidade",
      searchable: false,
      render: (row: InventoryProduct) => `${row.quantity} ${row.unit_of_measure || 'unidade'}`
    },
    {
      key: "minimum_quantity",
      header: "Qtd. Mínima",
      searchable: false,
      render: (row: InventoryProduct) => `${row.minimum_quantity || 0} ${row.unit_of_measure || 'unidade'}`
    },
    { key: "registration_date", header: "Data de Cadastro", searchable: false,
      render: (row: InventoryProduct) => new Date(row.registration_date).toLocaleDateString() },
    { key: "location", header: "Localização", searchable: true },
    { key: "department", header: "Departamento", searchable: true }
  ];

  // Colunas para a tabela de produtos com estoque baixo
  const lowStockColumns = [
    { key: "name", header: "Nome do Produto", searchable: true },
    {
      key: "quantity",
      header: "Quantidade",
      searchable: false,
      render: (row: InventoryProduct) => `${row.quantity} ${row.unit_of_measure || 'unidade'}`
    },
    {
      key: "minimum_quantity",
      header: "Qtd. Mínima",
      searchable: false,
      render: (row: InventoryProduct) => `${row.minimum_quantity || 0} ${row.unit_of_measure || 'unidade'}`
    },
    { key: "department", header: "Departamento", searchable: true },
    { key: "location", header: "Localização", searchable: true }
  ];

  // Colunas para a tabela de solicitações
  const requestColumns = [
    { key: "id", header: "ID", searchable: true },
    { key: "requester_name", header: "Solicitante", searchable: true },
    { key: "category", header: "Categoria", searchable: true },
    { key: "withdrawal_date", header: "Data de Retirada", searchable: false,
      render: (row: InventoryRequest) => new Date(row.withdrawal_date).toLocaleDateString() },
    { key: "status", header: "Status", searchable: true,
      render: (row: InventoryRequest) => {
        const statusMap: Record<string, { label: string, color: string }> = {
          pending: { label: "Pendente", color: "bg-yellow-100 text-yellow-800" },
          approved: { label: "Aprovado", color: "bg-blue-100 text-blue-800" },
          rejected: { label: "Rejeitado", color: "bg-red-100 text-red-800" },
          completed: { label: "Concluído", color: "bg-green-100 text-green-800" }
        };

        const status = statusMap[row.status] || { label: row.status, color: "bg-gray-100 text-gray-800" };

        return (
          <Badge className={status.color}>
            {status.label}
          </Badge>
        );
      }
    },
    { key: "created_at", header: "Data de Criação", searchable: false,
      render: (row: InventoryRequest) => new Date(row.created_at).toLocaleDateString() }
  ];

  // Colunas para a tabela de itens da solicitação
  const requestItemColumns = [
    { key: "product_name", header: "Produto", searchable: true },
    { key: "product_department", header: "Departamento", searchable: true },
    { key: "quantity", header: "Quantidade", searchable: false },
    { key: "returned_quantity", header: "Devolvido", searchable: false },
    { key: "available_quantity", header: "Disponível", searchable: false }
  ];

  // Ações para a tabela de solicitações
  const requestActions = (request: InventoryRequest) => (
    <div className="flex items-center gap-2">
      {request.status === 'pending' && (
        <Button
          variant="ghost"
          size="icon"
          onClick={() => handleProcessRequest(request)}
          title="Processar Solicitação"
        >
          <Check className="h-4 w-4 text-green-500" />
        </Button>
      )}
      <Button
        variant="ghost"
        size="icon"
        onClick={() => handleGenerateRequestReport(request)}
        title="Gerar Relatório"
      >
        <FileText className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => {
          setSelectedRequest(request);
          loadRequestItems(request.id);
        }}
        title="Ver Itens"
      >
        <ClipboardList className="h-4 w-4" />
      </Button>
      {request.status === 'pending' && (
        <>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => handleCancelRequest(request)}
            title="Cancelar Solicitação"
          >
            <X className="h-4 w-4 text-orange-500" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => {
              setSelectedRequest(request);
              setDeleteRequestDialogOpen(true);
            }}
            title="Excluir"
          >
            <Trash2 className="h-4 w-4 text-red-500" />
          </Button>
        </>
      )}
    </div>
  );

  // Ações para a tabela de itens da solicitação
  const requestItemActions = (item: InventoryRequestItem) => (
    <div className="flex items-center gap-2">
      {item.quantity > item.returned_quantity && (
        <Button
          variant="ghost"
          size="icon"
          onClick={() => handleReturnItem(item)}
          title="Devolver ao Estoque"
        >
          <RefreshCcw className="h-4 w-4 text-blue-500" />
        </Button>
      )}
    </div>
  );

  // Ações para a tabela de produtos
  const productActions = (product: InventoryProduct) => (
    <div className="flex items-center gap-2">
      <Button
        variant="ghost"
        size="icon"
        onClick={() => {
          setSelectedProduct(product);
          setTransactionType('entrada');
          setMovimentarDialogOpen(true);
        }}
        title="Entrada"
      >
        <ArrowUpCircle className="h-4 w-4 text-green-500" />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => {
          setSelectedProduct(product);
          setTransactionType('saida');
          setMovimentarDialogOpen(true);
        }}
        title="Saída"
      >
        <ArrowDownCircle className="h-4 w-4 text-red-500" />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => {
          setSelectedProduct(product);
          setDialogOpen(true);
        }}
        title="Editar"
      >
        <Pencil className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => handleUploadProductImage(product)}
        title="Enviar Imagem"
      >
        <Image className="h-4 w-4 text-blue-500" />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => {
          setSelectedProduct(product);
          setDeleteDialogOpen(true);
        }}
        title="Excluir"
      >
        <Trash2 className="h-4 w-4 text-red-500" />
      </Button>
    </div>
  );

  // Salvar configurações de notificação
  const saveNotificationSettings = async () => {
    try {
      await updateInventoryNotificationSettings(clubId, {
        threshold: notificationThreshold,
        notify_users: notifyUsers,
        email_notifications: emailNotifications,
        system_notifications: systemNotifications
      }, user?.id);

      toast({
        title: "Sucesso",
        description: "Configurações de notificação salvas com sucesso.",
      });
    } catch (error) {
      console.error("Erro ao salvar configurações de notificação:", error);
      toast({
        title: "Erro",
        description: "Não foi possível salvar as configurações de notificação.",
        variant: "destructive"
      });
    }
  };

  // Atualizar lista de produtos após operações
  const refreshProducts = async () => {
    try {
      const data = await getInventoryProducts(clubId);
      setProducts(data);

      const lowStock = await getLowStockProducts(clubId);
      setLowStockProducts(lowStock);
    } catch (error) {
      console.error("Erro ao atualizar produtos:", error);
    }
  };

  // Atualizar lista de solicitações após operações
  const refreshRequests = async () => {
    try {
      const requestsData = await getInventoryRequests(clubId, undefined, user?.id);
      setRequests(requestsData);
    } catch (error) {
      console.error("Erro ao atualizar solicitações:", error);
    }
  };

  // Carregar itens de uma solicitação
  const loadRequestItems = async (requestId: number) => {
    try {
      setIsLoading(true);
      const items = await getInventoryRequestItems(clubId, requestId, user?.id);
      setSelectedRequestItems(items);
    } catch (error) {
      console.error("Erro ao carregar itens da solicitação:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar os itens da solicitação.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Processar uma solicitação
  const handleProcessRequest = (request: InventoryRequest) => {
    setSelectedRequest(request);
    loadRequestItems(request.id);
    setProcessarDialogOpen(true);
  };

  // Devolver um item
  const handleReturnItem = (item: InventoryRequestItem) => {
    setSelectedRequestItem(item);
    setDevolverItemDialogOpen(true);
  };

  // Cancelar uma solicitação
  const handleCancelRequest = async (request: InventoryRequest) => {
    try {
      await cancelInventoryRequest(clubId, request.id, user?.id);

      toast({
        title: "Sucesso",
        description: "Solicitação cancelada com sucesso.",
      });

      // Atualizar a lista de solicitações
      refreshRequests();
    } catch (error) {
      console.error("Erro ao cancelar solicitação:", error);
      toast({
        title: "Erro",
        description: "Não foi possível cancelar a solicitação.",
        variant: "destructive"
      });
    }
  };

  // Gerar relatório de solicitação
  const handleGenerateRequestReport = async (request: InventoryRequest) => {
    try {
      setIsGeneratingPDF(true);

      if (!clubInfo) {
        await fetchClubInfo(clubId);
      }

      // Carregar itens da solicitação
      const items = await getInventoryRequestItems(clubId, request.id, user?.id);

      // Gerar o relatório
      const blob = await generateInventoryRequestReport(
        request,
        items,
        clubInfo!,
        `solicitacao-${request.id}.pdf`
      );

      // Criar URL para download
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `solicitacao-${request.id}.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      toast({
        title: "Sucesso",
        description: "Relatório de solicitação gerado com sucesso.",
      });
    } catch (error) {
      console.error("Erro ao gerar relatório de solicitação:", error);
      toast({
        title: "Erro",
        description: "Não foi possível gerar o relatório de solicitação.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  // Gerar relatório de lista de compras
  const handleGenerateShoppingListReport = async () => {
    try {
      setIsGeneratingPDF(true);

      if (!clubInfo) {
        await fetchClubInfo(clubId);
      }

      // Carregar produtos com estoque baixo
      const lowStockItems = await getLowStockShoppingList(clubId, 0, user?.id);

      // Gerar o relatório
      const blob = await generateShoppingListReport(
        lowStockItems,
        clubInfo!,
        0,
        'lista-compras.pdf'
      );

      // Criar URL para download
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'lista-compras.pdf';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      toast({
        title: "Sucesso",
        description: "Lista de compras gerada com sucesso.",
      });
    } catch (error) {
      console.error("Erro ao gerar lista de compras:", error);
      toast({
        title: "Erro",
        description: "Não foi possível gerar a lista de compras.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  // Gerar relatório de lista de compras por departamento
  const handleGenerateShoppingListByDepartmentReport = async () => {
    try {
      setIsGeneratingPDF(true);

      if (!clubInfo) {
        await fetchClubInfo(clubId);
      }

      // Carregar produtos com estoque baixo (todos os departamentos)
      const lowStockItems = await getLowStockShoppingList(clubId, 0, user?.id);

      // Gerar o relatório por departamento
      const blob = await generateShoppingListReportByDepartment(
        lowStockItems,
        INVENTORY_DEPARTMENTS,
        clubInfo!,
        'lista-compras-por-departamento.pdf'
      );

      // Criar URL para download
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'lista-compras-por-departamento.pdf';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      toast({
        title: "Sucesso",
        description: "Lista de compras por departamento gerada com sucesso.",
      });
    } catch (error) {
      console.error("Erro ao gerar lista de compras por departamento:", error);
      toast({
        title: "Erro",
        description: "Não foi possível gerar a lista de compras por departamento.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  // Gerar relatório de lista de compras para um departamento específico
  const handleGenerateShoppingListForDepartment = async (department: string) => {
    try {
      setIsGeneratingPDF(true);

      if (!clubInfo) {
        await fetchClubInfo(clubId);
      }

      // Carregar produtos com estoque baixo para o departamento específico
      const lowStockItems = await getLowStockShoppingList(clubId, 0, user?.id, department);

      // Se não houver produtos com estoque baixo neste departamento, mostrar mensagem
      if (lowStockItems.length === 0) {
        toast({
          title: "Informação",
          description: `Não há produtos com estoque baixo no departamento ${department}.`,
        });
        setIsGeneratingPDF(false);
        return;
      }

      // Gerar o relatório
      const fileName = `lista-compras-${department.toLowerCase().replace(/\s+/g, '-')}.pdf`;
      const blob = await generateShoppingListReport(
        lowStockItems,
        clubInfo!,
        0,
        fileName
      );

      // Criar URL para download
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      toast({
        title: "Sucesso",
        description: `Lista de compras para ${department} gerada com sucesso.`,
      });
    } catch (error) {
      console.error(`Erro ao gerar lista de compras para ${department}:`, error);
      toast({
        title: "Erro",
        description: `Não foi possível gerar a lista de compras para ${department}.`,
        variant: "destructive"
      });
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  // Fazer upload de imagem de produto
  const handleUploadProductImage = (product: InventoryProduct) => {
    setSelectedProduct(product);
    setSelectedProductName(product.name);
    setUploadImageDialogOpen(true);
  };

  // Gerar relatório de estoque para um departamento específico
  const handleGenerateReport = async (department: string | null) => {
    try {
      setIsGeneratingPDF(true);

      if (!clubInfo) {
        await fetchClubInfo(clubId);
      }

      // Nome do arquivo
      const fileName = department
        ? `relatorio-estoque-${department.toLowerCase().replace(/\s+/g, '-')}.pdf`
        : 'relatorio-estoque-completo.pdf';

      // Gerar o relatório
      await generateInventoryReport(products, department, clubInfo!, fileName);

      toast({
        title: "Sucesso",
        description: "Relatório gerado com sucesso.",
      });
    } catch (error) {
      console.error("Erro ao gerar relatório:", error);
      toast({
        title: "Erro",
        description: "Não foi possível gerar o relatório.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  // Gerar relatório de estoque com todos os departamentos
  const handleGenerateAllDepartmentsReport = async () => {
    try {
      setIsGeneratingPDF(true);

      if (!clubInfo) {
        await fetchClubInfo(clubId);
      }

      // Gerar o relatório
      await generateInventoryReportByDepartments(
        products,
        INVENTORY_DEPARTMENTS,
        clubInfo!,
        'relatorio-estoque-por-departamentos.pdf'
      );

      toast({
        title: "Sucesso",
        description: "Relatório completo gerado com sucesso.",
      });
    } catch (error) {
      console.error("Erro ao gerar relatório completo:", error);
      toast({
        title: "Erro",
        description: "Não foi possível gerar o relatório completo.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Estoque</h1>
          <p className="text-muted-foreground">
            Gerencie o estoque de produtos do clube
          </p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="cadastro" className="flex items-center gap-1">
            <Package className="h-4 w-4" />
            <span>Cadastro de Produto</span>
          </TabsTrigger>
          <TabsTrigger value="solicitacoes" className="flex items-center gap-1">
            <ClipboardList className="h-4 w-4" />
            <span>Solicitações</span>
          </TabsTrigger>
          <TabsTrigger value="relatorios" className="flex items-center gap-1">
            <FileText className="h-4 w-4" />
            <span>Relatórios por Departamento</span>
          </TabsTrigger>
          <TabsTrigger value="baixo-estoque" className="flex items-center gap-1">
            <AlertTriangle className="h-4 w-4" />
            <span>Produtos com Baixo Estoque</span>
          </TabsTrigger>
          <TabsTrigger value="lista-compras" className="flex items-center gap-1">
            <ShoppingCart className="h-4 w-4" />
            <span>Lista de Compras</span>
          </TabsTrigger>

        </TabsList>

        {/* Tab de Cadastro de Produto */}
        <TabsContent value="cadastro">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Cadastro de Produtos</CardTitle>
                <CardDescription>
                  Gerencie os produtos do estoque
                </CardDescription>
              </div>
              <Button onClick={() => {
                setSelectedProduct(null);
                setDialogOpen(true);
              }}>
                <Plus className="h-4 w-4 mr-2" />
                Novo Produto
              </Button>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row gap-4 mb-6">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Buscar produtos..."
                      className="pl-8"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>
                <div className="w-full md:w-64">
                  <Select
                    value={selectedDepartment || "all"}
                    onValueChange={(value) => setSelectedDepartment(value === "all" ? null : value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Todos os departamentos" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todos os departamentos</SelectItem>
                      {INVENTORY_DEPARTMENTS.map((dept) => (
                        <SelectItem key={dept} value={dept}>
                          {dept}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <DataTable
                data={filteredProducts}
                columns={productColumns}
                actions={productActions}
                isLoading={isLoading}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tab de Relatórios por Departamento */}
        <TabsContent value="relatorios">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Relatórios por Departamento</CardTitle>
                <CardDescription>
                  Visualize os produtos por departamento
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Select
                  value={selectedDepartment || "all"}
                  onValueChange={(value) => setSelectedDepartment(value === "all" ? null : value)}
                >
                  <SelectTrigger className="w-[200px] mr-2">
                    <SelectValue placeholder="Filtrar departamento" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos os departamentos</SelectItem>
                    {INVENTORY_DEPARTMENTS.map((dept) => (
                      <SelectItem key={dept} value={dept}>
                        {dept}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  variant="outline"
                  onClick={handleGenerateAllDepartmentsReport}
                  disabled={isGeneratingPDF || products.length === 0}
                >
                  {isGeneratingPDF ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Gerando...
                    </>
                  ) : (
                    <>
                      <Download className="mr-2 h-4 w-4" />
                      Baixar Todos
                    </>
                  )}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {productsByDepartment
                .filter(({ department }) => !selectedDepartment || department === selectedDepartment)
                .map(({ department, products }) => (
                <div key={department} className="mb-8">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold">{department}</h3>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleGenerateReport(department)}
                      disabled={isGeneratingPDF || products.length === 0}
                      className="h-8"
                    >
                      {isGeneratingPDF ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <>
                          <Download className="mr-2 h-4 w-4" />
                          PDF
                        </>
                      )}
                    </Button>
                  </div>

                  {products.length === 0 ? (
                    <p className="text-muted-foreground">Nenhum produto cadastrado neste departamento.</p>
                  ) : (
                    <div className="border rounded-md">
                      <table className="w-full">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left p-3">Data Registro</th>
                            <th className="text-left p-3">Produto</th>
                            <th className="text-left p-3">Quantidade</th>
                            <th className="text-left p-3">Localização</th>
                          </tr>
                        </thead>
                        <tbody>
                          {products.map((product) => (
                            <tr key={product.id} className="border-b">
                              <td className="p-3">{new Date(product.registration_date).toLocaleDateString()}</td>
                              <td className="p-3">{product.name}</td>
                              <td className="p-3">{product.quantity}</td>
                              <td className="p-3">{product.location || "-"}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
              ))}

              {products.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">Não há produtos cadastrados no estoque.</p>
                </div>
              )}

              {products.length > 0 && (
                <div className="mt-6 pt-6 border-t">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold">Relatório Completo</h3>
                      <p className="text-sm text-muted-foreground">
                        Baixe um relatório completo com todos os produtos do estoque
                      </p>
                    </div>
                    <Button
                      onClick={() => handleGenerateReport(null)}
                      disabled={isGeneratingPDF}
                    >
                      {isGeneratingPDF ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Gerando...
                        </>
                      ) : (
                        <>
                          <Download className="mr-2 h-4 w-4" />
                          Baixar Relatório Completo
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tab de Produtos com Baixo Estoque */}
        <TabsContent value="baixo-estoque">
          <Card>
            <CardHeader>
              <CardTitle>Produtos com Baixo Estoque</CardTitle>
              <CardDescription>
                Produtos com quantidade inferior à quantidade mínima configurada
              </CardDescription>
            </CardHeader>
            <CardContent>
              {lowStockProducts.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">Não há produtos com estoque baixo.</p>
                </div>
              ) : (
                <DataTable
                  data={lowStockProducts}
                  columns={lowStockColumns}
                  actions={productActions}
                  isLoading={isLoading}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tab de Solicitações */}
        <TabsContent value="solicitacoes">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Solicitações de Estoque</CardTitle>
                <CardDescription>
                  Gerencie as solicitações de produtos do estoque
                </CardDescription>
              </div>
              <Button onClick={() => {
                setSelectedRequest(null);
                setSolicitacaoDialogOpen(true);
              }}>
                <Plus className="h-4 w-4 mr-2" />
                Nova Solicitação
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Tabela de solicitações */}
                <div>
                  <DataTable
                    data={requests}
                    columns={requestColumns}
                    actions={requestActions}
                    isLoading={isLoading}
                  />
                </div>

                {/* Detalhes da solicitação selecionada */}
                {selectedRequest && (
                  <div className="mt-8 border-t pt-6">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-semibold">
                        Itens da Solicitação #{selectedRequest.id}
                      </h3>
                      <div className="flex items-center gap-2">
                        <Badge className={
                          selectedRequest.status === 'pending' ? "bg-yellow-100 text-yellow-800" :
                          selectedRequest.status === 'approved' ? "bg-blue-100 text-blue-800" :
                          selectedRequest.status === 'rejected' ? "bg-red-100 text-red-800" :
                          "bg-green-100 text-green-800"
                        }>
                          {selectedRequest.status === 'pending' ? "Pendente" :
                           selectedRequest.status === 'approved' ? "Aprovado" :
                           selectedRequest.status === 'rejected' ? "Rejeitado" :
                           "Concluído"}
                        </Badge>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <p className="text-sm font-medium">Solicitante</p>
                        <p className="text-sm">{selectedRequest.requester_name}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Categoria</p>
                        <p className="text-sm">{selectedRequest.category}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Data de Retirada</p>
                        <p className="text-sm">{new Date(selectedRequest.withdrawal_date).toLocaleDateString()}</p>
                      </div>
                    </div>

                    <DataTable
                      data={selectedRequestItems}
                      columns={requestItemColumns}
                      actions={requestItemActions}
                      isLoading={isLoading}
                    />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tab de Lista de Compras */}
        <TabsContent value="lista-compras">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Lista de Compras</CardTitle>
                <CardDescription>
                  Produtos com quantidade inferior à quantidade mínima configurada
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  onClick={handleGenerateShoppingListByDepartmentReport}
                  disabled={isGeneratingPDF || lowStockProducts.length === 0}
                >
                  {isGeneratingPDF ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Gerando...
                    </>
                  ) : (
                    <>
                      <Download className="mr-2 h-4 w-4" />
                      Por Departamento
                    </>
                  )}
                </Button>
                <Button
                  onClick={handleGenerateShoppingListReport}
                  disabled={isGeneratingPDF || lowStockProducts.length === 0}
                >
                  {isGeneratingPDF ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Gerando...
                    </>
                  ) : (
                    <>
                      <Download className="mr-2 h-4 w-4" />
                      Lista Completa
                    </>
                  )}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {lowStockProducts.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">Não há produtos com estoque baixo.</p>
                </div>
              ) : (
                <>
                  {/* Agrupar produtos por departamento */}
                  {INVENTORY_DEPARTMENTS.map(department => {
                    const departmentProducts = lowStockProducts.filter(
                      product => product.department === department
                    );

                    if (departmentProducts.length === 0) return null;

                    return (
                      <div key={department} className="mb-8">
                        <div className="flex justify-between items-center mb-4">
                          <h3 className="text-lg font-semibold">{department}</h3>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleGenerateShoppingListForDepartment(department)}
                            disabled={isGeneratingPDF}
                            className="h-8"
                          >
                            {isGeneratingPDF ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <>
                                <Download className="mr-2 h-4 w-4" />
                                PDF
                              </>
                            )}
                          </Button>
                        </div>

                        <div className="border rounded-md mb-6">
                          <table className="w-full">
                            <thead>
                              <tr className="border-b">
                                <th className="text-left p-3">Produto</th>
                                <th className="text-left p-3">Quantidade</th>
                                <th className="text-left p-3">Qtd. Mínima</th>
                                <th className="text-left p-3">Localização</th>
                              </tr>
                            </thead>
                            <tbody>
                              {departmentProducts.map(product => (
                                <tr key={product.id} className="border-b">
                                  <td className="p-3">{product.name}</td>
                                  <td className="p-3">{product.quantity} {product.unit_of_measure || 'unidade'}</td>
                                  <td className="p-3">{product.minimum_quantity || 0} {product.unit_of_measure || 'unidade'}</td>
                                  <td className="p-3">{product.location || "-"}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    );
                  })}

                  <div className="mt-6">
                    <h3 className="text-lg font-semibold mb-4">Todos os Produtos com Estoque Baixo</h3>
                    <DataTable
                      data={lowStockProducts}
                      columns={lowStockColumns}
                      actions={productActions}
                      isLoading={isLoading}
                    />
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>


      </Tabs>

      {/* Diálogos */}
      <CadastrarProdutoDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        product={selectedProduct}
        onSuccess={refreshProducts}
      />

      <MovimentarEstoqueDialog
        open={movimentarDialogOpen}
        onOpenChange={setMovimentarDialogOpen}
        product={selectedProduct}
        transactionType={transactionType}
        onSuccess={refreshProducts}
      />

      <SolicitacaoEstoqueDialog
        open={solicitacaoDialogOpen}
        onOpenChange={setSolicitacaoDialogOpen}
        request={selectedRequest}
        onSuccess={refreshRequests}
      />

      <ProcessarSolicitacaoDialog
        open={processarDialogOpen}
        onOpenChange={setProcessarDialogOpen}
        requestId={selectedRequest?.id || null}
        clubId={clubId}
        onSuccess={() => {
          refreshRequests();
          refreshProducts();
        }}
      />

      <DevolverItemDialog
        open={devolverItemDialogOpen}
        onOpenChange={setDevolverItemDialogOpen}
        item={selectedRequestItem}
        onSuccess={() => {
          if (selectedRequest) {
            loadRequestItems(selectedRequest.id);
          }
          refreshProducts();
        }}
      />

      <UploadImagemProdutoDialog
        open={uploadImageDialogOpen}
        onOpenChange={setUploadImageDialogOpen}
        productId={selectedProduct?.id || null}
        productName={selectedProductName}
        onSuccess={refreshProducts}
      />

      <ConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="Excluir Produto"
        description={`Tem certeza que deseja excluir o produto "${selectedProduct?.name}"? Esta ação não pode ser desfeita.`}
        onConfirm={async () => {
          try {
            if (selectedProduct) {
              await deleteInventoryProduct(clubId, selectedProduct.id, user?.id);
              toast({
                title: "Produto excluído",
                description: "O produto foi excluído com sucesso.",
              });
            }
            await refreshProducts();
          } catch (error) {
            console.error("Erro ao excluir produto:", error);
            toast({
              title: "Erro",
              description: "Ocorreu um erro ao excluir o produto. Tente novamente.",
              variant: "destructive",
            });
          }
        }}
      />

      <ConfirmDialog
        open={deleteRequestDialogOpen}
        onOpenChange={setDeleteRequestDialogOpen}
        title="Excluir Solicitação"
        description={`Tem certeza que deseja excluir a solicitação #${selectedRequest?.id}? Esta ação não pode ser desfeita.`}
        onConfirm={async () => {
          if (selectedRequest) {
            try {
              await deleteInventoryRequest(clubId, selectedRequest.id, user?.id);
              toast({
                title: "Solicitação excluída",
                description: "A solicitação foi excluída com sucesso.",
              });
              setSelectedRequest(null);
              setSelectedRequestItems([]);
              refreshRequests();
            } catch (error) {
              console.error("Erro ao excluir solicitação:", error);
              toast({
                title: "Erro",
                description: "Ocorreu um erro ao excluir a solicitação.",
                variant: "destructive",
              });
            }
          }
        }}
      />
    </div>
  );
}
