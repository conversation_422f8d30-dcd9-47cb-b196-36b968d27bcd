import { create } from "zustand";
import { 
  AdministrativeReminder, 
  getAdministrativeReminders, 
  createAdministrativeReminder, 
  updateAdministrativeReminder, 
  deleteAdministrativeReminder 
} from "@/api/api";

interface AdministrativeRemindersState {
  reminders: AdministrativeReminder[];
  loading: boolean;
  error: string | null;
  fetchReminders: (clubId: number) => Promise<void>;
  addReminder: (clubId: number, reminder: Omit<AdministrativeReminder, "id" | "created_at" | "updated_at">) => Promise<void>;
  updateReminder: (clubId: number, id: number, updates: Partial<AdministrativeReminder>) => Promise<void>;
  toggleReminderCompleted: (clubId: number, id: number, completed: boolean) => Promise<void>;
  removeReminder: (clubId: number, id: number) => Promise<void>;
  getActiveReminders: () => AdministrativeReminder[];
  getCompletedReminders: () => AdministrativeReminder[];
  getRemindersByType: (type: 'activity' | 'document' | 'email' | 'meeting') => AdministrativeReminder[];
}

export const useAdministrativeRemindersStore = create<AdministrativeRemindersState>((set, get) => ({
  reminders: [],
  loading: false,
  error: null,

  fetchReminders: async (clubId: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const reminders = await getAdministrativeReminders(clubId);
      set({ reminders, loading: false });
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao buscar lembretes", loading: false });
    }
  },

  addReminder: async (
    clubId: number, 
    reminder: Omit<AdministrativeReminder, "id" | "created_at" | "updated_at">
  ): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const newReminder = await createAdministrativeReminder(clubId, reminder);
      set((state) => ({ 
        reminders: [...state.reminders, newReminder], 
        loading: false 
      }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao adicionar lembrete", loading: false });
    }
  },

  updateReminder: async (
    clubId: number, 
    id: number, 
    updates: Partial<AdministrativeReminder>
  ): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const updatedReminder = await updateAdministrativeReminder(clubId, id, updates);
      set((state) => ({
        reminders: state.reminders.map(reminder => reminder.id === id ? updatedReminder : reminder),
        loading: false
      }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao atualizar lembrete", loading: false });
    }
  },

  toggleReminderCompleted: async (
    clubId: number, 
    id: number, 
    completed: boolean
  ): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const updatedReminder = await updateAdministrativeReminder(clubId, id, { completed });
      set((state) => ({
        reminders: state.reminders.map(reminder => reminder.id === id ? updatedReminder : reminder),
        loading: false
      }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao atualizar status do lembrete", loading: false });
    }
  },

  removeReminder: async (clubId: number, id: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      await deleteAdministrativeReminder(clubId, id);
      set((state) => ({ 
        reminders: state.reminders.filter(reminder => reminder.id !== id),
        loading: false 
      }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao remover lembrete", loading: false });
    }
  },

  getActiveReminders: (): AdministrativeReminder[] => {
    return get().reminders.filter(reminder => !reminder.completed);
  },

  getCompletedReminders: (): AdministrativeReminder[] => {
    return get().reminders.filter(reminder => reminder.completed);
  },

  getRemindersByType: (type: 'activity' | 'document' | 'email' | 'meeting'): AdministrativeReminder[] => {
    return get().reminders.filter(reminder => reminder.reminder_type === type);
  }
}));
