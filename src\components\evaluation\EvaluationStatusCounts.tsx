import { useState, useEffect } from "react";
import { getPlayersInEvaluation } from "@/api/playerEvaluationInvitations";
import { useCurrentClubId } from "@/context/ClubContext";
import { RefreshCw } from "lucide-react";

// Define player type
interface Player {
  id: string;
  name: string;
  position?: string;
  status: string;
  player_evaluation_invitations?: Array<{
    id: number;
    evaluation_date?: string;
    evaluation_location?: string;
    evaluation_requirements?: string;
    evaluation_notes?: string;
    documents_status?: "pending" | "approved" | "rejected";
    documents_verified_at?: string;
    documents_verified_by?: string;
    documents_rejection_reason?: string;
    evaluation_status?: "em avaliacao" | "aprovado" | "disponivel";
  }>;
}

export function EvaluationStatusCounts() {
  const clubId = useCurrentClubId();
  const [loading, setLoading] = useState(true);
  const [counts, setCounts] = useState({
    pendingDocuments: 0,
    awaitingSchedule: 0,
    scheduled: 0,
    approved: 0
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const players = await getPlayersInEvaluation(clubId);
        
        let pendingDocuments = 0;
        let awaitingSchedule = 0;
        let scheduled = 0;
        let approved = 0;

        players.forEach((player: Player) => {
          const invitation = player.player_evaluation_invitations?.[0];
          
          if (!invitation) {
            pendingDocuments++;
            return;
          }

          // Check evaluation status first
          if (invitation.evaluation_status === "disponivel") {
            approved++;
            return;
          } else if (invitation.evaluation_status === "aprovado") {
            approved++;
            return;
          }

          // Check document status
          if (invitation.documents_status === "pending" || !invitation.documents_status) {
            pendingDocuments++;
            return;
          } else if (invitation.documents_status === "rejected") {
            pendingDocuments++;
            return;
          } else if (invitation.documents_status === "approved") {
            // If documents are approved but no evaluation date
            if (!invitation.evaluation_date) {
              awaitingSchedule++;
              return;
            }
          }

          // Check evaluation date
          if (invitation.evaluation_date) {
            const evaluationDate = new Date(invitation.evaluation_date);
            const now = new Date();

            if (evaluationDate > now) {
              scheduled++;
            } else {
              // Already evaluated but not approved yet
              awaitingSchedule++;
            }
          }
        });

        setCounts({
          pendingDocuments,
          awaitingSchedule,
          scheduled,
          approved
        });
      } catch (error) {
        console.error("Error fetching evaluation data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [clubId]);

  if (loading) {
    return (
      <>
        <div className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-md">
          <RefreshCw className="h-5 w-5 animate-spin text-gray-600" />
          <span className="text-xs text-gray-600 mt-2">Carregando...</span>
        </div>
        <div className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-md">
          <RefreshCw className="h-5 w-5 animate-spin text-gray-600" />
          <span className="text-xs text-gray-600 mt-2">Carregando...</span>
        </div>
        <div className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-md">
          <RefreshCw className="h-5 w-5 animate-spin text-gray-600" />
          <span className="text-xs text-gray-600 mt-2">Carregando...</span>
        </div>
        <div className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-md">
          <RefreshCw className="h-5 w-5 animate-spin text-gray-600" />
          <span className="text-xs text-gray-600 mt-2">Carregando...</span>
        </div>
      </>
    );
  }

  return (
    <>
      <div className="flex flex-col items-center justify-center p-4 bg-yellow-50 rounded-md">
        <span className="text-2xl font-bold text-yellow-600">{counts.pendingDocuments}</span>
        <span className="text-xs text-yellow-600">Documentos Pendentes</span>
      </div>
      <div className="flex flex-col items-center justify-center p-4 bg-red-50 rounded-md">
        <span className="text-2xl font-bold text-red-600">{counts.awaitingSchedule}</span>
        <span className="text-xs text-red-600">Aguardando Agendamento</span>
      </div>
      <div className="flex flex-col items-center justify-center p-4 bg-blue-50 rounded-md">
        <span className="text-2xl font-bold text-blue-600">{counts.scheduled}</span>
        <span className="text-xs text-blue-600">Agendados</span>
      </div>
      <div className="flex flex-col items-center justify-center p-4 bg-green-50 rounded-md">
        <span className="text-2xl font-bold text-green-600">{counts.approved}</span>
        <span className="text-xs text-green-600">Aprovados</span>
      </div>
    </>
  );
}
