import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { usePlayersStore } from "@/store/usePlayersStore";
import { useCurrentClubId } from "@/context/ClubContext";
import { useMemo } from "react";
import { Button } from "@/components/ui/button";
import { ChevronRight } from "lucide-react";
// import { useRouter } from "next/router";

export function SquadStatus() {
  const clubId = useCurrentClubId();
  const { players, loading } = usePlayersStore();
  // const router = useRouter();

  // Ordenar e limitar jogadores para exibição
  const displayPlayers = useMemo(() => {
    // Primeiro ordenar por status (disponível primeiro, depois em recuperação, depois lesionado)
    // Depois por condição física (maior para menor)
    return [...players]
      .sort((a, b) => {
        // Primeiro por status
        if (a.status !== b.status) {
          if (a.status === "disponível") return -1;
          if (b.status === "disponível") return 1;
          if (a.status === "em recuperação") return -1;
          if (b.status === "em recuperação") return 1;
        }

        // Depois por condição física
        const fitnessA = a.stats?.minutes || 0;
        const fitnessB = b.stats?.minutes || 0;
        return fitnessB - fitnessA;
      })
      .slice(0, 10); // Limitar a 10 jogadores
  }, [players]);

  // Média de condição física
  const fitnessValues = players.map((p) => p.stats?.minutes ? p.stats.minutes : null).filter((v): v is number => v !== null);
  const avgFitness = fitnessValues.length > 0 ? Math.round(fitnessValues.reduce((a, b) => a + b, 0) / fitnessValues.length) : 0;

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Status do Elenco</CardTitle>
        {players.length > 10 && (
          <Button
            variant="ghost"
            size="sm"
            className="text-xs flex items-center"
            // onClick={() => router.push('/elenco')}
          >
            Ver todos <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        )}
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {loading ? (
            <div>Carregando jogadores...</div>
          ) : players.length === 0 ? (
            <div className="text-muted-foreground text-center">Nenhum jogador cadastrado no clube.</div>
          ) : (
            displayPlayers.map((player) => (
              <div key={player.id} className="space-y-1">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-sm">{player.name}</p>
                    <p className="text-xs text-muted-foreground">{player.position}</p>
                  </div>
                  <div
                    className={`text-xs px-2 py-1 rounded-full ${
                      player.status === "disponível"
                        ? "bg-green-100 text-green-800"
                        : player.status === "em recuperação"
                        ? "bg-amber-100 text-amber-800"
                        : player.status === "lesionado"
                        ? "bg-rose-100 text-rose-800"
                        : player.status === "suspenso"
                        ? "bg-orange-100 text-orange-800"
                        : player.status === "inativo"
                        ? "bg-gray-100 text-gray-800"
                        : player.status === "emprestado"
                        ? "bg-blue-100 text-blue-800"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {player.status}
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <p className="text-xs font-medium">Condição física</p>
                  <p className="text-xs font-medium">{player.stats?.minutes ?? "Sem dados"}%</p>
                </div>
                <Progress
                  value={typeof player.stats?.minutes === "number" ? player.stats.minutes : 0}
                  className={`h-2 ${
                    typeof player.stats?.minutes === "number" && player.stats.minutes > 80
                      ? "bg-muted text-team-green"
                      : typeof player.stats?.minutes === "number" && player.stats.minutes > 60
                      ? "bg-muted text-amber-500"
                      : "bg-muted text-rose-500"
                  }`}
                />
              </div>
            ))
          )}
          <div className="mt-4 text-xs text-muted-foreground text-center">
            Média de condição física: <span className="font-bold">{avgFitness}%</span>
            {players.length > 10 && (
              <span className="ml-2 text-xs text-muted-foreground">
                (Mostrando 10 de {players.length} jogadores)
              </span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
