-- Configurar políticas de acesso para permitir exclusão de arquivos no bucket playerdocuments
INSERT INTO storage.policies (name, definition, bucket_id, operation)
VALUES (
  'Authenticated Users can delete from playerdocuments',
  '(bucket_id = ''playerdocuments''::text) AND (auth.role() = ''authenticated''::text)',
  'playerdocuments',
  'DELETE'
);

-- Configurar políticas de acesso para permitir exclusão de arquivos no bucket profileimages
INSERT INTO storage.policies (name, definition, bucket_id, operation)
VALUES (
  'Authenticated Users can delete from profileimages',
  '(bucket_id = ''profileimages''::text) AND (auth.role() = ''authenticated''::text)',
  'profileimages',
  'DELETE'
);

-- Configurar políticas de acesso para permitir atualização de arquivos no bucket playerdocuments
INSERT INTO storage.policies (name, definition, bucket_id, operation)
VALUES (
  'Authenticated Users can update playerdocuments',
  '(bucket_id = ''playerdocuments''::text) AND (auth.role() = ''authenticated''::text)',
  'playerdocuments',
  'UPDATE'
);

-- Configurar políticas de acesso para permitir atualização de arquivos no bucket profileimages
INSERT INTO storage.policies (name, definition, bucket_id, operation)
VALUES (
  'Authenticated Users can update profileimages',
  '(bucket_id = ''profileimages''::text) AND (auth.role() = ''authenticated''::text)',
  'profileimages',
  'UPDATE'
);
