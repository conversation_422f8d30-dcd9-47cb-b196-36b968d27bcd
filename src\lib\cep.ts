/**
 * Interface for address data returned by the CEP API
 */
export interface AddressData {
  cep: string;
  state: string;
  city: string;
  neighborhood: string;
  street: string;
}

/**
 * Fetch address data from a CEP (Brazilian postal code)
 * @param cep CEP to fetch address data for
 * @returns Address data or null if not found
 */
export async function fetchAddressByCEP(cep: string): Promise<AddressData | null> {
  try {
    // Remove any non-numeric characters
    const cleanCep = cep.replace(/\D/g, '');
    
    if (cleanCep.length !== 8) {
      throw new Error('CEP inválido');
    }
    
    // Use the ViaCEP API to fetch address data
    const response = await fetch(`https://viacep.com.br/ws/${cleanCep}/json/`);
    const data = await response.json();
    
    if (data.erro) {
      throw new Error('CEP não encontrado');
    }
    
    return {
      cep: data.cep,
      state: data.uf,
      city: data.localidade,
      neighborhood: data.bairro,
      street: data.logradouro
    };
  } catch (error) {
    console.error('Error fetching address by CEP:', error);
    return null;
  }
}
