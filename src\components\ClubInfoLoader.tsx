import { useEffect } from "react";
import { useCurrentClubId } from "@/context/ClubContext";
import { useClubInfoStore } from "@/store/useClubInfoStore";

/**
 * Componente que carrega as informações do clube automaticamente
 * quando o usuário está autenticado e tem um clube selecionado.
 * 
 * Este componente não renderiza nada visualmente, apenas executa
 * a lógica de carregamento das informações do clube.
 */
export function ClubInfoLoader() {
  const clubId = useCurrentClubId();
  const { fetchClubInfo } = useClubInfoStore();

  // Carregar informações do clube quando o componente montar
  useEffect(() => {
    if (clubId) {
      console.log("ClubInfoLoader: Carregando informações do clube", clubId);
      fetchClubInfo(clubId);
    }
  }, [clubId, fetchClubInfo]);

  // Este componente não renderiza nada visualmente
  return null;
}
