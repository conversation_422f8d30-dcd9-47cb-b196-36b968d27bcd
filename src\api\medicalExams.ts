import { supabase } from "@/integrations/supabase/client";
import { withPermission, withAuditLog } from "./middleware";
import { uploadExamFile } from "./storage";

// Types
export type MedicalExam = {
  id: number;
  club_id: number;
  record_id: number;
  player_id: string;
  exam_type: string;
  request_date: string;
  exam_date?: string;
  result?: string;
  file_url?: string;
  requested_by: number;
  status: ExamStatus;
  notes?: string;
  created_at: string;
  updated_at: string;
};

export type ExamStatus = 
  | "Solicitado" 
  | "Agendado" 
  | "Realizado" 
  | "Cancelado";

export const EXAM_PERMISSIONS = {
  VIEW: "medical.exams.view",
  CREATE: "medical.exams.create",
  EDIT: "medical.exams.edit",
  DELETE: "medical.exams.delete",
};

/**
 * Get all medical exams for a record
 * @param clubId The club ID
 * @param recordId The medical record ID
 * @returns Array of medical exams
 */
export async function getMedicalExams(
  clubId: number,
  recordId: number
): Promise<MedicalExam[]> {
  try {
    const { data, error } = await supabase
      .from("medical_exams")
      .select("*")
      .eq("club_id", clubId)
      .eq("record_id", recordId)
      .order("request_date", { ascending: false });

    if (error) {
      throw new Error(`Error fetching medical exams: ${error.message}`);
    }

    return data as MedicalExam[];
  } catch (error: any) {
    console.error("Error in getMedicalExams:", error);
    throw new Error(error.message || "Failed to fetch medical exams");
  }
}

/**
 * Get all medical exams for a player
 * @param clubId The club ID
 * @param playerId The player ID
 * @returns Array of medical exams
 */
export async function getPlayerMedicalExams(
  clubId: number,
  playerId: string
): Promise<MedicalExam[]> {
  try {
    const { data, error } = await supabase
      .from("medical_exams")
      .select("*")
      .eq("club_id", clubId)
      .eq("player_id", playerId)
      .order("request_date", { ascending: false });

    if (error) {
      throw new Error(`Error fetching player medical exams: ${error.message}`);
    }

    return data as MedicalExam[];
  } catch (error: any) {
    console.error("Error in getPlayerMedicalExams:", error);
    throw new Error(error.message || "Failed to fetch player medical exams");
  }
}

/**
 * Create a new medical exam
 * @param clubId The club ID
 * @param userId The user ID creating the exam
 * @param exam The exam data
 * @returns The created exam
 */
export async function createMedicalExam(
  clubId: number,
  userId: string,
  exam: Omit<MedicalExam, "id" | "club_id" | "created_at" | "updated_at">
): Promise<MedicalExam> {
  return withPermission(
    clubId,
    userId,
    EXAM_PERMISSIONS.CREATE,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "medical.exam.create",
        { 
          record_id: exam.record_id,
          player_id: exam.player_id,
          exam_type: exam.exam_type
        },
        async () => {
          try {
            const { data, error } = await supabase
              .from("medical_exams")
              .insert({
                club_id: clubId,
                ...exam,
                updated_at: new Date().toISOString()
              })
              .select()
              .single();

            if (error) {
              throw new Error(`Error creating medical exam: ${error.message}`);
            }

            return data as MedicalExam;
          } catch (error: any) {
            console.error("Error in createMedicalExam:", error);
            throw new Error(error.message || "Failed to create medical exam");
          }
        }
      );
    }
  );
}

/**
 * Update an existing medical exam
 * @param clubId The club ID
 * @param userId The user ID updating the exam
 * @param id The exam ID
 * @param exam The updated exam data
 * @returns The updated exam
 */
export async function updateMedicalExam(
  clubId: number,
  userId: string,
  id: number,
  exam: Partial<Omit<MedicalExam, "id" | "club_id" | "created_at" | "updated_at">>
): Promise<MedicalExam> {
  return withPermission(
    clubId,
    userId,
    EXAM_PERMISSIONS.EDIT,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "medical.exam.update",
        { id, ...exam },
        async () => {
          try {
            const { data, error } = await supabase
              .from("medical_exams")
              .update({
                ...exam,
                updated_at: new Date().toISOString()
              })
              .eq("club_id", clubId)
              .eq("id", id)
              .select()
              .single();

            if (error) {
              throw new Error(`Error updating medical exam: ${error.message}`);
            }

            return data as MedicalExam;
          } catch (error: any) {
            console.error("Error in updateMedicalExam:", error);
            throw new Error(error.message || "Failed to update medical exam");
          }
        }
      );
    }
  );
}

/**
 * Delete a medical exam
 * @param clubId The club ID
 * @param userId The user ID deleting the exam
 * @param id The exam ID
 * @returns True if successful
 */
export async function deleteMedicalExam(
  clubId: number,
  userId: string,
  id: number
): Promise<boolean> {
  return withPermission(
    clubId,
    userId,
    EXAM_PERMISSIONS.DELETE,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "medical.exam.delete",
        { id },
        async () => {
          try {
            const { error } = await supabase
              .from("medical_exams")
              .delete()
              .eq("club_id", clubId)
              .eq("id", id);

            if (error) {
              throw new Error(`Error deleting medical exam: ${error.message}`);
            }

            return true;
          } catch (error: any) {
            console.error("Error in deleteMedicalExam:", error);
            throw new Error(error.message || "Failed to delete medical exam");
          }
        }
      );
    }
  );
}

/**
 * Upload an exam file
 * @param clubId The club ID
 * @param userId The user ID uploading the file
 * @param examId The exam ID
 * @param file The file to upload
 * @returns The URL of the uploaded file
 */
export async function uploadExam(
  clubId: number,
  userId: string,
  examId: number,
  file: File
): Promise<string> {
  return withPermission(
    clubId,
    userId,
    EXAM_PERMISSIONS.EDIT,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "medical.exam.upload",
        { examId, fileName: file.name },
        async () => {
          try {
            // Upload the file
            const fileUrl = await uploadExamFile(clubId, examId, file);

            // Update the exam with the file URL
            const { error } = await supabase
              .from("medical_exams")
              .update({ 
                file_url: fileUrl,
                updated_at: new Date().toISOString()
              })
              .eq("club_id", clubId)
              .eq("id", examId);

            if (error) {
              throw new Error(`Error updating exam with file URL: ${error.message}`);
            }

            return fileUrl;
          } catch (error: any) {
            console.error("Error in uploadExam:", error);
            throw new Error(error.message || "Failed to upload exam file");
          }
        }
      );
    }
  );
}
