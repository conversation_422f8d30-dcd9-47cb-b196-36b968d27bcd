import { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  Card<PERSON><PERSON>er,
  CardTitle,
  CardDescription
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/components/ui/use-toast";
import {
  Plus,
  FileText,
  Pencil,
  Trash2,
  <PERSON><PERSON>,
  Printer,
  CheckCircle2,
  PenLine,
  Filter
} from "lucide-react";
import { AdministrativeDocument } from "@/api/api";
import { useAdministrativeDocumentsStore } from "@/store/useAdministrativeDocumentsStore";
import { useUser } from "@/context/UserContext";
import { NovoDocumentoDialog } from "@/components/administrativo/NovoDocumentoDialog";
import { EditarDocumentoDialog } from "@/components/administrativo/EditarDocumentoDialog";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import { DocumentoPreviewDialog } from "@/components/administrativo/DocumentoPreviewDialog";
import { AssinaturaDigitalDialog } from "@/components/administrativo/AssinaturaDigitalDialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface OficiosTabProps {
  documents: AdministrativeDocument[];
  loading: boolean;
  error: string | null;
  clubId: number;
}

export function OficiosTab({ documents, loading, error, clubId }: OficiosTabProps) {
  const [novoDocumentoDialogOpen, setNovoDocumentoDialogOpen] = useState(false);
  const [editarDocumentoDialogOpen, setEditarDocumentoDialogOpen] = useState(false);
  const [excluirDocumentoDialogOpen, setExcluirDocumentoDialogOpen] = useState(false);
  const [previewDocumentoDialogOpen, setPreviewDocumentoDialogOpen] = useState(false);
  const [assinaturaDialogOpen, setAssinaturaDialogOpen] = useState(false);
  const [documentoParaImprimir, setDocumentoParaImprimir] = useState<AdministrativeDocument | null>(null);
  const [documentoParaExcluir, setDocumentoParaExcluir] = useState<AdministrativeDocument | null>(null);
  const [documentoParaAssinar, setDocumentoParaAssinar] = useState<AdministrativeDocument | null>(null);
  const [selectedMonth, setSelectedMonth] = useState<string>("all");
  const [selectedYear, setSelectedYear] = useState<string>("all");

  const { user } = useUser();
  const {
    selectedDocument,
    setSelectedDocument,
    removeDocument,
    signDocument,
    generatePDF
  } = useAdministrativeDocumentsStore();

  const handleEditDocument = (document: AdministrativeDocument) => {
    setSelectedDocument(document);
    setEditarDocumentoDialogOpen(true);
  };

  const handleDeleteDocument = (document: AdministrativeDocument) => {
    setDocumentoParaExcluir(document);
    setExcluirDocumentoDialogOpen(true);
  };

  const confirmDeleteDocument = async () => {
    if (documentoParaExcluir) {
      try {
        await removeDocument(clubId, documentoParaExcluir.id);
        toast({
          title: "Documento excluído",
          description: "O documento foi excluído com sucesso.",
        });
      } catch (error) {
        toast({
          title: "Erro ao excluir documento",
          description: "Ocorreu um erro ao excluir o documento.",
          variant: "destructive",
        });
      }
    }
    setExcluirDocumentoDialogOpen(false);
  };

  const handleDuplicateDocument = (document: AdministrativeDocument) => {
    setSelectedDocument({
      ...document,
      id: 0,
      document_number: 0,
      title: `Cópia de ${document.title}`,
      digital_signature: false,
      signed_by: undefined,
      signed_at: undefined,
    });
    setNovoDocumentoDialogOpen(true);
  };

  const handlePrintDocument = async (document: AdministrativeDocument) => {
    setDocumentoParaImprimir(document);
    setPreviewDocumentoDialogOpen(true);
  };

  const handleSignDocument = (document: AdministrativeDocument) => {
    setDocumentoParaAssinar(document);
    setAssinaturaDialogOpen(true);
  };

  // Get available years from documents
  const getAvailableYears = () => {
    const years = new Set<string>();
    documents.forEach(doc => {
      const year = new Date(doc.created_at).getFullYear().toString();
      years.add(year);
    });
    return Array.from(years).sort((a, b) => b.localeCompare(a)); // Sort descending
  };

  // Get available months (1-12)
  const getMonths = () => {
    return [
      { value: "1", label: "Janeiro" },
      { value: "2", label: "Fevereiro" },
      { value: "3", label: "Março" },
      { value: "4", label: "Abril" },
      { value: "5", label: "Maio" },
      { value: "6", label: "Junho" },
      { value: "7", label: "Julho" },
      { value: "8", label: "Agosto" },
      { value: "9", label: "Setembro" },
      { value: "10", label: "Outubro" },
      { value: "11", label: "Novembro" },
      { value: "12", label: "Dezembro" }
    ];
  };

  // Filter documents by month and year
  const filteredDocuments = documents.filter(doc => {
    const docDate = new Date(doc.created_at);
    const docMonth = (docDate.getMonth() + 1).toString(); // getMonth() returns 0-11
    const docYear = docDate.getFullYear().toString();

    const monthMatch = selectedMonth === "all" || docMonth === selectedMonth;
    const yearMatch = selectedYear === "all" || docYear === selectedYear;

    return monthMatch && yearMatch;
  });

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Ofícios e Memorandos</CardTitle>
          <CardDescription>
            Gerencie documentos oficiais do clube
          </CardDescription>
        </div>
        <Button onClick={() => {
          setSelectedDocument(null);
          setNovoDocumentoDialogOpen(true);
        }}>
          <Plus className="h-4 w-4 mr-2" />
          Novo Documento
        </Button>
      </CardHeader>
      <CardContent>
        <div className="mb-4 flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Filtrar por:</span>
          </div>

          <div className="flex gap-2">
            <Select
              value={selectedMonth}
              onValueChange={setSelectedMonth}
            >
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Mês" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os meses</SelectItem>
                {getMonths().map(month => (
                  <SelectItem key={month.value} value={month.value}>
                    {month.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select
              value={selectedYear}
              onValueChange={setSelectedYear}
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Ano" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os anos</SelectItem>
                {getAvailableYears().map(year => (
                  <SelectItem key={year} value={year}>
                    {year}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {(selectedMonth !== "all" || selectedYear !== "all") && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setSelectedMonth("all");
                  setSelectedYear("all");
                }}
              >
                Limpar filtros
              </Button>
            )}
          </div>
        </div>

        {loading ? (
          <div className="text-center py-4">Carregando documentos...</div>
        ) : error ? (
          <div className="text-center py-4 text-red-500">{error}</div>
        ) : documents.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            Nenhum documento encontrado. Clique em "Novo Documento" para criar.
          </div>
        ) : filteredDocuments.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            Nenhum documento encontrado com os filtros selecionados.
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nº</TableHead>
                <TableHead>Tipo</TableHead>
                <TableHead>Título</TableHead>
                <TableHead>Data</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredDocuments.map((document) => (
                <TableRow key={document.id}>
                  <TableCell>{document.document_number}</TableCell>
                  <TableCell>
                    {document.document_type === 'oficio' ? 'Ofício' : 'Memorando'}
                  </TableCell>
                  <TableCell>{document.title}</TableCell>
                  <TableCell>
                    {new Date(document.created_at).toLocaleDateString('pt-BR')}
                  </TableCell>
                  <TableCell>
                    {document.digital_signature ? (
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        Assinado
                      </Badge>
                    ) : (
                      <Badge variant="outline">Não assinado</Badge>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handlePrintDocument(document)}
                        title="Imprimir"
                      >
                        <Printer className="h-4 w-4" />
                      </Button>
                      {!document.digital_signature && (
                        <>
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => handleEditDocument(document)}
                            title="Editar"
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => handleSignDocument(document)}
                            title="Assinar Digitalmente"
                          >
                            <PenLine className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handleDuplicateDocument(document)}
                        title="Duplicar"
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handleDeleteDocument(document)}
                        title="Excluir"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>

      <NovoDocumentoDialog
        open={novoDocumentoDialogOpen}
        onOpenChange={setNovoDocumentoDialogOpen}
        clubId={clubId}
        initialData={selectedDocument}
      />

      <EditarDocumentoDialog
        open={editarDocumentoDialogOpen}
        onOpenChange={setEditarDocumentoDialogOpen}
        clubId={clubId}
        document={selectedDocument}
      />

      <ConfirmDialog
        open={excluirDocumentoDialogOpen}
        onOpenChange={setExcluirDocumentoDialogOpen}
        title="Excluir documento"
        description="Tem certeza que deseja excluir este documento? Esta ação não pode ser desfeita."
        onConfirm={confirmDeleteDocument}
      />

      <DocumentoPreviewDialog
        open={previewDocumentoDialogOpen}
        onOpenChange={setPreviewDocumentoDialogOpen}
        document={documentoParaImprimir}
        clubId={clubId}
      />

      <AssinaturaDigitalDialog
        open={assinaturaDialogOpen}
        onOpenChange={setAssinaturaDialogOpen}
        document={documentoParaAssinar}
        clubId={clubId}
        onSuccess={() => {
          // Refresh documents if needed
        }}
      />
    </Card>
  );
}
