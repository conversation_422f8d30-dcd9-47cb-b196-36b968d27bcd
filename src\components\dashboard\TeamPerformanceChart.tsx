import { useEffect, useState } from "react";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON>,
} from "recharts";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

// Adicionando tipagem para receber dados reais
interface TeamPerformanceChartProps {
  players?: any[];
  matchHistory?: any[];
}

export function TeamPerformanceChart({ players = [], matchHistory = [] }: TeamPerformanceChartProps) {
  // Exemplo: calcular médias de atributos dos jogadores por dia da semana
  // Aqui você pode adaptar para usar dados reais do matchHistory, treinos, etc.
  const [data, setData] = useState<any[]>([]);

  useEffect(() => {
    // Se não houver jogadores ou histórico, não gera dados
    if (!players.length && !matchHistory.length) {
      setData([]);
      return;
    }
    // Exemplo: gerar dados fictícios baseados em jogadores reais
    // Substitua por lógica real conforme necessário
    const days = ["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"];
    const chartData = days.map((day) => ({
      day,
      físico: players.length > 0 ? Math.round(players.reduce((a, b) => a + (b.stats?.minutes || 0), 0) / players.length) : 0,
      tático: matchHistory.length > 0 ? Math.round(matchHistory.reduce((a, b) => a + (b.estatisticas?.chutesNoGol || 0), 0) / matchHistory.length) : 0,
      mental: players.length > 0 ? Math.round(players.reduce((a, b) => a + (b.stats?.goals || 0), 0) / players.length) : 0,
    }));
    setData(chartData);
  }, [players, matchHistory]);

  const hasData = data.some((d) => d.físico > 0 || d.tático > 0 || d.mental > 0);

  return (
    <Card className="col-span-2">
      <CardHeader>
        <CardTitle>Desempenho da Equipe</CardTitle>
        <CardDescription>Análise semanal por categoria de performance</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          {hasData ? (
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={data}
                margin={{
                  top: 5,
                  right: 30,
                  left: 0,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="físico"
                  stackId="1"
                  stroke="#0c4da2"
                  fill="#0c4da2"
                  fillOpacity={0.6}
                />
                <Area
                  type="monotone"
                  dataKey="tático"
                  stackId="1"
                  stroke="#0db14b"
                  fill="#0db14b"
                  fillOpacity={0.6}
                />
                <Area
                  type="monotone"
                  dataKey="mental"
                  stackId="1"
                  stroke="#fbbf24"
                  fill="#fbbf24"
                  fillOpacity={0.6}
                />
              </AreaChart>
            </ResponsiveContainer>
          ) : (
            <div className="h-full flex items-center justify-center text-muted-foreground">
              Sem dados suficientes para exibir o gráfico.
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
