-- Criar função RPC para verificar documentos de colaboradores
CREATE OR REPLACE FUNCTION verify_collaborator_document(
  p_document_id INTEGER,
  p_verified_by UUID,
  p_status TEXT,
  p_rejection_reason TEXT DEFAULT NULL
) RETURNS VOID AS $$
BEGIN
  UPDATE collaborator_documents
  SET 
    status = p_status,
    verified_at = NOW(),
    verified_by = p_verified_by,
    rejection_reason = CASE WHEN p_status = 'rejected' THEN p_rejection_reason ELSE NULL END
  WHERE id = p_document_id;
END;
$$ LANGUAGE plpgsql;
