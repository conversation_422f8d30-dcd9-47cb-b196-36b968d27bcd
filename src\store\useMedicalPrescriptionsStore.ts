import { create } from "zustand";
import {
  MedicalPrescription,
  PrescriptionItem,
  getMedicalPrescriptions,
  getPlayerPrescriptions,
  getPrescriptionItems,
  createMedicalPrescription,
  addPrescriptionItem,
  updateMedicalPrescription,
  deleteMedicalPrescription,
  addPrescriptionSignature,
  PrescriptionStatus
} from "@/api/api";

interface MedicalPrescriptionsState {
  prescriptions: MedicalPrescription[];
  prescriptionItems: Record<number, PrescriptionItem[]>;
  loading: boolean;
  error: string | null;
  fetchPrescriptions: (clubId: number, recordId: number) => Promise<void>;
  fetchPlayerPrescriptions: (clubId: number, playerId: string) => Promise<void>;
  fetchPrescriptionItems: (clubId: number, prescriptionId: number) => Promise<void>;
  addPrescription: (
    clubId: number,
    userId: string,
    prescription: Omit<MedicalPrescription, "id" | "club_id" | "created_at" | "signature_url">
  ) => Promise<MedicalPrescription | null>;
  addItem: (
    clubId: number,
    userId: string,
    item: Omit<PrescriptionItem, "id" | "club_id" | "created_at" | "dispensed" | "dispensed_at" | "dispensed_by">
  ) => Promise<void>;
  updatePrescription: (
    clubId: number,
    userId: string,
    id: number,
    prescription: Partial<Omit<MedicalPrescription, "id" | "club_id" | "created_at">>
  ) => Promise<void>;
  deletePrescription: (clubId: number, userId: string, id: number) => Promise<void>;
  addSignature: (clubId: number, userId: string, prescriptionId: number, signatureFile: File) => Promise<void>;
}

export const useMedicalPrescriptionsStore = create<MedicalPrescriptionsState>((set, get) => ({
  prescriptions: [],
  prescriptionItems: {},
  loading: false,
  error: null,

  fetchPrescriptions: async (clubId, recordId) => {
    set({ loading: true, error: null });
    try {
      const prescriptions = await getMedicalPrescriptions(clubId, recordId);
      set({ prescriptions, loading: false });
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao buscar prescrições médicas",
        loading: false,
      });
    }
  },

  fetchPlayerPrescriptions: async (clubId, playerId) => {
    set({ loading: true, error: null });
    try {
      const prescriptions = await getPlayerPrescriptions(clubId, playerId);
      set({ prescriptions, loading: false });
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao buscar prescrições do jogador",
        loading: false,
      });
    }
  },

  fetchPrescriptionItems: async (clubId, prescriptionId) => {
    set({ loading: true, error: null });
    try {
      const items = await getPrescriptionItems(clubId, prescriptionId);
      set((state) => ({
        prescriptionItems: {
          ...state.prescriptionItems,
          [prescriptionId]: items,
        },
        loading: false,
      }));
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao buscar itens da prescrição",
        loading: false,
      });
    }
  },

  addPrescription: async (clubId, userId, prescription) => {
    set({ loading: true, error: null });
    try {
      const newPrescription = await createMedicalPrescription(clubId, userId, prescription);
      set((state) => ({
        prescriptions: [...state.prescriptions, newPrescription],
        loading: false,
      }));
      return newPrescription;
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao adicionar prescrição médica",
        loading: false,
      });
      return null;
    }
  },

  addItem: async (clubId, userId, item) => {
    set({ loading: true, error: null });
    try {
      const newItem = await addPrescriptionItem(clubId, userId, item);
      set((state) => {
        const currentItems = state.prescriptionItems[item.prescription_id] || [];
        return {
          prescriptionItems: {
            ...state.prescriptionItems,
            [item.prescription_id]: [...currentItems, newItem],
          },
          loading: false,
        };
      });
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao adicionar item à prescrição",
        loading: false,
      });
    }
  },

  updatePrescription: async (clubId, userId, id, prescription) => {
    set({ loading: true, error: null });
    try {
      const updated = await updateMedicalPrescription(clubId, userId, id, prescription);
      set((state) => ({
        prescriptions: state.prescriptions.map((p) => (p.id === id ? updated : p)),
        loading: false,
      }));
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao atualizar prescrição médica",
        loading: false,
      });
    }
  },

  deletePrescription: async (clubId, userId, id) => {
    set({ loading: true, error: null });
    try {
      await deleteMedicalPrescription(clubId, userId, id);
      set((state) => {
        const { [id]: _, ...remainingItems } = state.prescriptionItems;
        return {
          prescriptions: state.prescriptions.filter((p) => p.id !== id),
          prescriptionItems: remainingItems,
          loading: false,
        };
      });
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao excluir prescrição médica",
        loading: false,
      });
    }
  },

  addSignature: async (clubId, userId, prescriptionId, signatureFile) => {
    set({ loading: true, error: null });
    try {
      const signatureUrl = await addPrescriptionSignature(clubId, userId, prescriptionId, signatureFile);
      set((state) => ({
        prescriptions: state.prescriptions.map((p) =>
          p.id === prescriptionId ? { ...p, signature_url: signatureUrl } : p
        ),
        loading: false,
      }));
    } catch (err: unknown) {
      set({
        error: err instanceof Error ? err.message : "Erro ao adicionar assinatura",
        loading: false,
      });
    }
  },
}));
