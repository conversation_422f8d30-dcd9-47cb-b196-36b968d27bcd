import { supabase } from "@/integrations/supabase/client";
import { withPermission, withAuditLog } from "./middleware";
import { uploadSignature } from "./storage";

// Types
export type MedicalPrescription = {
  id: number;
  club_id: number;
  record_id: number;
  player_id: string;
  professional_id: number;
  issue_date: string;
  expiration_date?: string;
  status: PrescriptionStatus;
  notes?: string;
  created_at: string;
  signature_url?: string;
};

export type PrescriptionItem = {
  id: number;
  club_id: number;
  prescription_id: number;
  product_id: number;
  dosage: string;
  frequency: string;
  duration?: string;
  instructions?: string;
  quantity: number;
  dispensed: boolean;
  dispensed_at?: string;
  dispensed_by?: string;
  created_at: string;
};

export type PrescriptionStatus = 
  | "Ativo" 
  | "Dispensado" 
  | "Expirado" 
  | "Cancelado";

export const PRESCRIPTION_PERMISSIONS = {
  VIEW: "medical.prescriptions.view",
  CREATE: "medical.prescriptions.create",
  EDIT: "medical.prescriptions.edit",
  DELETE: "medical.prescriptions.delete",
  DISPENSE: "medical.prescriptions.dispense",
};

/**
 * Get all prescriptions for a medical record
 * @param clubId The club ID
 * @param recordId The medical record ID
 * @returns Array of prescriptions
 */
export async function getMedicalPrescriptions(
  clubId: number,
  recordId: number
): Promise<MedicalPrescription[]> {
  try {
    const { data, error } = await supabase
      .from("medical_prescriptions")
      .select("*")
      .eq("club_id", clubId)
      .eq("record_id", recordId)
      .order("issue_date", { ascending: false });

    if (error) {
      throw new Error(`Error fetching medical prescriptions: ${error.message}`);
    }

    return data as MedicalPrescription[];
  } catch (error: any) {
    console.error("Error in getMedicalPrescriptions:", error);
    throw new Error(error.message || "Failed to fetch medical prescriptions");
  }
}

/**
 * Get all prescriptions for a player
 * @param clubId The club ID
 * @param playerId The player ID
 * @returns Array of prescriptions
 */
export async function getPlayerPrescriptions(
  clubId: number,
  playerId: string
): Promise<MedicalPrescription[]> {
  try {
    const { data, error } = await supabase
      .from("medical_prescriptions")
      .select("*")
      .eq("club_id", clubId)
      .eq("player_id", playerId)
      .order("issue_date", { ascending: false });

    if (error) {
      throw new Error(`Error fetching player prescriptions: ${error.message}`);
    }

    return data as MedicalPrescription[];
  } catch (error: any) {
    console.error("Error in getPlayerPrescriptions:", error);
    throw new Error(error.message || "Failed to fetch player prescriptions");
  }
}

/**
 * Get prescription items for a prescription
 * @param clubId The club ID
 * @param prescriptionId The prescription ID
 * @returns Array of prescription items
 */
export async function getPrescriptionItems(
  clubId: number,
  prescriptionId: number
): Promise<PrescriptionItem[]> {
  try {
    const { data, error } = await supabase
      .from("medical_prescription_items")
      .select("*")
      .eq("club_id", clubId)
      .eq("prescription_id", prescriptionId);

    if (error) {
      throw new Error(`Error fetching prescription items: ${error.message}`);
    }

    return data as PrescriptionItem[];
  } catch (error: any) {
    console.error("Error in getPrescriptionItems:", error);
    throw new Error(error.message || "Failed to fetch prescription items");
  }
}

/**
 * Create a new prescription
 * @param clubId The club ID
 * @param userId The user ID creating the prescription
 * @param prescription The prescription data
 * @returns The created prescription
 */
export async function createMedicalPrescription(
  clubId: number,
  userId: string,
  prescription: Omit<MedicalPrescription, "id" | "club_id" | "created_at" | "signature_url">
): Promise<MedicalPrescription> {
  return withPermission(
    clubId,
    userId,
    PRESCRIPTION_PERMISSIONS.CREATE,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "medical.prescription.create",
        { 
          record_id: prescription.record_id,
          player_id: prescription.player_id,
          professional_id: prescription.professional_id
        },
        async () => {
          try {
            const { data, error } = await supabase
              .from("medical_prescriptions")
              .insert({
                club_id: clubId,
                ...prescription
              })
              .select()
              .single();

            if (error) {
              throw new Error(`Error creating medical prescription: ${error.message}`);
            }

            return data as MedicalPrescription;
          } catch (error: any) {
            console.error("Error in createMedicalPrescription:", error);
            throw new Error(error.message || "Failed to create medical prescription");
          }
        }
      );
    }
  );
}

/**
 * Add an item to a prescription
 * @param clubId The club ID
 * @param userId The user ID adding the item
 * @param item The prescription item data
 * @returns The created prescription item
 */
export async function addPrescriptionItem(
  clubId: number,
  userId: string,
  item: Omit<PrescriptionItem, "id" | "club_id" | "created_at" | "dispensed" | "dispensed_at" | "dispensed_by">
): Promise<PrescriptionItem> {
  return withPermission(
    clubId,
    userId,
    PRESCRIPTION_PERMISSIONS.EDIT,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "medical.prescription.add_item",
        { 
          prescription_id: item.prescription_id,
          product_id: item.product_id,
          quantity: item.quantity
        },
        async () => {
          try {
            const { data, error } = await supabase
              .from("medical_prescription_items")
              .insert({
                club_id: clubId,
                ...item,
                dispensed: false
              })
              .select()
              .single();

            if (error) {
              throw new Error(`Error adding prescription item: ${error.message}`);
            }

            return data as PrescriptionItem;
          } catch (error: any) {
            console.error("Error in addPrescriptionItem:", error);
            throw new Error(error.message || "Failed to add prescription item");
          }
        }
      );
    }
  );
}

/**
 * Update a prescription
 * @param clubId The club ID
 * @param userId The user ID updating the prescription
 * @param id The prescription ID
 * @param prescription The updated prescription data
 * @returns The updated prescription
 */
export async function updateMedicalPrescription(
  clubId: number,
  userId: string,
  id: number,
  prescription: Partial<Omit<MedicalPrescription, "id" | "club_id" | "created_at">>
): Promise<MedicalPrescription> {
  return withPermission(
    clubId,
    userId,
    PRESCRIPTION_PERMISSIONS.EDIT,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "medical.prescription.update",
        { id, ...prescription },
        async () => {
          try {
            const { data, error } = await supabase
              .from("medical_prescriptions")
              .update(prescription)
              .eq("club_id", clubId)
              .eq("id", id)
              .select()
              .single();

            if (error) {
              throw new Error(`Error updating medical prescription: ${error.message}`);
            }

            return data as MedicalPrescription;
          } catch (error: any) {
            console.error("Error in updateMedicalPrescription:", error);
            throw new Error(error.message || "Failed to update medical prescription");
          }
        }
      );
    }
  );
}

/**
 * Delete a prescription
 * @param clubId The club ID
 * @param userId The user ID deleting the prescription
 * @param id The prescription ID
 * @returns True if successful
 */
export async function deleteMedicalPrescription(
  clubId: number,
  userId: string,
  id: number
): Promise<boolean> {
  return withPermission(
    clubId,
    userId,
    PRESCRIPTION_PERMISSIONS.DELETE,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "medical.prescription.delete",
        { id },
        async () => {
          try {
            // Delete all prescription items first
            await supabase
              .from("medical_prescription_items")
              .delete()
              .eq("club_id", clubId)
              .eq("prescription_id", id);

            // Then delete the prescription
            const { error } = await supabase
              .from("medical_prescriptions")
              .delete()
              .eq("club_id", clubId)
              .eq("id", id);

            if (error) {
              throw new Error(`Error deleting medical prescription: ${error.message}`);
            }

            return true;
          } catch (error: any) {
            console.error("Error in deleteMedicalPrescription:", error);
            throw new Error(error.message || "Failed to delete medical prescription");
          }
        }
      );
    }
  );
}

/**
 * Add a digital signature to a prescription
 * @param clubId The club ID
 * @param userId The user ID adding the signature
 * @param prescriptionId The prescription ID
 * @param signatureFile The signature file
 * @returns The URL of the uploaded signature
 */
export async function addPrescriptionSignature(
  clubId: number,
  userId: string,
  prescriptionId: number,
  signatureFile: File
): Promise<string> {
  return withPermission(
    clubId,
    userId,
    PRESCRIPTION_PERMISSIONS.EDIT,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "medical.prescription.sign",
        { prescriptionId },
        async () => {
          try {
            // Upload the signature
            const signatureUrl = await uploadSignature(clubId, `prescription_${prescriptionId}`, signatureFile);

            // Update the prescription with the signature URL
            const { error } = await supabase
              .from("medical_prescriptions")
              .update({ signature_url: signatureUrl })
              .eq("club_id", clubId)
              .eq("id", prescriptionId);

            if (error) {
              throw new Error(`Error updating prescription signature: ${error.message}`);
            }

            return signatureUrl;
          } catch (error: any) {
            console.error("Error in addPrescriptionSignature:", error);
            throw new Error(error.message || "Failed to add prescription signature");
          }
        }
      );
    }
  );
}
