# Documentação das Funcionalidades do SaaS

Esta seção descreve todos os módulos, fluxos, diferenciais e integrações do produto.

## Visão Geral
SaaS para gestão de clubes esportivos: elenco, partidas, treinos, finanças, saúde, staff, agenda, tarefas, multi-clube e integrações. Plataforma multiusuário, responsiva, com dados reais do Supabase.

## Funcionalidades por Módulo

### Elenco
- Cadastro, edição e exclusão de jogadores
- Status dinâmico (disponível, lesionado, suspenso)
- Filtros por status
- Edição de dados e status via modal
- Busca, ordenação e detalhamento de jogadores
- Cadastro e exibição de staff técnico

### Partidas
- Agendamento, edição e finalização de partidas
- Fluxo de ida/volta (campos e regras específicas)
- Cadastro e edição de escalação (inclui tipo de formação)
- Cadastro de gols e cartões (submodais dedicados)
- Histórico e estatísticas por temporada
- Exibição de próximos jogos e jogos passados
- Detalhamento completo de partidas e eventos
- Artilheiros dinâmicos por temporada
- Relatórios de desempenho individual e coletivo

### Treinamentos
- Cadastro e edição de objetivos semanais
- Progresso dos objetivos (incremento/decremento com limite)
- Edição/exclusão direta na lista de objetivos
- Tipos de treino: técnico, tático, físico
- Banco de exercícios real (cadastro, busca, detalhamento)
- Associação de exercícios a treinos
- Detalhamento de treinos e exercícios realizados

### Financeiro
- Lançamentos de receitas e despesas
- Vínculo a contratos e salários
- Listagem, criação, edição e exclusão de lançamentos
- Relatórios financeiros básicos

### Saúde/Médico
- Cadastro de registros médicos
- Histórico de lesões e recuperação
- Listagem e detalhamento de registros

### Staff
- Cadastro e gerenciamento de membros do staff
- Definição de cargos, experiência e dados pessoais

### Agenda/Eventos
- Cadastro, edição e exclusão de eventos do clube
- Listagem de eventos futuros e passados
- Associação de participantes a eventos

### Tarefas
- Cadastro, edição e exclusão de tarefas
- Acompanhamento de status e responsáveis

### Multi-clube e Permissões
- Associação de usuários a múltiplos clubes
- Isolamento total dos dados por clube
- Permissões baseadas em usuário e papel
- Seleção de clube ativo e troca dinâmica

### Progresso Físico (em planejamento)
- Cadastro e exibição de registros de evolução física (peso, altura, gordura, notas)
- Integração futura com relatórios de desempenho físico

### Utilitários e Integrações
- Relatórios de artilheiros por temporada
- Suporte a multiusuário real (login, permissões, vínculo a clubes)
- Integração total com Supabase (dados reais, RLS)
- UX responsiva e mobile-friendly
- Estados de loading, erro e vazio em todas as listas
- Mensagens amigáveis para empty states
- Comentários e roadmap para módulos em planejamento

## Fluxos do Usuário
- Cadastro e edição de jogador, staff, partida, treino, objetivo, evento, tarefa, etc
- Finalização de partida com submodais para gols/cartões
- Seleção de temporada ativa para todas as estatísticas
- Troca de clube ativo (multi-clube)
- Busca, filtro e ordenação em todas as listas
- Feedback visual de loading, sucesso e erro

## Diferenciais do SaaS
- Multi-clube e multiusuário reais
- Dados 100% reais, sem mocks
- Integração direta com Supabase
- Permissões e isolamento de dados robustos
- UX moderna, responsiva e amigável
- Estrutura pronta para expansão (roadmap)

## Roadmap / Módulos em Planejamento
- Progresso físico detalhado (aguardando tabela no banco)
- Notificações e alertas
- Relatórios avançados (exportação, gráficos)
- Integração com apps de terceiros

---

> Consulte fluxos detalhados e exemplos de tela abaixo. (Em construção)
