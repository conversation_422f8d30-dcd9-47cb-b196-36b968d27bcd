import { useState } from "react";
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON>nt, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Card<PERSON>itle, 
  CardDescription 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/components/ui/use-toast";
import { 
  Plus, 
  Pencil, 
  Trash2, 
  CheckCircle2, 
  Calendar, 
  Mail, 
  FileText, 
  Users 
} from "lucide-react";
import { AdministrativeReminder } from "@/api/api";
import { useAdministrativeRemindersStore } from "@/store/useAdministrativeRemindersStore";
import { NovoLembreteDialog } from "@/components/administrativo/NovoLembreteDialog";
import { EditarLembreteDialog } from "@/components/administrativo/EditarLembreteDialog";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import { Checkbox } from "@/components/ui/checkbox";

interface LembretesTabProps {
  reminders: AdministrativeReminder[];
  loading: boolean;
  error: string | null;
  clubId: number;
}

export function LembretesTab({ reminders, loading, error, clubId }: LembretesTabProps) {
  const [novoLembreteDialogOpen, setNovoLembreteDialogOpen] = useState(false);
  const [editarLembreteDialogOpen, setEditarLembreteDialogOpen] = useState(false);
  const [excluirLembreteDialogOpen, setExcluirLembreteDialogOpen] = useState(false);
  const [lembreteParaExcluir, setLembreteParaExcluir] = useState<AdministrativeReminder | null>(null);
  
  const { 
    toggleReminderCompleted, 
    removeReminder 
  } = useAdministrativeRemindersStore();
  
  const [selectedReminder, setSelectedReminder] = useState<AdministrativeReminder | null>(null);
  const [showCompleted, setShowCompleted] = useState(false);

  const filteredReminders = reminders.filter(reminder => showCompleted || !reminder.completed);

  const handleEditReminder = (reminder: AdministrativeReminder) => {
    setSelectedReminder(reminder);
    setEditarLembreteDialogOpen(true);
  };

  const handleDeleteReminder = (reminder: AdministrativeReminder) => {
    setLembreteParaExcluir(reminder);
    setExcluirLembreteDialogOpen(true);
  };

  const confirmDeleteReminder = async () => {
    if (lembreteParaExcluir) {
      try {
        await removeReminder(clubId, lembreteParaExcluir.id);
        toast({
          title: "Lembrete excluído",
          description: "O lembrete foi excluído com sucesso.",
        });
      } catch (error) {
        toast({
          title: "Erro ao excluir lembrete",
          description: "Ocorreu um erro ao excluir o lembrete.",
          variant: "destructive",
        });
      }
    }
    setExcluirLembreteDialogOpen(false);
  };

  const handleToggleCompleted = async (reminder: AdministrativeReminder) => {
    try {
      await toggleReminderCompleted(clubId, reminder.id, !reminder.completed);
      toast({
        title: reminder.completed ? "Lembrete reativado" : "Lembrete concluído",
        description: reminder.completed 
          ? "O lembrete foi marcado como pendente." 
          : "O lembrete foi marcado como concluído.",
      });
    } catch (error) {
      toast({
        title: "Erro ao atualizar lembrete",
        description: "Ocorreu um erro ao atualizar o status do lembrete.",
        variant: "destructive",
      });
    }
  };

  const getReminderTypeIcon = (type: string) => {
    switch (type) {
      case 'activity':
        return <Calendar className="h-4 w-4" />;
      case 'document':
        return <FileText className="h-4 w-4" />;
      case 'email':
        return <Mail className="h-4 w-4" />;
      case 'meeting':
        return <Users className="h-4 w-4" />;
      default:
        return <Calendar className="h-4 w-4" />;
    }
  };

  const getReminderTypeLabel = (type: string) => {
    switch (type) {
      case 'activity':
        return 'Atividade';
      case 'document':
        return 'Documento';
      case 'email':
        return 'E-mail';
      case 'meeting':
        return 'Reunião';
      default:
        return 'Outro';
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Agenda e Lembretes</CardTitle>
          <CardDescription>
            Gerencie lembretes e compromissos administrativos
          </CardDescription>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="show-completed" 
              checked={showCompleted} 
              onCheckedChange={(checked) => setShowCompleted(checked as boolean)} 
            />
            <label 
              htmlFor="show-completed" 
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Mostrar concluídos
            </label>
          </div>
          <Button onClick={() => {
            setSelectedReminder(null);
            setNovoLembreteDialogOpen(true);
          }}>
            <Plus className="h-4 w-4 mr-2" />
            Novo Lembrete
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="text-center py-4">Carregando lembretes...</div>
        ) : error ? (
          <div className="text-center py-4 text-red-500">{error}</div>
        ) : filteredReminders.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            Nenhum lembrete encontrado. Clique em "Novo Lembrete" para criar.
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]"></TableHead>
                <TableHead>Tipo</TableHead>
                <TableHead>Título</TableHead>
                <TableHead>Descrição</TableHead>
                <TableHead>Data</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredReminders.map((reminder) => (
                <TableRow key={reminder.id} className={reminder.completed ? "opacity-60" : ""}>
                  <TableCell>
                    <Checkbox 
                      checked={reminder.completed} 
                      onCheckedChange={() => handleToggleCompleted(reminder)}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getReminderTypeIcon(reminder.reminder_type)}
                      {getReminderTypeLabel(reminder.reminder_type)}
                    </div>
                  </TableCell>
                  <TableCell>{reminder.title}</TableCell>
                  <TableCell>{reminder.description}</TableCell>
                  <TableCell>
                    {new Date(reminder.reminder_date).toLocaleDateString('pt-BR')}
                  </TableCell>
                  <TableCell>
                    {reminder.completed ? (
                      <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                        Concluído
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-200">
                        Pendente
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handleToggleCompleted(reminder)}
                        title={reminder.completed ? "Marcar como pendente" : "Marcar como concluído"}
                      >
                        <CheckCircle2 className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handleEditReminder(reminder)}
                        title="Editar"
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handleDeleteReminder(reminder)}
                        title="Excluir"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>

      <NovoLembreteDialog
        open={novoLembreteDialogOpen}
        onOpenChange={setNovoLembreteDialogOpen}
        clubId={clubId}
      />

      <EditarLembreteDialog
        open={editarLembreteDialogOpen}
        onOpenChange={setEditarLembreteDialogOpen}
        clubId={clubId}
        reminder={selectedReminder}
      />

      <ConfirmDialog
        open={excluirLembreteDialogOpen}
        onOpenChange={setExcluirLembreteDialogOpen}
        title="Excluir lembrete"
        description="Tem certeza que deseja excluir este lembrete? Esta ação não pode ser desfeita."
        onConfirm={confirmDeleteReminder}
      />
    </Card>
  );
}
