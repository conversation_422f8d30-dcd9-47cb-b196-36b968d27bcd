import { useState, useEffect, useRef } from "react";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Plus, Pencil, Trash2, Loader2, Upload, Image as ImageIcon } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { supabase } from "@/integrations/supabase/client";
import { uploadClubLogo } from "@/api/storage-simple";
import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

// Tipo para os adversários
type Opponent = {
  id: string;
  club_id: number;
  name: string;
  logo_url: string | null;
  country: string | null;
  city: string | null;
  stadium: string | null;
  created_at: string;
  updated_at: string;
};

export default function Adversarios() {
  const [opponents, setOpponents] = useState<Opponent[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [currentOpponent, setCurrentOpponent] = useState<Opponent | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    logo_url: "",
    country: "",
    city: "",
    stadium: "",
  });
  const [submitting, setSubmitting] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { toast } = useToast();
  const clubId = useCurrentClubId();

  // Carregar adversários
  useEffect(() => {
    if (clubId) {
      fetchOpponents();
    }
  }, [clubId]);

  const fetchOpponents = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("opponents")
        .select("*")
        .eq("club_id", clubId)
        .order("name");

      if (error) throw error;
      setOpponents(data || []);
    } catch (error) {
      console.error("Erro ao carregar adversários:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar os adversários.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (opponent: Opponent | null = null) => {
    // Reset logo file state
    setLogoFile(null);

    if (opponent) {
      setCurrentOpponent(opponent);
      setFormData({
        name: opponent.name,
        logo_url: opponent.logo_url || "",
        country: opponent.country || "",
        city: opponent.city || "",
        stadium: opponent.stadium || "",
      });
    } else {
      setCurrentOpponent(null);
      setFormData({
        name: "",
        logo_url: "",
        country: "",
        city: "",
        stadium: "",
      });
    }
    setDialogOpen(true);
  };

  const handleOpenDeleteDialog = (opponent: Opponent) => {
    setCurrentOpponent(opponent);
    setDeleteDialogOpen(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setLogoFile(file);
      // Create a preview URL
      const previewUrl = URL.createObjectURL(file);
      setFormData(prev => ({ ...prev, logo_url: previewUrl }));
    } catch (err) {
      console.error("Erro ao selecionar arquivo:", err);
      toast({
        title: "Erro",
        description: "Não foi possível selecionar o arquivo.",
        variant: "destructive",
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim()) {
      toast({
        title: "Erro",
        description: "O nome do adversário é obrigatório.",
        variant: "destructive",
      });
      return;
    }

    try {
      setSubmitting(true);

      // Upload logo file if selected
      let logoUrl = formData.logo_url;
      if (logoFile) {
        try {
          setUploading(true);
          // Upload the file
          logoUrl = await uploadClubLogo(clubId.toString(), logoFile);
        } catch (err) {
          console.error("Erro ao fazer upload do logo:", err);
          toast({
            title: "Erro",
            description: "Não foi possível fazer upload do logo.",
            variant: "destructive",
          });
          // Continue with the save process even if logo upload fails
        } finally {
          setUploading(false);
        }
      }

      if (currentOpponent) {
        // Atualizar adversário existente
        const { error } = await supabase
          .from("opponents")
          .update({
            name: formData.name,
            logo_url: logoUrl || null,
            country: formData.country || null,
            city: formData.city || null,
            stadium: formData.stadium || null,
            updated_at: new Date().toISOString(),
          })
          .eq("id", currentOpponent.id);

        if (error) throw error;

        toast({
          title: "Sucesso",
          description: "Adversário atualizado com sucesso!",
        });
      } else {
        // Criar novo adversário
        const { error } = await supabase.from("opponents").insert({
          club_id: clubId,
          name: formData.name,
          logo_url: logoUrl || null,
          country: formData.country || null,
          city: formData.city || null,
          stadium: formData.stadium || null,
        });

        if (error) throw error;

        toast({
          title: "Sucesso",
          description: "Adversário adicionado com sucesso!",
        });
      }

      // Recarregar a lista de adversários
      fetchOpponents();
      setDialogOpen(false);
    } catch (error) {
      console.error("Erro ao salvar adversário:", error);
      toast({
        title: "Erro",
        description: "Não foi possível salvar o adversário.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!currentOpponent) return;

    try {
      setSubmitting(true);

      // Verificar se o adversário está sendo usado em alguma partida
      const { data: matchesData, error: matchesError } = await supabase
        .from("matches")
        .select("id")
        .eq("opponent_id", currentOpponent.id)
        .limit(1);

      if (matchesError) throw matchesError;

      if (matchesData && matchesData.length > 0) {
        toast({
          title: "Erro",
          description: "Este adversário não pode ser excluído porque está sendo usado em partidas.",
          variant: "destructive",
        });
        setDeleteDialogOpen(false);
        return;
      }

      // Excluir adversário
      const { error } = await supabase
        .from("opponents")
        .delete()
        .eq("id", currentOpponent.id);

      if (error) throw error;

      toast({
        title: "Sucesso",
        description: "Adversário excluído com sucesso!",
      });

      // Recarregar a lista de adversários
      fetchOpponents();
      setDeleteDialogOpen(false);
    } catch (error) {
      console.error("Erro ao excluir adversário:", error);
      toast({
        title: "Erro",
        description: "Não foi possível excluir o adversário.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Adversários</h1>
        <Button onClick={() => handleOpenDialog()}>
          <Plus className="h-4 w-4 mr-2" /> Adicionar Adversário
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Lista de Adversários</CardTitle>
          <CardDescription>
            Gerencie os adversários do seu clube para usar nas partidas.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
            </div>
          ) : opponents.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>Nenhum adversário cadastrado.</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => handleOpenDialog()}
              >
                Adicionar seu primeiro adversário
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>País</TableHead>
                  <TableHead>Cidade</TableHead>
                  <TableHead>Estádio</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {opponents.map((opponent) => (
                  <TableRow key={opponent.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        {opponent.logo_url && (
                          <img
                            src={opponent.logo_url}
                            alt={opponent.name}
                            className="h-6 w-6 object-contain"
                          />
                        )}
                        {opponent.name}
                      </div>
                    </TableCell>
                    <TableCell>{opponent.country || "-"}</TableCell>
                    <TableCell>{opponent.city || "-"}</TableCell>
                    <TableCell>{opponent.stadium || "-"}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleOpenDialog(opponent)}
                        >
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleOpenDeleteDialog(opponent)}
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Dialog para adicionar/editar adversário */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {currentOpponent ? "Editar Adversário" : "Adicionar Adversário"}
            </DialogTitle>
            <DialogDescription>
              Preencha os dados do adversário abaixo.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Nome *
                </Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="logo_upload" className="text-right">
                  Logo do Time
                </Label>
                <div className="col-span-3 flex flex-col gap-2">
                  <div className="flex items-center gap-2">
                    {formData.logo_url ? (
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={formData.logo_url} alt="Logo do time" />
                        <AvatarFallback>
                          {formData.name?.slice(0, 2).toUpperCase() || "TM"}
                        </AvatarFallback>
                      </Avatar>
                    ) : (
                      <div className="h-12 w-12 rounded-full bg-gray-100 flex items-center justify-center">
                        <ImageIcon className="h-6 w-6 text-gray-400" />
                      </div>
                    )}
                    <div className="flex-1">
                      <input
                        type="file"
                        id="logo_upload"
                        ref={fileInputRef}
                        className="hidden"
                        accept="image/*"
                        onChange={handleFileChange}
                        disabled={uploading}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={uploading}
                        className="w-full"
                      >
                        {uploading ? (
                          <div className="flex items-center">
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            <span>Enviando...</span>
                          </div>
                        ) : (
                          <div className="flex items-center">
                            <Upload className="mr-2 h-4 w-4" />
                            <span>Selecionar logo</span>
                          </div>
                        )}
                      </Button>
                    </div>
                  </div>
                  {logoFile && (
                    <p className="text-xs text-muted-foreground">
                      Arquivo selecionado: {logoFile.name}
                    </p>
                  )}
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="country" className="text-right">
                  País
                </Label>
                <Input
                  id="country"
                  name="country"
                  value={formData.country}
                  onChange={handleInputChange}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="city" className="text-right">
                  Cidade
                </Label>
                <Input
                  id="city"
                  name="city"
                  value={formData.city}
                  onChange={handleInputChange}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="stadium" className="text-right">
                  Estádio
                </Label>
                <Input
                  id="stadium"
                  name="stadium"
                  value={formData.stadium}
                  onChange={handleInputChange}
                  className="col-span-3"
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setDialogOpen(false)}
                disabled={submitting}
              >
                Cancelar
              </Button>
              <Button type="submit" disabled={submitting}>
                {submitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Salvando...
                  </>
                ) : (
                  "Salvar"
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Dialog para confirmar exclusão */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar Exclusão</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir o adversário "{currentOpponent?.name}"?
              Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
              disabled={submitting}
            >
              Cancelar
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleDelete}
              disabled={submitting}
            >
              {submitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Excluindo...
                </>
              ) : (
                "Excluir"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
