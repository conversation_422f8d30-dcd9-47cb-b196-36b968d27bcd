# Sistema de Permissões - Game Day Nexus Platform

## Visão Geral
O sistema de permissões é baseado em um modelo RBAC (Role-Based Access Control) com suporte a permissões granulares. As permissões são organizadas por módulos e podem ser atribuídas a papéis (roles) ou diretamente a usuários.

## Estrutura de Permissões

### Níveis de Acesso
1. **Visualização (VIEW)**: Permite apenas visualizar os dados
2. **Criação (CREATE)**: Permite adicionar novos registros
3. **Edição (EDIT)**: Permite modificar registros existentes
4. **Exclusão (DELETE)**: Permite remover registros
5. **Permissões Específicas**: Alguns módulos têm permissões adicionais específicas

## Módulos e Permissões

### 1. <PERSON><PERSON><PERSON> (Players)
```
players.view         // Visualizar lista de jogadores
players.view_own     // Visualizar apenas o próprio perfil (para jogadores)
players.edit_own     // Editar apenas o próprio perfil (para jogadores)
players.create       // Adicionar novos jogadores
players.edit         // Editar jogadores existentes
players.delete       // Remover jogadores
players.documents.view    // Visualizar documentos
players.documents.verify  // Verificar documentos
players.finances.view     // Visualizar finanças
players.finances.edit     // Gerenciar finanças
players.evaluation.view   // Visualizar avaliações
players.evaluation.edit   // Gerenciar avaliações
```

### 2. Partidas (Matches)
```
matches.view    // Visualizar partidas
matches.create  // Criar partidas
matches.edit    // Editar partidas
matches.delete  // Excluir partidas
matches.lineup  // Gerenciar escalação
matches.events  // Registrar eventos (gols, cartões, etc.)
```

### 3. Treinamentos (Trainings)
```
trainings.view    // Visualizar treinamentos
trainings.create  // Criar treinamentos
trainings.edit    // Editar treinamentos
trainings.delete  // Excluir treinamentos
```

### 4. Departamento Médico (Medical)
```
medical.view    // Visualizar prontuários
medical.create  // Criar prontuários
medical.edit    // Editar prontuários
medical.delete  // Excluir prontuários
```

### 5. Finanças (Finance)
```
finances.view    // Visualizar finanças
finances.create  // Criar transações
finances.edit    // Editar transações
finances.delete  // Excluir transações
```

### 6. Usuários (Users)
```
users.view         // Visualizar usuários
users.create       // Criar usuários
users.edit         // Editar usuários
users.delete       // Excluir usuários
users.permissions  // Gerenciar permissões
```

### 7. Configurações (Settings)
```
settings.view  // Visualizar configurações
settings.edit  // Editar configurações
```

### 8. Agenda (Agenda)
```
agenda.view    // Visualizar agenda
agenda.create  // Criar eventos
agenda.edit    // Editar eventos
agenda.delete  // Excluir eventos
```

## Papéis Predefinidos (Roles)

O sistema inclui papéis predefinidos como:
- `admin`: Acesso total ao sistema
- `manager`: Acesso a todas as funcionalidades, exceto configurações do sistema
- `coach`: Acesso a jogadores, treinamentos e partidas
- `doctor`: Acesso ao departamento médico
- `player`: Acesso limitado ao próprio perfil e informações relevantes

## Implementação Técnica

### Frontend
- **Hook**: `usePermission()` para verificar permissões
- **Componentes**: 
  - `PermissionGuard` para proteger rotas
  - `PermissionControl` para mostrar/ocultar elementos

### Backend
- **Tabela**: `club_members` armazena as permissões dos usuários
- **RLS**: Row Level Security no Supabase para proteção adicional

## Características Avançadas

1. **Permissões Específicas por Módulo**:
   - Ex: `players.documents.verify` para verificação de documentos
   - Ex: `matches.lineup` para gerenciar escalação de times
   - Ex: `users.permissions` para gerenciar permissões de outros usuários

2. **Permissões de Propriedade**:
   - `view_own` e `edit_own` permitem que usuários acessem apenas seus próprios dados

3. **Permissões por Departamento**:
   - É possível restringir permissões a departamentos específicos

4. **Permissões Específicas de Contexto**:
   - Algumas ações específicas podem ter suas próprias permissões (ex: aprovações, verificações)

5. **Hierarquia de Acesso**:
   - As permissões podem ser atribuídas de forma hierárquica, com alguns papéis tendo acesso a subconjuntos específicos de funcionalidades

## Como Adicionar Novas Permissões

1. Adicione a nova permissão em `src/constants/permissions.ts`
2. Atualize a documentação neste arquivo
3. Atualize os papéis predefinidos conforme necessário
4. Implemente as verificações de permissão no código

## Verificação de Permissões

Para verificar permissões em componentes React:

```typescript
import { usePermission } from '@/hooks/usePermission';

function MeuComponente() {
  const { hasPermission } = usePermission();
  
  if (hasPermission('players.view')) {
    return <div>Conteúdo restrito</div>;
  }
  
  return <div>Sem permissão</div>;
}
```

Para verificar permissões em funções assíncronas:

```typescript
import { usePermission } from '@/hooks/usePermission';

async function minhaFuncao() {
  const { checkPermission } = usePermission();
  
  if (await checkPermission('players.edit')) {
    // Executar ação restrita
  }
}
```

## Solução de Problemas

Se encontrar problemas com permissões:
1. Verifique se o usuário está autenticado
2. Confira se o usuário tem a role correta
3. Verifique se as permissões específicas estão atribuídas
4. Consulte os logs do servidor para erros de permissão

---

Documentação atualizada em: 27/05/2025
