export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      accommodations: {
        Row: {
          address: string | null
          capacity: number | null
          club_id: number | null
          cost: number | null
          created_at: string | null
          id: number
          name: string
          notes: string | null
          rooms_count: number | null
          type: string
        }
        Insert: {
          address?: string | null
          capacity?: number | null
          club_id?: number | null
          cost?: number | null
          created_at?: string | null
          id?: number
          name: string
          notes?: string | null
          rooms_count?: number | null
          type: string
        }
        Update: {
          address?: string | null
          capacity?: number | null
          club_id?: number | null
          cost?: number | null
          created_at?: string | null
          id?: number
          name?: string
          notes?: string | null
          rooms_count?: number | null
          type?: string
        }
        Relationships: [
          {
            foreignKeyName: "accommodations_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      administrative_documents: {
        Row: {
          club_id: number
          content: string
          created_at: string | null
          created_by: string
          digital_signature: boolean | null
          document_number: number
          document_type: string
          id: number
          signature_url: string | null
          signed_at: string | null
          signed_by: string | null
          title: string
          updated_at: string | null
        }
        Insert: {
          club_id: number
          content: string
          created_at?: string | null
          created_by: string
          digital_signature?: boolean | null
          document_number: number
          document_type: string
          id?: number
          signature_url?: string | null
          signed_at?: string | null
          signed_by?: string | null
          title: string
          updated_at?: string | null
        }
        Update: {
          club_id?: number
          content?: string
          created_at?: string | null
          created_by?: string
          digital_signature?: boolean | null
          document_number?: number
          document_type?: string
          id?: number
          signature_url?: string | null
          signed_at?: string | null
          signed_by?: string | null
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "administrative_documents_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      administrative_reminders: {
        Row: {
          club_id: number
          completed: boolean | null
          created_at: string | null
          created_by: string
          description: string | null
          id: number
          reminder_date: string
          reminder_type: string
          title: string
          updated_at: string | null
        }
        Insert: {
          club_id: number
          completed?: boolean | null
          created_at?: string | null
          created_by: string
          description?: string | null
          id?: number
          reminder_date: string
          reminder_type: string
          title: string
          updated_at?: string | null
        }
        Update: {
          club_id?: number
          completed?: boolean | null
          created_at?: string | null
          created_by?: string
          description?: string | null
          id?: number
          reminder_date?: string
          reminder_type?: string
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "administrative_reminders_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      administrative_tasks: {
        Row: {
          club_id: number
          collaborator_id: number | null
          created_at: string | null
          created_by: string
          description: string | null
          due_date: string | null
          id: number
          responsible: string | null
          status: string
          title: string
          updated_at: string | null
        }
        Insert: {
          club_id: number
          collaborator_id?: number | null
          created_at?: string | null
          created_by: string
          description?: string | null
          due_date?: string | null
          id?: number
          responsible?: string | null
          status?: string
          title: string
          updated_at?: string | null
        }
        Update: {
          club_id?: number
          collaborator_id?: number | null
          created_at?: string | null
          created_by?: string
          description?: string | null
          due_date?: string | null
          id?: number
          responsible?: string | null
          status?: string
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "administrative_tasks_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "administrative_tasks_collaborator_id_fkey"
            columns: ["collaborator_id"]
            isOneToOne: false
            referencedRelation: "collaborators"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "administrative_tasks_collaborator_id_fkey"
            columns: ["collaborator_id"]
            isOneToOne: false
            referencedRelation: "collaborators_view"
            referencedColumns: ["id"]
          },
        ]
      }
      agenda_events: {
        Row: {
          club_id: number | null
          date: string
          description: string | null
          end_time: string | null
          id: number
          location: string | null
          participants: string[] | null
          title: string
          type: string | null
        }
        Insert: {
          club_id?: number | null
          date: string
          description?: string | null
          end_time?: string | null
          id?: number
          location?: string | null
          participants?: string[] | null
          title: string
          type?: string | null
        }
        Update: {
          club_id?: number | null
          date?: string
          description?: string | null
          end_time?: string | null
          id?: number
          location?: string | null
          participants?: string[] | null
          title?: string
          type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "agenda_events_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      audit_logs: {
        Row: {
          action: string
          club_id: number | null
          created_at: string | null
          details: Json | null
          id: number
          success: boolean
          user_id: string | null
        }
        Insert: {
          action: string
          club_id?: number | null
          created_at?: string | null
          details?: Json | null
          id?: number
          success: boolean
          user_id?: string | null
        }
        Update: {
          action?: string
          club_id?: number | null
          created_at?: string | null
          details?: Json | null
          id?: number
          success?: boolean
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "audit_logs_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "audit_logs_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      callup_players: {
        Row: {
          callup_id: number
          club_id: number
          created_at: string | null
          id: number
          name: string | null
          player_id: string | null
          role: string
          user_id: string | null
        }
        Insert: {
          callup_id: number
          club_id: number
          created_at?: string | null
          id?: number
          name?: string | null
          player_id?: string | null
          role: string
          user_id?: string | null
        }
        Update: {
          callup_id?: number
          club_id?: number
          created_at?: string | null
          id?: number
          name?: string | null
          player_id?: string | null
          role?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "callup_players_callup_id_fkey"
            columns: ["callup_id"]
            isOneToOne: false
            referencedRelation: "callups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "callup_players_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "callup_players_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "players"
            referencedColumns: ["id"]
          },
        ]
      }
      callups: {
        Row: {
          away_club_logo: string | null
          bus_image: string | null
          category_id: number
          club_id: number
          competition_image: string | null
          created_at: string | null
          home_club_logo: string | null
          hotel_control: string | null
          hotel_image: string | null
          id: number
          match_date: string
          match_location: string
          match_schedule: string | null
          sponsor_image1: string | null
          sponsor_image2: string | null
          sponsor_image3: string | null
          sponsor_image4: string | null
          stadium_image: string | null
          tournament_type: string
          uniform_image: string | null
          updated_at: string | null
        }
        Insert: {
          away_club_logo?: string | null
          bus_image?: string | null
          category_id: number
          club_id: number
          competition_image?: string | null
          created_at?: string | null
          home_club_logo?: string | null
          hotel_control?: string | null
          hotel_image?: string | null
          id?: number
          match_date: string
          match_location: string
          match_schedule?: string | null
          sponsor_image1?: string | null
          sponsor_image2?: string | null
          sponsor_image3?: string | null
          sponsor_image4?: string | null
          stadium_image?: string | null
          tournament_type: string
          uniform_image?: string | null
          updated_at?: string | null
        }
        Update: {
          away_club_logo?: string | null
          bus_image?: string | null
          category_id?: number
          club_id?: number
          competition_image?: string | null
          created_at?: string | null
          home_club_logo?: string | null
          hotel_control?: string | null
          hotel_image?: string | null
          id?: number
          match_date?: string
          match_location?: string
          match_schedule?: string | null
          sponsor_image1?: string | null
          sponsor_image2?: string | null
          sponsor_image3?: string | null
          sponsor_image4?: string | null
          stadium_image?: string | null
          tournament_type?: string
          uniform_image?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "callups_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "callups_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      categories: {
        Row: {
          club_id: number | null
          created_at: string | null
          description: string | null
          id: number
          name: string
          type: string
        }
        Insert: {
          club_id?: number | null
          created_at?: string | null
          description?: string | null
          id?: number
          name: string
          type: string
        }
        Update: {
          club_id?: number | null
          created_at?: string | null
          description?: string | null
          id?: number
          name?: string
          type?: string
        }
        Relationships: [
          {
            foreignKeyName: "categories_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      category_lineups: {
        Row: {
          category_id: number
          club_id: number
          created_at: string | null
          formation: string
          id: number
          lineup: Json
          updated_at: string | null
        }
        Insert: {
          category_id: number
          club_id: number
          created_at?: string | null
          formation?: string
          id?: number
          lineup?: Json
          updated_at?: string | null
        }
        Update: {
          category_id?: number
          club_id?: number
          created_at?: string | null
          formation?: string
          id?: number
          lineup?: Json
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "category_lineups_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "category_lineups_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      club_form_templates: {
        Row: {
          club_id: number
          content: string
          created_at: string | null
          created_by: string
          description: string | null
          form_type: string
          id: number
          is_active: boolean | null
          name: string
          updated_at: string | null
        }
        Insert: {
          club_id: number
          content: string
          created_at?: string | null
          created_by: string
          description?: string | null
          form_type: string
          id?: number
          is_active?: boolean | null
          name: string
          updated_at?: string | null
        }
        Update: {
          club_id?: number
          content?: string
          created_at?: string | null
          created_by?: string
          description?: string | null
          form_type?: string
          id?: number
          is_active?: boolean | null
          name?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "club_form_templates_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      club_info: {
        Row: {
          address: string | null
          founded_year: number | null
          id: number
          logo_url: string | null
          name: string
          phone: string | null
          president: string | null
          primary_color: string | null
          secondary_color: string | null
          stadium: string | null
          zip_code: string | null
        }
        Insert: {
          address?: string | null
          founded_year?: number | null
          id?: number
          logo_url?: string | null
          name: string
          phone?: string | null
          president?: string | null
          primary_color?: string | null
          secondary_color?: string | null
          stadium?: string | null
          zip_code?: string | null
        }
        Update: {
          address?: string | null
          founded_year?: number | null
          id?: number
          logo_url?: string | null
          name?: string
          phone?: string | null
          president?: string | null
          primary_color?: string | null
          secondary_color?: string | null
          stadium?: string | null
          zip_code?: string | null
        }
        Relationships: []
      }
      club_members: {
        Row: {
          club_id: number
          created_at: string | null
          id: number
          permissions: Json | null
          role: string
          staff_role: string | null
          status: string
          user_id: string
        }
        Insert: {
          club_id: number
          created_at?: string | null
          id?: number
          permissions?: Json | null
          role: string
          staff_role?: string | null
          status?: string
          user_id: string
        }
        Update: {
          club_id?: number
          created_at?: string | null
          id?: number
          permissions?: Json | null
          role?: string
          staff_role?: string | null
          status?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "club_members_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      coaches: {
        Row: {
          age: number | null
          club_id: number | null
          experience: string | null
          id: string
          name: string
          nationality: string | null
          role: string
        }
        Insert: {
          age?: number | null
          club_id?: number | null
          experience?: string | null
          id?: string
          name: string
          nationality?: string | null
          role: string
        }
        Update: {
          age?: number | null
          club_id?: number | null
          experience?: string | null
          id?: string
          name?: string
          nationality?: string | null
          role?: string
        }
        Relationships: [
          {
            foreignKeyName: "coaches_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      collaborator_accommodations: {
        Row: {
          accommodation_id: number
          check_in_date: string | null
          check_out_date: string | null
          club_id: number
          collaborator_id: number
          created_at: string | null
          hotel_room_id: number | null
          id: number
          notes: string | null
          room_number: string | null
          status: string | null
        }
        Insert: {
          accommodation_id: number
          check_in_date?: string | null
          check_out_date?: string | null
          club_id: number
          collaborator_id: number
          created_at?: string | null
          hotel_room_id?: number | null
          id?: number
          notes?: string | null
          room_number?: string | null
          status?: string | null
        }
        Update: {
          accommodation_id?: number
          check_in_date?: string | null
          check_out_date?: string | null
          club_id?: number
          collaborator_id?: number
          created_at?: string | null
          hotel_room_id?: number | null
          id?: number
          notes?: string | null
          room_number?: string | null
          status?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "collaborator_accommodations_accommodation_id_fkey"
            columns: ["accommodation_id"]
            isOneToOne: false
            referencedRelation: "accommodations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "collaborator_accommodations_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "collaborator_accommodations_collaborator_id_fkey"
            columns: ["collaborator_id"]
            isOneToOne: false
            referencedRelation: "collaborators"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "collaborator_accommodations_collaborator_id_fkey"
            columns: ["collaborator_id"]
            isOneToOne: false
            referencedRelation: "collaborators_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "collaborator_accommodations_hotel_room_id_fkey"
            columns: ["hotel_room_id"]
            isOneToOne: false
            referencedRelation: "hotel_rooms"
            referencedColumns: ["id"]
          },
        ]
      }
      collaborator_documents: {
        Row: {
          club_id: number
          collaborator_id: number
          document_type: string
          file_url: string
          id: number
          rejection_reason: string | null
          status: string | null
          uploaded_at: string | null
          verified_at: string | null
          verified_by: string | null
        }
        Insert: {
          club_id: number
          collaborator_id: number
          document_type: string
          file_url: string
          id?: number
          rejection_reason?: string | null
          status?: string | null
          uploaded_at?: string | null
          verified_at?: string | null
          verified_by?: string | null
        }
        Update: {
          club_id?: number
          collaborator_id?: number
          document_type?: string
          file_url?: string
          id?: number
          rejection_reason?: string | null
          status?: string | null
          uploaded_at?: string | null
          verified_at?: string | null
          verified_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "collaborator_documents_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "collaborator_documents_collaborator_id_fkey"
            columns: ["collaborator_id"]
            isOneToOne: false
            referencedRelation: "collaborators"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "collaborator_documents_collaborator_id_fkey"
            columns: ["collaborator_id"]
            isOneToOne: false
            referencedRelation: "collaborators_view"
            referencedColumns: ["id"]
          },
        ]
      }
      collaborators: {
        Row: {
          address: string | null
          address_number: string | null
          bank_info: Json | null
          birth_date: string | null
          bonus: number | null
          certificate_url: string | null
          city: string | null
          club_id: number
          cpf: string | null
          created_at: string | null
          credential_number: string | null
          criminal_record_url: string | null
          document_id: string | null
          document_id_url: string | null
          email: string | null
          entry_date: string | null
          financial_data: Json | null
          full_name: string
          id: number
          image: string | null
          medical_certificate_url: string | null
          phone: string | null
          registration_number: string
          resume_url: string | null
          role: string
          role_type: string
          salary: number | null
          state: string | null
          status: string | null
          updated_at: string | null
          user_id: string | null
          zip_code: string | null
        }
        Insert: {
          address?: string | null
          address_number?: string | null
          bank_info?: Json | null
          birth_date?: string | null
          bonus?: number | null
          certificate_url?: string | null
          city?: string | null
          club_id: number
          cpf?: string | null
          created_at?: string | null
          credential_number?: string | null
          criminal_record_url?: string | null
          document_id?: string | null
          document_id_url?: string | null
          email?: string | null
          entry_date?: string | null
          financial_data?: Json | null
          full_name: string
          id?: number
          image?: string | null
          medical_certificate_url?: string | null
          phone?: string | null
          registration_number: string
          resume_url?: string | null
          role: string
          role_type: string
          salary?: number | null
          state?: string | null
          status?: string | null
          updated_at?: string | null
          user_id?: string | null
          zip_code?: string | null
        }
        Update: {
          address?: string | null
          address_number?: string | null
          bank_info?: Json | null
          birth_date?: string | null
          bonus?: number | null
          certificate_url?: string | null
          city?: string | null
          club_id?: number
          cpf?: string | null
          created_at?: string | null
          credential_number?: string | null
          criminal_record_url?: string | null
          document_id?: string | null
          document_id_url?: string | null
          email?: string | null
          entry_date?: string | null
          financial_data?: Json | null
          full_name?: string
          id?: number
          image?: string | null
          medical_certificate_url?: string | null
          phone?: string | null
          registration_number?: string
          resume_url?: string | null
          role?: string
          role_type?: string
          salary?: number | null
          state?: string | null
          status?: string | null
          updated_at?: string | null
          user_id?: string | null
          zip_code?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "collaborators_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      competitions: {
        Row: {
          club_id: number
          created_at: string | null
          end_date: string | null
          id: string
          logo_url: string | null
          name: string
          season_id: number | null
          start_date: string | null
          type: string | null
          updated_at: string | null
        }
        Insert: {
          club_id: number
          created_at?: string | null
          end_date?: string | null
          id?: string
          logo_url?: string | null
          name: string
          season_id?: number | null
          start_date?: string | null
          type?: string | null
          updated_at?: string | null
        }
        Update: {
          club_id?: number
          created_at?: string | null
          end_date?: string | null
          id?: string
          logo_url?: string | null
          name?: string
          season_id?: number | null
          start_date?: string | null
          type?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "competitions_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "competitions_season_id_fkey"
            columns: ["season_id"]
            isOneToOne: false
            referencedRelation: "seasons"
            referencedColumns: ["id"]
          },
        ]
      }
      contracts: {
        Row: {
          club_id: number | null
          details: string | null
          end_date: string
          id: number
          start_date: string
          status: string
          title: string
          type: string
          value: number
        }
        Insert: {
          club_id?: number | null
          details?: string | null
          end_date: string
          id?: number
          start_date: string
          status?: string
          title: string
          type: string
          value: number
        }
        Update: {
          club_id?: number | null
          details?: string | null
          end_date?: string
          id?: number
          start_date?: string
          status?: string
          title?: string
          type?: string
          value?: number
        }
        Relationships: [
          {
            foreignKeyName: "contracts_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      departments: {
        Row: {
          club_id: number | null
          created_at: string | null
          description: string | null
          id: number
          name: string
          permissions: Json | null
        }
        Insert: {
          club_id?: number | null
          created_at?: string | null
          description?: string | null
          id?: number
          name: string
          permissions?: Json | null
        }
        Update: {
          club_id?: number | null
          created_at?: string | null
          description?: string | null
          id?: number
          name?: string
          permissions?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "departments_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      exercises: {
        Row: {
          category: string | null
          club_id: number | null
          created_at: string | null
          description: string | null
          difficulty: string | null
          id: number
          name: string
          video_url: string | null
        }
        Insert: {
          category?: string | null
          club_id?: number | null
          created_at?: string | null
          description?: string | null
          difficulty?: string | null
          id?: number
          name: string
          video_url?: string | null
        }
        Update: {
          category?: string | null
          club_id?: number | null
          created_at?: string | null
          description?: string | null
          difficulty?: string | null
          id?: number
          name?: string
          video_url?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "exercises_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      financial_accounts: {
        Row: {
          amount: number
          category: string
          club_id: number
          cost_center: string | null
          created_at: string | null
          creation_date: string
          description: string
          due_date: string
          id: number
          notes: string | null
          receipt_url: string | null
          status: string
          supplier_client: string
          type: string
          updated_at: string | null
        }
        Insert: {
          amount: number
          category: string
          club_id: number
          cost_center?: string | null
          created_at?: string | null
          creation_date?: string
          description: string
          due_date: string
          id?: number
          notes?: string | null
          receipt_url?: string | null
          status?: string
          supplier_client: string
          type: string
          updated_at?: string | null
        }
        Update: {
          amount?: number
          category?: string
          club_id?: number
          cost_center?: string | null
          created_at?: string | null
          creation_date?: string
          description?: string
          due_date?: string
          id?: number
          notes?: string | null
          receipt_url?: string | null
          status?: string
          supplier_client?: string
          type?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "financial_accounts_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      financial_transactions: {
        Row: {
          amount: number
          category: string
          club_id: number | null
          collaborator_id: number | null
          date: string
          description: string
          id: number
          payment_status: string
          player_id: string | null
          type: string
        }
        Insert: {
          amount: number
          category: string
          club_id?: number | null
          collaborator_id?: number | null
          date: string
          description: string
          id?: number
          payment_status?: string
          player_id?: string | null
          type: string
        }
        Update: {
          amount?: number
          category?: string
          club_id?: number | null
          collaborator_id?: number | null
          date?: string
          description?: string
          id?: number
          payment_status?: string
          player_id?: string | null
          type?: string
        }
        Relationships: [
          {
            foreignKeyName: "financial_transactions_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "financial_transactions_collaborator_id_fkey"
            columns: ["collaborator_id"]
            isOneToOne: false
            referencedRelation: "collaborators"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "financial_transactions_collaborator_id_fkey"
            columns: ["collaborator_id"]
            isOneToOne: false
            referencedRelation: "collaborators_view"
            referencedColumns: ["id"]
          },
        ]
      }
      gols: {
        Row: {
          club_id: number | null
          created_at: string | null
          id: number
          match_id: string | null
          minute: number | null
          player_id: string | null
        }
        Insert: {
          club_id?: number | null
          created_at?: string | null
          id?: number
          match_id?: string | null
          minute?: number | null
          player_id?: string | null
        }
        Update: {
          club_id?: number | null
          created_at?: string | null
          id?: number
          match_id?: string | null
          minute?: number | null
          player_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "gols_match_id_fkey"
            columns: ["match_id"]
            isOneToOne: false
            referencedRelation: "matches"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "gols_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "players"
            referencedColumns: ["id"]
          },
        ]
      }
      hotel_rooms: {
        Row: {
          accommodation_id: number | null
          capacity: number
          club_id: number | null
          created_at: string | null
          id: number
          room_number: string
        }
        Insert: {
          accommodation_id?: number | null
          capacity: number
          club_id?: number | null
          created_at?: string | null
          id?: number
          room_number: string
        }
        Update: {
          accommodation_id?: number | null
          capacity?: number
          club_id?: number | null
          created_at?: string | null
          id?: number
          room_number?: string
        }
        Relationships: [
          {
            foreignKeyName: "hotel_rooms_accommodation_id_fkey"
            columns: ["accommodation_id"]
            isOneToOne: false
            referencedRelation: "accommodations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "hotel_rooms_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      inventory_notifications: {
        Row: {
          club_id: number
          created_at: string | null
          email_notifications: boolean | null
          id: number
          notify_users: Json | null
          system_notifications: boolean | null
          threshold: number
          updated_at: string | null
        }
        Insert: {
          club_id: number
          created_at?: string | null
          email_notifications?: boolean | null
          id?: number
          notify_users?: Json | null
          system_notifications?: boolean | null
          threshold?: number
          updated_at?: string | null
        }
        Update: {
          club_id?: number
          created_at?: string | null
          email_notifications?: boolean | null
          id?: number
          notify_users?: Json | null
          system_notifications?: boolean | null
          threshold?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "inventory_notifications_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      inventory_products: {
        Row: {
          club_id: number
          created_at: string | null
          department: string
          description: string | null
          expiration_date: string | null
          id: number
          image_url: string | null
          location: string | null
          minimum_quantity: number | null
          name: string
          quantity: number
          registration_date: string | null
          unit_of_measure: string | null
          updated_at: string | null
        }
        Insert: {
          club_id: number
          created_at?: string | null
          department: string
          description?: string | null
          expiration_date?: string | null
          id?: number
          image_url?: string | null
          location?: string | null
          minimum_quantity?: number | null
          name: string
          quantity?: number
          registration_date?: string | null
          unit_of_measure?: string | null
          updated_at?: string | null
        }
        Update: {
          club_id?: number
          created_at?: string | null
          department?: string
          description?: string | null
          expiration_date?: string | null
          id?: number
          image_url?: string | null
          location?: string | null
          minimum_quantity?: number | null
          name?: string
          quantity?: number
          registration_date?: string | null
          unit_of_measure?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "inventory_products_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      inventory_request_items: {
        Row: {
          club_id: number
          created_at: string | null
          id: number
          product_id: number
          quantity: number
          request_id: number
          returned_quantity: number | null
        }
        Insert: {
          club_id: number
          created_at?: string | null
          id?: number
          product_id: number
          quantity: number
          request_id: number
          returned_quantity?: number | null
        }
        Update: {
          club_id?: number
          created_at?: string | null
          id?: number
          product_id?: number
          quantity?: number
          request_id?: number
          returned_quantity?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "inventory_request_items_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_request_items_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "inventory_products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_request_items_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "inventory_requests"
            referencedColumns: ["id"]
          },
        ]
      }
      inventory_requests: {
        Row: {
          category: string
          club_id: number
          created_at: string | null
          delivery_location: string | null
          delivery_method: string
          delivery_notes: string | null
          delivery_signature_url: string | null
          department_id: number | null
          id: number
          requested_by: string
          requester_id: string | null
          requester_notes: string | null
          requester_signature_url: string | null
          requester_type: string
          status: string
          updated_at: string | null
          withdrawal_date: string
        }
        Insert: {
          category: string
          club_id: number
          created_at?: string | null
          delivery_location?: string | null
          delivery_method: string
          delivery_notes?: string | null
          delivery_signature_url?: string | null
          department_id?: number | null
          id?: number
          requested_by: string
          requester_id?: string | null
          requester_notes?: string | null
          requester_signature_url?: string | null
          requester_type: string
          status?: string
          updated_at?: string | null
          withdrawal_date: string
        }
        Update: {
          category?: string
          club_id?: number
          created_at?: string | null
          delivery_location?: string | null
          delivery_method?: string
          delivery_notes?: string | null
          delivery_signature_url?: string | null
          department_id?: number | null
          id?: number
          requested_by?: string
          requester_id?: string | null
          requester_notes?: string | null
          requester_signature_url?: string | null
          requester_type?: string
          status?: string
          updated_at?: string | null
          withdrawal_date?: string
        }
        Relationships: [
          {
            foreignKeyName: "inventory_requests_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_requests_department_id_fkey"
            columns: ["department_id"]
            isOneToOne: false
            referencedRelation: "departments"
            referencedColumns: ["id"]
          },
        ]
      }
      inventory_transactions: {
        Row: {
          club_id: number
          created_at: string | null
          id: number
          new_quantity: number
          notes: string | null
          previous_quantity: number
          product_id: number
          quantity: number
          transaction_type: string
          user_id: string
        }
        Insert: {
          club_id: number
          created_at?: string | null
          id?: number
          new_quantity: number
          notes?: string | null
          previous_quantity: number
          product_id: number
          quantity: number
          transaction_type: string
          user_id: string
        }
        Update: {
          club_id?: number
          created_at?: string | null
          id?: number
          new_quantity?: number
          notes?: string | null
          previous_quantity?: number
          product_id?: number
          quantity?: number
          transaction_type?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "inventory_transactions_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_transactions_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "inventory_products"
            referencedColumns: ["id"]
          },
        ]
      }
      match_events: {
        Row: {
          club_id: number
          created_at: string | null
          event_data: Json | null
          event_type: string
          id: string
          match_id: string | null
          minute: string | null
          player_id: string | null
          player_in_id: string | null
        }
        Insert: {
          club_id: number
          created_at?: string | null
          event_data?: Json | null
          event_type: string
          id?: string
          match_id?: string | null
          minute?: string | null
          player_id?: string | null
          player_in_id?: string | null
        }
        Update: {
          club_id?: number
          created_at?: string | null
          event_data?: Json | null
          event_type?: string
          id?: string
          match_id?: string | null
          minute?: string | null
          player_id?: string | null
          player_in_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "match_events_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "match_events_match_id_fkey"
            columns: ["match_id"]
            isOneToOne: false
            referencedRelation: "matches"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "match_events_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "players"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "match_events_player_in_id_fkey"
            columns: ["player_in_id"]
            isOneToOne: false
            referencedRelation: "players"
            referencedColumns: ["id"]
          },
        ]
      }
      matches: {
        Row: {
          cartoes: Json | null
          category_id: number | null
          club_id: number | null
          competition_id: string | null
          corners: number | null
          date: string
          escalacao: Json | null
          formation: string | null
          fouls: number | null
          id: string
          ida_volta: boolean | null
          location: string
          notes: string | null
          offsides: number | null
          opponent: string
          opponent_id: string | null
          result: string | null
          score_away: number | null
          score_home: number | null
          season_id: number | null
          shots: number | null
          shots_on_target: number | null
          time: string | null
          type: string | null
        }
        Insert: {
          cartoes?: Json | null
          category_id?: number | null
          club_id?: number | null
          competition_id?: string | null
          corners?: number | null
          date: string
          escalacao?: Json | null
          formation?: string | null
          fouls?: number | null
          id?: string
          ida_volta?: boolean | null
          location: string
          notes?: string | null
          offsides?: number | null
          opponent: string
          opponent_id?: string | null
          result?: string | null
          score_away?: number | null
          score_home?: number | null
          season_id?: number | null
          shots?: number | null
          shots_on_target?: number | null
          time?: string | null
          type?: string | null
        }
        Update: {
          cartoes?: Json | null
          category_id?: number | null
          club_id?: number | null
          competition_id?: string | null
          corners?: number | null
          date?: string
          escalacao?: Json | null
          formation?: string | null
          fouls?: number | null
          id?: string
          ida_volta?: boolean | null
          location?: string
          notes?: string | null
          offsides?: number | null
          opponent?: string
          opponent_id?: string | null
          result?: string | null
          score_away?: number | null
          score_home?: number | null
          season_id?: number | null
          shots?: number | null
          shots_on_target?: number | null
          time?: string | null
          type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "matches_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "matches_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "matches_competition_id_fkey"
            columns: ["competition_id"]
            isOneToOne: false
            referencedRelation: "competitions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "matches_opponent_id_fkey"
            columns: ["opponent_id"]
            isOneToOne: false
            referencedRelation: "opponents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "matches_season_id_fkey"
            columns: ["season_id"]
            isOneToOne: false
            referencedRelation: "seasons"
            referencedColumns: ["id"]
          },
        ]
      }
      meal_participants: {
        Row: {
          club_id: number
          created_at: string | null
          id: number
          meal_session_id: number
          participant_id: string
          participant_type: string
          signed: boolean | null
        }
        Insert: {
          club_id: number
          created_at?: string | null
          id?: number
          meal_session_id: number
          participant_id: string
          participant_type: string
          signed?: boolean | null
        }
        Update: {
          club_id?: number
          created_at?: string | null
          id?: number
          meal_session_id?: number
          participant_id?: string
          participant_type?: string
          signed?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "meal_participants_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "meal_participants_meal_session_id_fkey"
            columns: ["meal_session_id"]
            isOneToOne: false
            referencedRelation: "meal_sessions"
            referencedColumns: ["id"]
          },
        ]
      }
      meal_sessions: {
        Row: {
          accommodation_id: number
          club_id: number
          created_at: string | null
          date: string
          id: number
          meal_type_id: number
          notes: string | null
          time: string | null
        }
        Insert: {
          accommodation_id: number
          club_id: number
          created_at?: string | null
          date: string
          id?: number
          meal_type_id: number
          notes?: string | null
          time?: string | null
        }
        Update: {
          accommodation_id?: number
          club_id?: number
          created_at?: string | null
          date?: string
          id?: number
          meal_type_id?: number
          notes?: string | null
          time?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "meal_sessions_accommodation_id_fkey"
            columns: ["accommodation_id"]
            isOneToOne: false
            referencedRelation: "accommodations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "meal_sessions_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "meal_sessions_meal_type_id_fkey"
            columns: ["meal_type_id"]
            isOneToOne: false
            referencedRelation: "meal_types"
            referencedColumns: ["id"]
          },
        ]
      }
      meal_types: {
        Row: {
          address: string | null
          club_id: number
          created_at: string | null
          id: number
          location: string | null
          name: string
        }
        Insert: {
          address?: string | null
          club_id: number
          created_at?: string | null
          id?: number
          location?: string | null
          name: string
        }
        Update: {
          address?: string | null
          club_id?: number
          created_at?: string | null
          id?: number
          location?: string | null
          name?: string
        }
        Relationships: [
          {
            foreignKeyName: "meal_types_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      medical_appointments: {
        Row: {
          appointment_date: string
          appointment_time: string
          appointment_type: string
          club_id: number
          created_at: string
          created_by: string | null
          duration: number
          id: number
          location: string | null
          notes: string | null
          notification_sent: boolean | null
          player_id: string
          professional_id: number
          status: string
          updated_at: string
        }
        Insert: {
          appointment_date: string
          appointment_time: string
          appointment_type: string
          club_id: number
          created_at?: string
          created_by?: string | null
          duration?: number
          id?: number
          location?: string | null
          notes?: string | null
          notification_sent?: boolean | null
          player_id: string
          professional_id: number
          status?: string
          updated_at?: string
        }
        Update: {
          appointment_date?: string
          appointment_time?: string
          appointment_type?: string
          club_id?: number
          created_at?: string
          created_by?: string | null
          duration?: number
          id?: number
          location?: string | null
          notes?: string | null
          notification_sent?: boolean | null
          player_id?: string
          professional_id?: number
          status?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "medical_appointments_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "medical_appointments_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "players"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "medical_appointments_professional_id_fkey"
            columns: ["professional_id"]
            isOneToOne: false
            referencedRelation: "medical_professionals"
            referencedColumns: ["id"]
          },
        ]
      }
      medical_exams: {
        Row: {
          club_id: number
          created_at: string
          exam_date: string | null
          exam_type: string
          file_url: string | null
          id: number
          notes: string | null
          player_id: string
          record_id: number
          request_date: string
          requested_by: number
          result: string | null
          status: string
          updated_at: string
        }
        Insert: {
          club_id: number
          created_at?: string
          exam_date?: string | null
          exam_type: string
          file_url?: string | null
          id?: number
          notes?: string | null
          player_id: string
          record_id: number
          request_date: string
          requested_by: number
          result?: string | null
          status?: string
          updated_at?: string
        }
        Update: {
          club_id?: number
          created_at?: string
          exam_date?: string | null
          exam_type?: string
          file_url?: string | null
          id?: number
          notes?: string | null
          player_id?: string
          record_id?: number
          request_date?: string
          requested_by?: number
          result?: string | null
          status?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "medical_exams_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "medical_exams_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "players"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "medical_exams_record_id_fkey"
            columns: ["record_id"]
            isOneToOne: false
            referencedRelation: "medical_records"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "medical_exams_requested_by_fkey"
            columns: ["requested_by"]
            isOneToOne: false
            referencedRelation: "medical_professionals"
            referencedColumns: ["id"]
          },
        ]
      }
      medical_prescription_items: {
        Row: {
          club_id: number
          created_at: string
          dispensed: boolean | null
          dispensed_at: string | null
          dispensed_by: string | null
          dosage: string
          duration: string | null
          frequency: string
          id: number
          instructions: string | null
          prescription_id: number
          product_id: number
          quantity: number
        }
        Insert: {
          club_id: number
          created_at?: string
          dispensed?: boolean | null
          dispensed_at?: string | null
          dispensed_by?: string | null
          dosage: string
          duration?: string | null
          frequency: string
          id?: number
          instructions?: string | null
          prescription_id: number
          product_id: number
          quantity: number
        }
        Update: {
          club_id?: number
          created_at?: string
          dispensed?: boolean | null
          dispensed_at?: string | null
          dispensed_by?: string | null
          dosage?: string
          duration?: string | null
          frequency?: string
          id?: number
          instructions?: string | null
          prescription_id?: number
          product_id?: number
          quantity?: number
        }
        Relationships: [
          {
            foreignKeyName: "medical_prescription_items_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "medical_prescription_items_prescription_id_fkey"
            columns: ["prescription_id"]
            isOneToOne: false
            referencedRelation: "medical_prescriptions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "medical_prescription_items_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "inventory_products"
            referencedColumns: ["id"]
          },
        ]
      }
      medical_prescriptions: {
        Row: {
          club_id: number
          created_at: string
          expiration_date: string | null
          id: number
          issue_date: string
          notes: string | null
          player_id: string
          professional_id: number
          record_id: number
          signature_url: string | null
          status: string
        }
        Insert: {
          club_id: number
          created_at?: string
          expiration_date?: string | null
          id?: number
          issue_date: string
          notes?: string | null
          player_id: string
          professional_id: number
          record_id: number
          signature_url?: string | null
          status?: string
        }
        Update: {
          club_id?: number
          created_at?: string
          expiration_date?: string | null
          id?: number
          issue_date?: string
          notes?: string | null
          player_id?: string
          professional_id?: number
          record_id?: number
          signature_url?: string | null
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "medical_prescriptions_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "medical_prescriptions_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "players"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "medical_prescriptions_professional_id_fkey"
            columns: ["professional_id"]
            isOneToOne: false
            referencedRelation: "medical_professionals"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "medical_prescriptions_record_id_fkey"
            columns: ["record_id"]
            isOneToOne: false
            referencedRelation: "medical_records"
            referencedColumns: ["id"]
          },
        ]
      }
      medical_professionals: {
        Row: {
          address: string | null
          birth_date: string | null
          certificate_url: string | null
          city: string | null
          club_id: number
          cpf: string | null
          created_at: string | null
          credential: string | null
          id: number
          name: string
          role: string
          state: string | null
          user_id: string | null
          zip_code: string | null
        }
        Insert: {
          address?: string | null
          birth_date?: string | null
          certificate_url?: string | null
          city?: string | null
          club_id: number
          cpf?: string | null
          created_at?: string | null
          credential?: string | null
          id?: number
          name: string
          role: string
          state?: string | null
          user_id?: string | null
          zip_code?: string | null
        }
        Update: {
          address?: string | null
          birth_date?: string | null
          certificate_url?: string | null
          city?: string | null
          club_id?: number
          cpf?: string | null
          created_at?: string | null
          credential?: string | null
          id?: number
          name?: string
          role?: string
          state?: string | null
          user_id?: string | null
          zip_code?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "medical_professionals_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "medical_professionals_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      medical_records: {
        Row: {
          club_id: number | null
          color_status: string | null
          completed: boolean | null
          completed_at: string | null
          completed_by: number | null
          date: string
          description: string
          diagnosis: string | null
          doctor: string | null
          doctor_id: number | null
          id: number
          is_referral: boolean | null
          last_evolution_date: string | null
          player_id: string | null
          referred_by: string | null
          severity: string | null
          status: string | null
          symptoms: string | null
          treatment_plan: string | null
          updated_at: string | null
          viewed: boolean | null
          viewed_at: string | null
        }
        Insert: {
          club_id?: number | null
          color_status?: string | null
          completed?: boolean | null
          completed_at?: string | null
          completed_by?: number | null
          date: string
          description: string
          diagnosis?: string | null
          doctor?: string | null
          doctor_id?: number | null
          id?: number
          is_referral?: boolean | null
          last_evolution_date?: string | null
          player_id?: string | null
          referred_by?: string | null
          severity?: string | null
          status?: string | null
          symptoms?: string | null
          treatment_plan?: string | null
          updated_at?: string | null
          viewed?: boolean | null
          viewed_at?: string | null
        }
        Update: {
          club_id?: number | null
          color_status?: string | null
          completed?: boolean | null
          completed_at?: string | null
          completed_by?: number | null
          date?: string
          description?: string
          diagnosis?: string | null
          doctor?: string | null
          doctor_id?: number | null
          id?: number
          is_referral?: boolean | null
          last_evolution_date?: string | null
          player_id?: string | null
          referred_by?: string | null
          severity?: string | null
          status?: string | null
          symptoms?: string | null
          treatment_plan?: string | null
          updated_at?: string | null
          viewed?: boolean | null
          viewed_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "medical_records_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "medical_records_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "players"
            referencedColumns: ["id"]
          },
        ]
      }
      medical_treatment_evolution: {
        Row: {
          club_id: number
          created_at: string
          date: string
          description: string
          id: number
          procedures: string[] | null
          professional_id: number
          record_id: number
          response: string | null
          signature_url: string | null
          status: string
        }
        Insert: {
          club_id: number
          created_at?: string
          date: string
          description: string
          id?: number
          procedures?: string[] | null
          professional_id: number
          record_id: number
          response?: string | null
          signature_url?: string | null
          status: string
        }
        Update: {
          club_id?: number
          created_at?: string
          date?: string
          description?: string
          id?: number
          procedures?: string[] | null
          professional_id?: number
          record_id?: number
          response?: string | null
          signature_url?: string | null
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "medical_treatment_evolution_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "medical_treatment_evolution_professional_id_fkey"
            columns: ["professional_id"]
            isOneToOne: false
            referencedRelation: "medical_professionals"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "medical_treatment_evolution_record_id_fkey"
            columns: ["record_id"]
            isOneToOne: false
            referencedRelation: "medical_records"
            referencedColumns: ["id"]
          },
        ]
      }
      notifications: {
        Row: {
          club_id: number | null
          created_at: string | null
          description: string | null
          id: number
          message: string | null
          read: boolean | null
          reference_id: string | null
          reference_type: string | null
          scheduled_for: string | null
          sent: boolean | null
          sent_at: string | null
          title: string
          type: string | null
          user_id: string | null
        }
        Insert: {
          club_id?: number | null
          created_at?: string | null
          description?: string | null
          id?: number
          message?: string | null
          read?: boolean | null
          reference_id?: string | null
          reference_type?: string | null
          scheduled_for?: string | null
          sent?: boolean | null
          sent_at?: string | null
          title: string
          type?: string | null
          user_id?: string | null
        }
        Update: {
          club_id?: number | null
          created_at?: string | null
          description?: string | null
          id?: number
          message?: string | null
          read?: boolean | null
          reference_id?: string | null
          reference_type?: string | null
          scheduled_for?: string | null
          sent?: boolean | null
          sent_at?: string | null
          title?: string
          type?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "notifications_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      opponents: {
        Row: {
          city: string | null
          club_id: number
          country: string | null
          created_at: string | null
          id: string
          logo_url: string | null
          name: string
          stadium: string | null
          updated_at: string | null
        }
        Insert: {
          city?: string | null
          club_id: number
          country?: string | null
          created_at?: string | null
          id?: string
          logo_url?: string | null
          name: string
          stadium?: string | null
          updated_at?: string | null
        }
        Update: {
          city?: string | null
          club_id?: number
          country?: string | null
          created_at?: string | null
          id?: string
          logo_url?: string | null
          name?: string
          stadium?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "opponents_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      permissions: {
        Row: {
          category: string
          created_at: string | null
          description: string
          id: number
          name: string
        }
        Insert: {
          category: string
          created_at?: string | null
          description: string
          id?: number
          name: string
        }
        Update: {
          category?: string
          created_at?: string | null
          description?: string
          id?: number
          name?: string
        }
        Relationships: []
      }
      physical_progress: {
        Row: {
          club_id: number | null
          created_at: string | null
          fitness_avg: number | null
          id: number
          resistance: number | null
          speed: number | null
          strength: number | null
          week_start: string
        }
        Insert: {
          club_id?: number | null
          created_at?: string | null
          fitness_avg?: number | null
          id?: number
          resistance?: number | null
          speed?: number | null
          strength?: number | null
          week_start: string
        }
        Update: {
          club_id?: number | null
          created_at?: string | null
          fitness_avg?: number | null
          id?: number
          resistance?: number | null
          speed?: number | null
          strength?: number | null
          week_start?: string
        }
        Relationships: [
          {
            foreignKeyName: "physical_progress_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      player_accommodations: {
        Row: {
          accommodation_id: number | null
          check_in_date: string | null
          check_out_date: string | null
          club_id: number | null
          created_at: string | null
          hotel_room_id: number | null
          id: number
          notes: string | null
          player_id: string | null
          room_number: string | null
          status: string | null
        }
        Insert: {
          accommodation_id?: number | null
          check_in_date?: string | null
          check_out_date?: string | null
          club_id?: number | null
          created_at?: string | null
          hotel_room_id?: number | null
          id?: number
          notes?: string | null
          player_id?: string | null
          room_number?: string | null
          status?: string | null
        }
        Update: {
          accommodation_id?: number | null
          check_in_date?: string | null
          check_out_date?: string | null
          club_id?: number | null
          created_at?: string | null
          hotel_room_id?: number | null
          id?: number
          notes?: string | null
          player_id?: string | null
          room_number?: string | null
          status?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "player_accommodations_accommodation_id_fkey"
            columns: ["accommodation_id"]
            isOneToOne: false
            referencedRelation: "accommodations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_accommodations_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_accommodations_hotel_room_id_fkey"
            columns: ["hotel_room_id"]
            isOneToOne: false
            referencedRelation: "hotel_rooms"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_accommodations_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "players"
            referencedColumns: ["id"]
          },
        ]
      }
      player_accounts: {
        Row: {
          club_id: number | null
          created_at: string | null
          expires_at: string | null
          id: number
          player_id: string | null
          user_id: string | null
        }
        Insert: {
          club_id?: number | null
          created_at?: string | null
          expires_at?: string | null
          id?: number
          player_id?: string | null
          user_id?: string | null
        }
        Update: {
          club_id?: number | null
          created_at?: string | null
          expires_at?: string | null
          id?: number
          player_id?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "player_accounts_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_accounts_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "players"
            referencedColumns: ["id"]
          },
        ]
      }
      player_categories: {
        Row: {
          category_id: number | null
          club_id: number | null
          created_at: string | null
          id: number
          player_id: string | null
        }
        Insert: {
          category_id?: number | null
          club_id?: number | null
          created_at?: string | null
          id?: number
          player_id?: string | null
        }
        Update: {
          category_id?: number | null
          club_id?: number | null
          created_at?: string | null
          id?: number
          player_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "player_categories_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_categories_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_categories_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "players"
            referencedColumns: ["id"]
          },
        ]
      }
      player_documents: {
        Row: {
          club_id: number | null
          document_type: string
          file_url: string
          id: number
          player_id: string | null
          rejection_reason: string | null
          status: string | null
          uploaded_at: string | null
          verified_at: string | null
          verified_by: string | null
        }
        Insert: {
          club_id?: number | null
          document_type: string
          file_url: string
          id?: number
          player_id?: string | null
          rejection_reason?: string | null
          status?: string | null
          uploaded_at?: string | null
          verified_at?: string | null
          verified_by?: string | null
        }
        Update: {
          club_id?: number | null
          document_type?: string
          file_url?: string
          id?: number
          player_id?: string | null
          rejection_reason?: string | null
          status?: string | null
          uploaded_at?: string | null
          verified_at?: string | null
          verified_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "player_documents_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_documents_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "players"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_documents_verified_by_fkey"
            columns: ["verified_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      player_evaluation_invitations: {
        Row: {
          club_id: number | null
          cpf: string | null
          created_at: string | null
          created_by: string | null
          documents_rejection_reason: string | null
          documents_status: string | null
          documents_verified_at: string | null
          documents_verified_by: string | null
          email: string
          evaluation_date: string | null
          evaluation_location: string | null
          evaluation_notes: string | null
          evaluation_requirements: string | null
          evaluation_status: string | null
          expires_at: string | null
          id: number
          player_id: string | null
          status: string | null
          token: string
          used_at: string | null
        }
        Insert: {
          club_id?: number | null
          cpf?: string | null
          created_at?: string | null
          created_by?: string | null
          documents_rejection_reason?: string | null
          documents_status?: string | null
          documents_verified_at?: string | null
          documents_verified_by?: string | null
          email: string
          evaluation_date?: string | null
          evaluation_location?: string | null
          evaluation_notes?: string | null
          evaluation_requirements?: string | null
          evaluation_status?: string | null
          expires_at?: string | null
          id?: number
          player_id?: string | null
          status?: string | null
          token: string
          used_at?: string | null
        }
        Update: {
          club_id?: number | null
          cpf?: string | null
          created_at?: string | null
          created_by?: string | null
          documents_rejection_reason?: string | null
          documents_status?: string | null
          documents_verified_at?: string | null
          documents_verified_by?: string | null
          email?: string
          evaluation_date?: string | null
          evaluation_location?: string | null
          evaluation_notes?: string | null
          evaluation_requirements?: string | null
          evaluation_status?: string | null
          expires_at?: string | null
          id?: number
          player_id?: string | null
          status?: string | null
          token?: string
          used_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "player_evaluation_invitations_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluation_invitations_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "players"
            referencedColumns: ["id"]
          },
        ]
      }
      player_evaluations: {
        Row: {
          approved_by_manager: string | null
          approved_by_president: string | null
          club_id: number
          content: string
          created_at: string | null
          created_by: string
          id: number
          is_locked: boolean | null
          last_viewed_at: string | null
          manager_approved_at: string | null
          manager_notes: string | null
          player_id: string
          president_approved_at: string | null
          president_notes: string | null
          requires_manager_approval: boolean | null
          requires_president_approval: boolean | null
          signature_url: string | null
          status: string | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          approved_by_manager?: string | null
          approved_by_president?: string | null
          club_id: number
          content: string
          created_at?: string | null
          created_by: string
          id?: number
          is_locked?: boolean | null
          last_viewed_at?: string | null
          manager_approved_at?: string | null
          manager_notes?: string | null
          player_id: string
          president_approved_at?: string | null
          president_notes?: string | null
          requires_manager_approval?: boolean | null
          requires_president_approval?: boolean | null
          signature_url?: string | null
          status?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          approved_by_manager?: string | null
          approved_by_president?: string | null
          club_id?: number
          content?: string
          created_at?: string | null
          created_by?: string
          id?: number
          is_locked?: boolean | null
          last_viewed_at?: string | null
          manager_approved_at?: string | null
          manager_notes?: string | null
          player_id?: string
          president_approved_at?: string | null
          president_notes?: string | null
          requires_manager_approval?: boolean | null
          requires_president_approval?: boolean | null
          signature_url?: string | null
          status?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "player_evaluations_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluations_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluations_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "players"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluations_updated_by_fkey"
            columns: ["updated_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      player_match_statistics: {
        Row: {
          assists: number | null
          club_id: number
          created_at: string | null
          fouls_committed: number | null
          fouls_suffered: number | null
          goals: number | null
          id: number
          interceptions: number | null
          key_passes: number | null
          match_id: string
          minutes_played: number | null
          passes: number | null
          passes_completed: number | null
          player_id: string
          red_cards: number | null
          shots: number | null
          shots_on_target: number | null
          tackles: number | null
          updated_at: string | null
          yellow_cards: number | null
        }
        Insert: {
          assists?: number | null
          club_id: number
          created_at?: string | null
          fouls_committed?: number | null
          fouls_suffered?: number | null
          goals?: number | null
          id?: number
          interceptions?: number | null
          key_passes?: number | null
          match_id: string
          minutes_played?: number | null
          passes?: number | null
          passes_completed?: number | null
          player_id: string
          red_cards?: number | null
          shots?: number | null
          shots_on_target?: number | null
          tackles?: number | null
          updated_at?: string | null
          yellow_cards?: number | null
        }
        Update: {
          assists?: number | null
          club_id?: number
          created_at?: string | null
          fouls_committed?: number | null
          fouls_suffered?: number | null
          goals?: number | null
          id?: number
          interceptions?: number | null
          key_passes?: number | null
          match_id?: string
          minutes_played?: number | null
          passes?: number | null
          passes_completed?: number | null
          player_id?: string
          red_cards?: number | null
          shots?: number | null
          shots_on_target?: number | null
          tackles?: number | null
          updated_at?: string | null
          yellow_cards?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "player_match_statistics_match_id_fkey"
            columns: ["match_id"]
            isOneToOne: false
            referencedRelation: "matches"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_match_statistics_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "players"
            referencedColumns: ["id"]
          },
        ]
      }
      player_salaries: {
        Row: {
          club_id: number
          created_at: string | null
          details: string | null
          end_date: string | null
          id: number
          player_id: string
          start_date: string
          status: string
          value: number
        }
        Insert: {
          club_id: number
          created_at?: string | null
          details?: string | null
          end_date?: string | null
          id?: never
          player_id: string
          start_date: string
          status?: string
          value: number
        }
        Update: {
          club_id?: number
          created_at?: string | null
          details?: string | null
          end_date?: string | null
          id?: never
          player_id?: string
          start_date?: string
          status?: string
          value?: number
        }
        Relationships: [
          {
            foreignKeyName: "player_salaries_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_salaries_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "players"
            referencedColumns: ["id"]
          },
        ]
      }
      players: {
        Row: {
          accommodation_id: number | null
          address: string | null
          age: number
          bank_account_number: string | null
          bank_account_type: string | null
          bank_branch: string | null
          bank_name: string | null
          bank_pix_key: string | null
          birthdate: string | null
          birthplace: string | null
          championship_registration: string | null
          city: string | null
          club_id: number | null
          contract_end_date: string | null
          cpf_number: string | null
          email: string | null
          entry_date: string | null
          exit_date: string | null
          father_name: string | null
          financial_data: Json | null
          height: number | null
          id: string
          image: string | null
          is_accommodated: boolean | null
          loan_club_name: string | null
          loan_end_date: string | null
          mother_name: string | null
          name: string
          nationality: string | null
          nickname: string | null
          number: number
          observation: string | null
          phone: string | null
          position: string
          professional_status: string | null
          referred_by: string | null
          registration_number: string | null
          rg_number: string | null
          role: string | null
          state: string | null
          stats: Json | null
          status: string
          user_id: string | null
          weight: number | null
          zip_code: string | null
        }
        Insert: {
          accommodation_id?: number | null
          address?: string | null
          age: number
          bank_account_number?: string | null
          bank_account_type?: string | null
          bank_branch?: string | null
          bank_name?: string | null
          bank_pix_key?: string | null
          birthdate?: string | null
          birthplace?: string | null
          championship_registration?: string | null
          city?: string | null
          club_id?: number | null
          contract_end_date?: string | null
          cpf_number?: string | null
          email?: string | null
          entry_date?: string | null
          exit_date?: string | null
          father_name?: string | null
          financial_data?: Json | null
          height?: number | null
          id?: string
          image?: string | null
          is_accommodated?: boolean | null
          loan_club_name?: string | null
          loan_end_date?: string | null
          mother_name?: string | null
          name: string
          nationality?: string | null
          nickname?: string | null
          number: number
          observation?: string | null
          phone?: string | null
          position: string
          professional_status?: string | null
          referred_by?: string | null
          registration_number?: string | null
          rg_number?: string | null
          role?: string | null
          state?: string | null
          stats?: Json | null
          status: string
          user_id?: string | null
          weight?: number | null
          zip_code?: string | null
        }
        Update: {
          accommodation_id?: number | null
          address?: string | null
          age?: number
          bank_account_number?: string | null
          bank_account_type?: string | null
          bank_branch?: string | null
          bank_name?: string | null
          bank_pix_key?: string | null
          birthdate?: string | null
          birthplace?: string | null
          championship_registration?: string | null
          city?: string | null
          club_id?: number | null
          contract_end_date?: string | null
          cpf_number?: string | null
          email?: string | null
          entry_date?: string | null
          exit_date?: string | null
          father_name?: string | null
          financial_data?: Json | null
          height?: number | null
          id?: string
          image?: string | null
          is_accommodated?: boolean | null
          loan_club_name?: string | null
          loan_end_date?: string | null
          mother_name?: string | null
          name?: string
          nationality?: string | null
          nickname?: string | null
          number?: number
          observation?: string | null
          phone?: string | null
          position?: string
          professional_status?: string | null
          referred_by?: string | null
          registration_number?: string | null
          rg_number?: string | null
          role?: string | null
          state?: string | null
          stats?: Json | null
          status?: string
          user_id?: string | null
          weight?: number | null
          zip_code?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "players_accommodation_id_fkey"
            columns: ["accommodation_id"]
            isOneToOne: false
            referencedRelation: "accommodations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "players_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "players_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      rehab_sessions: {
        Row: {
          activity: string
          archived: boolean | null
          club_id: number
          created_at: string | null
          date: string
          duration: number
          id: number
          location: string | null
          notes: string | null
          player_id: string
          professional: string
          professional_id: number | null
          status: string
          time: string
          treatment_description: string[] | null
        }
        Insert: {
          activity: string
          archived?: boolean | null
          club_id: number
          created_at?: string | null
          date: string
          duration: number
          id?: number
          location?: string | null
          notes?: string | null
          player_id: string
          professional: string
          professional_id?: number | null
          status: string
          time: string
          treatment_description?: string[] | null
        }
        Update: {
          activity?: string
          archived?: boolean | null
          club_id?: number
          created_at?: string | null
          date?: string
          duration?: number
          id?: number
          location?: string | null
          notes?: string | null
          player_id?: string
          professional?: string
          professional_id?: number | null
          status?: string
          time?: string
          treatment_description?: string[] | null
        }
        Relationships: [
          {
            foreignKeyName: "rehab_sessions_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rehab_sessions_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "players"
            referencedColumns: ["id"]
          },
        ]
      }
      salary_advances: {
        Row: {
          advance_date: string
          amount: number
          club_id: number
          created_at: string | null
          created_by: string | null
          description: string | null
          id: number
          month: number
          payment_method: string | null
          person_id: number
          person_id_str: string | null
          person_type: string
          receipt_url: string | null
          status: string
          year: number
        }
        Insert: {
          advance_date: string
          amount: number
          club_id: number
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: number
          month: number
          payment_method?: string | null
          person_id: number
          person_id_str?: string | null
          person_type: string
          receipt_url?: string | null
          status?: string
          year: number
        }
        Update: {
          advance_date?: string
          amount?: number
          club_id?: number
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: number
          month?: number
          payment_method?: string | null
          person_id?: number
          person_id_str?: string | null
          person_type?: string
          receipt_url?: string | null
          status?: string
          year?: number
        }
        Relationships: [
          {
            foreignKeyName: "salary_advances_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      seasons: {
        Row: {
          club_id: number | null
          created_at: string | null
          end_date: string | null
          id: number
          name: string
          start_date: string | null
        }
        Insert: {
          club_id?: number | null
          created_at?: string | null
          end_date?: string | null
          id?: number
          name: string
          start_date?: string | null
        }
        Update: {
          club_id?: number | null
          created_at?: string | null
          end_date?: string | null
          id?: number
          name?: string
          start_date?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "seasons_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      supplier_orders: {
        Row: {
          amount: number
          club_id: number
          created_at: string | null
          description: string
          financial_account_id: number | null
          id: number
          purchase_date: string
          supplier_id: number
          updated_at: string | null
        }
        Insert: {
          amount: number
          club_id: number
          created_at?: string | null
          description: string
          financial_account_id?: number | null
          id?: number
          purchase_date?: string
          supplier_id: number
          updated_at?: string | null
        }
        Update: {
          amount?: number
          club_id?: number
          created_at?: string | null
          description?: string
          financial_account_id?: number | null
          id?: number
          purchase_date?: string
          supplier_id?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "supplier_orders_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "supplier_orders_financial_account_id_fkey"
            columns: ["financial_account_id"]
            isOneToOne: false
            referencedRelation: "financial_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "supplier_orders_supplier_id_fkey"
            columns: ["supplier_id"]
            isOneToOne: false
            referencedRelation: "suppliers"
            referencedColumns: ["id"]
          },
        ]
      }
      suppliers: {
        Row: {
          address: string | null
          address_number: string | null
          bank_account: string | null
          bank_agency: string | null
          bank_name: string | null
          bank_pix: string | null
          city: string | null
          club_id: number
          company_name: string
          created_at: string | null
          email: string | null
          expiration_date: string | null
          id: number
          phone1: string | null
          phone2: string | null
          state: string | null
          updated_at: string | null
          zip_code: string | null
        }
        Insert: {
          address?: string | null
          address_number?: string | null
          bank_account?: string | null
          bank_agency?: string | null
          bank_name?: string | null
          bank_pix?: string | null
          city?: string | null
          club_id: number
          company_name: string
          created_at?: string | null
          email?: string | null
          expiration_date?: string | null
          id?: number
          phone1?: string | null
          phone2?: string | null
          state?: string | null
          updated_at?: string | null
          zip_code?: string | null
        }
        Update: {
          address?: string | null
          address_number?: string | null
          bank_account?: string | null
          bank_agency?: string | null
          bank_name?: string | null
          bank_pix?: string | null
          city?: string | null
          club_id?: number
          company_name?: string
          created_at?: string | null
          email?: string | null
          expiration_date?: string | null
          id?: number
          phone1?: string | null
          phone2?: string | null
          state?: string | null
          updated_at?: string | null
          zip_code?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "suppliers_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      task_types: {
        Row: {
          club_id: number
          color: string | null
          created_at: string | null
          id: number
          name: string
          position: number | null
          updated_at: string | null
        }
        Insert: {
          club_id: number
          color?: string | null
          created_at?: string | null
          id?: number
          name: string
          position?: number | null
          updated_at?: string | null
        }
        Update: {
          club_id?: number
          color?: string | null
          created_at?: string | null
          id?: number
          name?: string
          position?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "task_types_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      tasks: {
        Row: {
          club_id: number | null
          description: string | null
          due_date: string | null
          id: number
          status: string | null
          title: string
        }
        Insert: {
          club_id?: number | null
          description?: string | null
          due_date?: string | null
          id?: number
          status?: string | null
          title: string
        }
        Update: {
          club_id?: number | null
          description?: string | null
          due_date?: string | null
          id?: number
          status?: string | null
          title?: string
        }
        Relationships: [
          {
            foreignKeyName: "tasks_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      training_exercises: {
        Row: {
          exercise_id: number | null
          id: number
          notes: string | null
          order_in_training: number | null
          training_id: number | null
        }
        Insert: {
          exercise_id?: number | null
          id?: number
          notes?: string | null
          order_in_training?: number | null
          training_id?: number | null
        }
        Update: {
          exercise_id?: number | null
          id?: number
          notes?: string | null
          order_in_training?: number | null
          training_id?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "training_exercises_exercise_id_fkey"
            columns: ["exercise_id"]
            isOneToOne: false
            referencedRelation: "exercises"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "training_exercises_training_id_fkey"
            columns: ["training_id"]
            isOneToOne: false
            referencedRelation: "trainings"
            referencedColumns: ["id"]
          },
        ]
      }
      training_goals: {
        Row: {
          club_id: number
          created_at: string | null
          current_value: number | null
          description: string | null
          id: number
          name: string
          target_value: number
          type: string
        }
        Insert: {
          club_id: number
          created_at?: string | null
          current_value?: number | null
          description?: string | null
          id?: number
          name: string
          target_value: number
          type: string
        }
        Update: {
          club_id?: number
          created_at?: string | null
          current_value?: number | null
          description?: string | null
          id?: number
          name?: string
          target_value?: number
          type?: string
        }
        Relationships: [
          {
            foreignKeyName: "training_goals_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      training_images: {
        Row: {
          club_id: number
          created_at: string | null
          id: number
          image_order: number
          image_url: string
          training_id: number
        }
        Insert: {
          club_id: number
          created_at?: string | null
          id?: number
          image_order?: number
          image_url: string
          training_id: number
        }
        Update: {
          club_id?: number
          created_at?: string | null
          id?: number
          image_order?: number
          image_url?: string
          training_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "training_images_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "training_images_training_id_fkey"
            columns: ["training_id"]
            isOneToOne: false
            referencedRelation: "trainings"
            referencedColumns: ["id"]
          },
        ]
      }
      training_players: {
        Row: {
          club_id: number
          created_at: string | null
          id: number
          player_id: string
          training_id: number
        }
        Insert: {
          club_id: number
          created_at?: string | null
          id?: number
          player_id: string
          training_id: number
        }
        Update: {
          club_id?: number
          created_at?: string | null
          id?: number
          player_id?: string
          training_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "training_players_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "training_players_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "players"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "training_players_training_id_fkey"
            columns: ["training_id"]
            isOneToOne: false
            referencedRelation: "trainings"
            referencedColumns: ["id"]
          },
        ]
      }
      trainings: {
        Row: {
          category_id: number | null
          club_id: number | null
          date: string
          id: number
          notes: string | null
          required_materials: string | null
        }
        Insert: {
          category_id?: number | null
          club_id?: number | null
          date: string
          id?: number
          notes?: string | null
          required_materials?: string | null
        }
        Update: {
          category_id?: number | null
          club_id?: number | null
          date?: string
          id?: number
          notes?: string | null
          required_materials?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "trainings_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "trainings_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      user_departments: {
        Row: {
          club_id: number | null
          created_at: string | null
          department_id: number | null
          id: number
          permissions: Json | null
          role: string
          user_id: string | null
        }
        Insert: {
          club_id?: number | null
          created_at?: string | null
          department_id?: number | null
          id?: number
          permissions?: Json | null
          role: string
          user_id?: string | null
        }
        Update: {
          club_id?: number | null
          created_at?: string | null
          department_id?: number | null
          id?: number
          permissions?: Json | null
          role?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_departments_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_departments_department_id_fkey"
            columns: ["department_id"]
            isOneToOne: false
            referencedRelation: "departments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_departments_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_invitations: {
        Row: {
          club_id: number | null
          collaborator_id: number | null
          created_at: string | null
          custom_password: string | null
          department_id: number | null
          email: string
          expires_at: string | null
          id: number
          permissions: Json | null
          role: string
          status: string | null
          token: string
        }
        Insert: {
          club_id?: number | null
          collaborator_id?: number | null
          created_at?: string | null
          custom_password?: string | null
          department_id?: number | null
          email: string
          expires_at?: string | null
          id?: number
          permissions?: Json | null
          role: string
          status?: string | null
          token: string
        }
        Update: {
          club_id?: number | null
          collaborator_id?: number | null
          created_at?: string | null
          custom_password?: string | null
          department_id?: number | null
          email?: string
          expires_at?: string | null
          id?: number
          permissions?: Json | null
          role?: string
          status?: string | null
          token?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_invitations_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_invitations_collaborator_id_fkey"
            columns: ["collaborator_id"]
            isOneToOne: false
            referencedRelation: "collaborators"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_invitations_collaborator_id_fkey"
            columns: ["collaborator_id"]
            isOneToOne: false
            referencedRelation: "collaborators_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_invitations_department_id_fkey"
            columns: ["department_id"]
            isOneToOne: false
            referencedRelation: "departments"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          email: string
          first_login: boolean | null
          id: string
          name: string
          permissions: Json | null
          profile_image: string | null
          role: string | null
        }
        Insert: {
          email: string
          first_login?: boolean | null
          id: string
          name: string
          permissions?: Json | null
          profile_image?: string | null
          role?: string | null
        }
        Update: {
          email?: string
          first_login?: boolean | null
          id?: string
          name?: string
          permissions?: Json | null
          profile_image?: string | null
          role?: string | null
        }
        Relationships: []
      }
      youth_players: {
        Row: {
          age: number
          birthdate: string | null
          birthplace: string | null
          category: string | null
          club_id: number | null
          height: number | null
          id: string
          image: string | null
          name: string
          nationality: string | null
          number: number
          position: string
          potential: number | null
          since: string | null
          status: string
          weight: number | null
        }
        Insert: {
          age: number
          birthdate?: string | null
          birthplace?: string | null
          category?: string | null
          club_id?: number | null
          height?: number | null
          id?: string
          image?: string | null
          name: string
          nationality?: string | null
          number: number
          position: string
          potential?: number | null
          since?: string | null
          status: string
          weight?: number | null
        }
        Update: {
          age?: number
          birthdate?: string | null
          birthplace?: string | null
          category?: string | null
          club_id?: number | null
          height?: number | null
          id?: string
          image?: string | null
          name?: string
          nationality?: string | null
          number?: number
          position?: string
          potential?: number | null
          since?: string | null
          status?: string
          weight?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "youth_players_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      administrative_documents_view: {
        Row: {
          club_id: number | null
          content: string | null
          created_at: string | null
          created_by: string | null
          creator_name: string | null
          digital_signature: boolean | null
          document_number: number | null
          document_type: string | null
          id: number | null
          signature_url: string | null
          signed_at: string | null
          signed_by: string | null
          signer_name: string | null
          title: string | null
          updated_at: string | null
        }
        Relationships: [
          {
            foreignKeyName: "administrative_documents_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      administrative_reminders_view: {
        Row: {
          club_id: number | null
          completed: boolean | null
          created_at: string | null
          created_by: string | null
          creator_name: string | null
          description: string | null
          id: number | null
          reminder_date: string | null
          reminder_type: string | null
          title: string | null
          updated_at: string | null
        }
        Relationships: [
          {
            foreignKeyName: "administrative_reminders_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      administrative_tasks_view: {
        Row: {
          club_id: number | null
          collaborator_id: number | null
          collaborator_name: string | null
          created_at: string | null
          created_by: string | null
          creator_name: string | null
          description: string | null
          due_date: string | null
          id: number | null
          responsible: string | null
          responsible_name: string | null
          status: string | null
          title: string | null
          updated_at: string | null
        }
        Relationships: [
          {
            foreignKeyName: "administrative_tasks_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "administrative_tasks_collaborator_id_fkey"
            columns: ["collaborator_id"]
            isOneToOne: false
            referencedRelation: "collaborators"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "administrative_tasks_collaborator_id_fkey"
            columns: ["collaborator_id"]
            isOneToOne: false
            referencedRelation: "collaborators_view"
            referencedColumns: ["id"]
          },
        ]
      }
      collaborators_view: {
        Row: {
          address: string | null
          address_number: string | null
          bank_info: Json | null
          birth_date: string | null
          bonus: number | null
          certificate_url: string | null
          city: string | null
          club_id: number | null
          cpf: string | null
          created_at: string | null
          credential_number: string | null
          criminal_record_url: string | null
          document_id: string | null
          document_id_url: string | null
          email: string | null
          entry_date: string | null
          financial_data: Json | null
          full_name: string | null
          id: number | null
          image: string | null
          medical_certificate_url: string | null
          phone: string | null
          registration_number: string | null
          resume_url: string | null
          role: string | null
          role_type: string | null
          salary: number | null
          state: string | null
          status: string | null
          updated_at: string | null
          user_email: string | null
          user_id: string | null
          user_name: string | null
          zip_code: string | null
        }
        Relationships: [
          {
            foreignKeyName: "collaborators_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
        ]
      }
      supplier_orders_view: {
        Row: {
          account_description: string | null
          account_due_date: string | null
          account_status: string | null
          amount: number | null
          club_id: number | null
          created_at: string | null
          description: string | null
          financial_account_id: number | null
          id: number | null
          purchase_date: string | null
          supplier_id: number | null
          supplier_name: string | null
          updated_at: string | null
        }
        Relationships: [
          {
            foreignKeyName: "supplier_orders_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "club_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "supplier_orders_financial_account_id_fkey"
            columns: ["financial_account_id"]
            isOneToOne: false
            referencedRelation: "financial_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "supplier_orders_supplier_id_fkey"
            columns: ["supplier_id"]
            isOneToOne: false
            referencedRelation: "suppliers"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      add_user_to_club: {
        Args: {
          p_user_id: string
          p_club_id: number
          p_role: string
          p_status: string
        }
        Returns: Json
      }
      calculate_age: {
        Args: { birth_date: string }
        Returns: number
      }
      check_permission: {
        Args: { p_club_id: number; p_user_id: string; p_permission: string }
        Returns: boolean
      }
      confirm_user_email: {
        Args: { user_id: string }
        Returns: undefined
      }
      confirm_user_email_direct: {
        Args: { user_id: string }
        Returns: boolean
      }
      delete_collaborator_document: {
        Args: { p_document_id: number }
        Returns: undefined
      }
      execute_sql: {
        Args: { sql_query: string }
        Returns: Json
      }
      generate_registration_number: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_collaborator_documents: {
        Args: { p_club_id: number; p_collaborator_id: number }
        Returns: {
          club_id: number
          collaborator_id: number
          document_type: string
          file_url: string
          id: number
          rejection_reason: string | null
          status: string | null
          uploaded_at: string | null
          verified_at: string | null
          verified_by: string | null
        }[]
      }
      get_current_club_id: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      get_pending_collaborator_documents: {
        Args: { p_club_id: number }
        Returns: {
          id: number
          club_id: number
          collaborator_id: number
          document_type: string
          file_url: string
          status: string
          uploaded_at: string
          verified_at: string
          verified_by: string
          rejection_reason: string
          collaborator_name: string
        }[]
      }
      has_column: {
        Args: { p_table_name: string; p_column_name: string }
        Returns: boolean
      }
      insert_collaborator_document: {
        Args: {
          p_club_id: number
          p_collaborator_id: number
          p_document_type: string
          p_file_url: string
          p_status?: string
        }
        Returns: undefined
      }
      log_audit_event: {
        Args: {
          p_club_id: number
          p_user_id: string
          p_action: string
          p_details: Json
          p_success: boolean
        }
        Returns: number
      }
      process_inventory_request: {
        Args: { p_request_id: number; p_user_id: string }
        Returns: boolean
      }
      register_player_document: {
        Args: {
          p_club_id: number
          p_player_id: string
          p_document_type: string
          p_file_url: string
        }
        Returns: Json
      }
      remove_collaborator_associations: {
        Args: { p_club_id: number; p_collaborator_id: number }
        Returns: undefined
      }
      remove_player_associations: {
        Args: { p_club_id: number; p_player_id: string }
        Returns: undefined
      }
      remove_player_completely: {
        Args: { p_club_id: number; p_player_id: string }
        Returns: undefined
      }
      return_inventory_request_item: {
        Args: {
          p_request_item_id: number
          p_return_quantity: number
          p_user_id: string
        }
        Returns: boolean
      }
      update_inventory_product_quantity: {
        Args: {
          p_club_id: number
          p_product_id: number
          p_quantity: number
          p_transaction_type: string
          p_user_id: string
          p_notes?: string
        }
        Returns: number
      }
      update_user_profile_image: {
        Args: { user_id: string; image_url: string }
        Returns: boolean
      }
      verify_collaborator_document: {
        Args: {
          p_document_id: number
          p_verified_by: string
          p_status: string
          p_rejection_reason?: string
        }
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
