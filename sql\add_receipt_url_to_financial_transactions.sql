-- =====================================================
-- SCRIPT SQL PARA ADICIONAR CAMPO receipt_url
-- Tabela: financial_transactions
-- =====================================================

-- Adicionar campo receipt_url à tabela financial_transactions
ALTER TABLE financial_transactions 
ADD COLUMN IF NOT EXISTS receipt_url TEXT;

-- Adicionar comentário explicativo
COMMENT ON COLUMN financial_transactions.receipt_url IS 'URL do comprovante de pagamento da transação';

-- Criar índice para melhor performance nas consultas por comprovante
CREATE INDEX IF NOT EXISTS idx_financial_transactions_receipt_url 
ON financial_transactions(receipt_url) 
WHERE receipt_url IS NOT NULL;

-- =====================================================
-- COMENTÁRIOS FINAIS
-- =====================================================

-- Este script adiciona o campo receipt_url à tabela financial_transactions
-- para permitir o armazenamento de URLs de comprovantes de pagamento.
-- 
-- Para executar:
-- 1. Conecte-se ao seu banco Supabase
-- 2. Execute este script no SQL Editor
-- 3. Verifique se o campo foi adicionado corretamente
-- 
-- Após executar este script, você precisará:
-- 1. Regenerar os tipos TypeScript do Supabase
-- 2. Atualizar o arquivo src/integrations/supabase/types.ts
