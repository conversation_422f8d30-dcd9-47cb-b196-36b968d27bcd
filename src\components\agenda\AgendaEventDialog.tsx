import { useState } from "react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Calendar } from "@/components/ui/calendar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CalendarIcon, Clock } from "lucide-react";
import { useAgendaStore } from "@/store/useAgendaStore";

interface AgendaEventDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
}

// Função utilitária para combinar data e hora em um timestamp ISO
function combineDateAndTime(date: Date, time: string): string {
  const [hours, minutes] = time.split(":");
  const copy = new Date(date);
  copy.setHours(Number(hours), Number(minutes), 0, 0);
  return copy.toISOString();
}

export function AgendaEventDialog({ open, onOpenChange, clubId }: AgendaEventDialogProps) {
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [startTime, setStartTime] = useState("09:00");
  const [endTime, setEndTime] = useState("10:30");
  const [title, setTitle] = useState("");
  const [location, setLocation] = useState("");
  const [description, setDescription] = useState("");
  const [participants, setParticipants] = useState("");
  const [eventType, setEventType] = useState("outro");
  const [error, setError] = useState({
    title: "",
    date: "",
    startTime: "",
    endTime: "",
  });
  const { addAgendaEvent, loading } = useAgendaStore();

  // Gerar opções de horário (de 30 em 30 minutos)
  const generateTimeOptions = () => {
    const options = [];
    for (let hour = 0; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const h = hour.toString().padStart(2, '0');
        const m = minute.toString().padStart(2, '0');
        options.push(`${h}:${m}`);
      }
    }
    return options;
  };

  const timeOptions = generateTimeOptions();

  const handleSave = async () => {
    if (!title.trim()) {
      setError({ ...error, title: "Título é obrigatório" });
      return;
    }
    if (!date) {
      setError({ ...error, date: "Data é obrigatória" });
      return;
    }
    if (!startTime) {
      setError({ ...error, startTime: "Início é obrigatório" });
      return;
    }
    if (!endTime) {
      setError({ ...error, endTime: "Término é obrigatório" });
      return;
    }
    await addAgendaEvent(clubId, {
      title,
      date: combineDateAndTime(date, startTime),
      time: startTime, // string HH:mm
      endTime: combineDateAndTime(date, endTime),
      type: eventType,
      location,
      description,
      participants: participants.split(",").map(p => p.trim()).filter(Boolean),
      club_id: clubId,
    });
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Adicionar novo evento</DialogTitle>
          <DialogDescription>
            Preencha os dados para criar um novo evento na agenda
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 py-4">
          <div className="space-y-1.5">
            <Label htmlFor="title">Título do evento</Label>
            <Input id="title" value={title} onChange={(e) => setTitle(e.target.value)} placeholder="Título do evento" />
            {error.title && <div className="text-red-500">{error.title}</div>}
          </div>
          
          <div className="space-y-1.5">
            <Label htmlFor="event-type">Tipo de evento</Label>
            <Select defaultValue={eventType} onValueChange={setEventType}>
              <SelectTrigger id="event-type">
                <SelectValue placeholder="Selecione o tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectLabel>Tipos de evento</SelectLabel>
                  <SelectItem value="treino">Treino</SelectItem>
                  <SelectItem value="jogo">Jogo</SelectItem>
                  <SelectItem value="reuniao">Reunião</SelectItem>
                  <SelectItem value="medico">Médico</SelectItem>
                  <SelectItem value="viagem">Viagem</SelectItem>
                  <SelectItem value="outro">Outro</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-1.5">
            <Label>Data</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {date ? format(date, "dd/MM/yyyy", { locale: ptBR }) : <span>Selecione a data</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={setDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
            {error.date && <div className="text-red-500">{error.date}</div>}
          </div>
          
          <div className="grid grid-cols-2 gap-2">
            <div className="space-y-1.5">
              <Label htmlFor="start-time">Início</Label>
              <Select value={startTime} onValueChange={setStartTime}>
                <SelectTrigger id="start-time" className="flex items-center">
                  <Clock className="mr-2 h-4 w-4" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {timeOptions.map((time) => (
                    <SelectItem key={`start-${time}`} value={time}>
                      {time}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {error.startTime && <div className="text-red-500">{error.startTime}</div>}
            </div>
            
            <div className="space-y-1.5">
              <Label htmlFor="end-time">Término</Label>
              <Select value={endTime} onValueChange={setEndTime}>
                <SelectTrigger id="end-time" className="flex items-center">
                  <Clock className="mr-2 h-4 w-4" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {timeOptions.map((time) => (
                    <SelectItem key={`end-${time}`} value={time}>
                      {time}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {error.endTime && <div className="text-red-500">{error.endTime}</div>}
            </div>
          </div>
          
          <div className="space-y-1.5">
            <Label htmlFor="location">Local</Label>
            <Input id="location" value={location} onChange={(e) => setLocation(e.target.value)} placeholder="Local do evento" />
          </div>
          
          <div className="space-y-1.5">
            <Label htmlFor="participants">Participantes</Label>
            <Input id="participants" value={participants} onChange={(e) => setParticipants(e.target.value)} placeholder="Participantes (separados por vírgula)" />
          </div>
          
          <div className="space-y-1.5 sm:col-span-2">
            <Label htmlFor="description">Descrição</Label>
            <Textarea 
              id="description" 
              value={description} 
              onChange={(e) => setDescription(e.target.value)} 
              placeholder="Descreva os detalhes do evento" 
              className="min-h-24 resize-none" 
            />
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button type="submit" onClick={handleSave} disabled={loading}>
            Salvar evento
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
