-- Criar função RPC para inserir documentos de colaboradores
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION insert_collaborator_document(
  p_club_id INTEGER,
  p_collaborator_id INTEGER,
  p_document_type TEXT,
  p_file_url TEXT,
  p_status TEXT DEFAULT 'pending'
) RETURNS VOID AS $$
BEGIN
  INSERT INTO collaborator_documents (
    club_id,
    collaborator_id,
    document_type,
    file_url,
    status,
    uploaded_at
  ) VALUES (
    p_club_id,
    p_collaborator_id,
    p_document_type,
    p_file_url,
    p_status,
    NOW()
  );
END;
$$ LANGUAGE plpgsql;
