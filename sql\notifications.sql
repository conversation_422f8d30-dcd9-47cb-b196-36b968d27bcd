-- C<PERSON>r tabela de notificações
CREATE TABLE IF NOT EXISTS notifications (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  user_id UUID REFERENCES users(id) NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type TEXT NOT NULL, -- 'rehab_session', 'medical_record', 'system', etc.
  reference_id TEXT, -- ID da sessão, prontuário, etc.
  reference_type TEXT, -- 'rehab_session', 'medical_record', etc.
  read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  scheduled_for TIMESTAMP WITH TIME ZONE, -- Data para a qual a notificação está programada
  sent BOOLEAN DEFAULT FALSE, -- Indica se a notificação foi enviada
  sent_at TIMESTAMP WITH TIME ZONE -- Data em que a notificação foi enviada
);

-- Criar índices para melhorar a performance
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_club_id ON notifications(club_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(read);
CREATE INDEX IF NOT EXISTS idx_notifications_scheduled_for ON notifications(scheduled_for);
CREATE INDEX IF NOT EXISTS idx_notifications_sent ON notifications(sent);

-- Ativar Row Level Security (RLS)
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Política para isolamento por clube
CREATE POLICY notifications_club_isolation ON notifications
    USING (club_id IN (
        SELECT club_id FROM club_members WHERE user_id = auth.uid()
    ));

-- Política para permitir que usuários vejam apenas suas próprias notificações
CREATE POLICY notifications_user_view ON notifications
    FOR SELECT
    USING (
        user_id = auth.uid() OR
        auth.uid() IN (
            SELECT user_id FROM club_members 
            WHERE club_id = notifications.club_id 
            AND role IN ('admin', 'technical')
        )
    );

-- Política para permitir que apenas admins e técnicos criem notificações
CREATE POLICY notifications_insert ON notifications
    FOR INSERT
    WITH CHECK (
        auth.uid() IN (
            SELECT user_id FROM club_members 
            WHERE club_id = notifications.club_id 
            AND role IN ('admin', 'technical')
        )
    );

-- Política para permitir que usuários atualizem apenas suas próprias notificações
CREATE POLICY notifications_update ON notifications
    FOR UPDATE
    USING (
        user_id = auth.uid() OR
        auth.uid() IN (
            SELECT user_id FROM club_members 
            WHERE club_id = notifications.club_id 
            AND role IN ('admin', 'technical')
        )
    );

-- Política para permitir que apenas admins e técnicos excluam notificações
CREATE POLICY notifications_delete ON notifications
    FOR DELETE
    USING (
        auth.uid() IN (
            SELECT user_id FROM club_members 
            WHERE club_id = notifications.club_id 
            AND role IN ('admin', 'technical')
        )
    );

-- Função para criar notificações de agendamento de reabilitação
CREATE OR REPLACE FUNCTION create_rehab_session_notifications()
RETURNS TRIGGER AS $$
DECLARE
    medical_professional_id UUID;
    session_date DATE;
    session_time TIME;
    player_name TEXT;
    notification_title TEXT;
    notification_message TEXT;
BEGIN
    -- Obter o ID do profissional médico associado à sessão
    SELECT mp.user_id INTO medical_professional_id
    FROM medical_professionals mp
    WHERE mp.name = NEW.professional
    AND mp.club_id = NEW.club_id;
    
    -- Se não encontrar um profissional médico, sair da função
    IF medical_professional_id IS NULL THEN
        RETURN NEW;
    END IF;
    
    -- Obter o nome do jogador
    SELECT p.name INTO player_name
    FROM players p
    WHERE p.id = NEW.player_id
    AND p.club_id = NEW.club_id;
    
    -- Extrair data e hora da sessão
    session_date := NEW.date::DATE;
    session_time := NEW.time::TIME;
    
    -- Criar notificação para 3 dias antes
    notification_title := 'Agendamento em 3 dias';
    notification_message := 'Você tem uma sessão de ' || NEW.activity || ' com ' || player_name || ' em 3 dias (' || NEW.date || ' às ' || NEW.time || ').';
    
    INSERT INTO notifications (
        club_id, 
        user_id, 
        title, 
        message, 
        type, 
        reference_id, 
        reference_type, 
        scheduled_for
    ) VALUES (
        NEW.club_id,
        medical_professional_id,
        notification_title,
        notification_message,
        'rehab_session',
        NEW.id::TEXT,
        'rehab_session',
        (session_date - INTERVAL '3 days')::TIMESTAMP + session_time::TIME
    );
    
    -- Criar notificação para 1 dia antes
    notification_title := 'Agendamento amanhã';
    notification_message := 'Você tem uma sessão de ' || NEW.activity || ' com ' || player_name || ' amanhã (' || NEW.date || ' às ' || NEW.time || ').';
    
    INSERT INTO notifications (
        club_id, 
        user_id, 
        title, 
        message, 
        type, 
        reference_id, 
        reference_type, 
        scheduled_for
    ) VALUES (
        NEW.club_id,
        medical_professional_id,
        notification_title,
        notification_message,
        'rehab_session',
        NEW.id::TEXT,
        'rehab_session',
        (session_date - INTERVAL '1 day')::TIMESTAMP + session_time::TIME
    );
    
    -- Criar notificação para o dia do agendamento
    notification_title := 'Agendamento hoje';
    notification_message := 'Você tem uma sessão de ' || NEW.activity || ' com ' || player_name || ' hoje às ' || NEW.time || '.';
    
    INSERT INTO notifications (
        club_id, 
        user_id, 
        title, 
        message, 
        type, 
        reference_id, 
        reference_type, 
        scheduled_for
    ) VALUES (
        NEW.club_id,
        medical_professional_id,
        notification_title,
        notification_message,
        'rehab_session',
        NEW.id::TEXT,
        'rehab_session',
        session_date::TIMESTAMP + session_time::TIME - INTERVAL '2 hours'
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Criar trigger para gerar notificações quando uma nova sessão de reabilitação for criada
CREATE TRIGGER rehab_session_notifications_trigger
AFTER INSERT ON rehab_sessions
FOR EACH ROW
EXECUTE FUNCTION create_rehab_session_notifications();
