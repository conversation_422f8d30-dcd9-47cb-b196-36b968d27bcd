/**
 * Constantes de permissões do sistema
 *
 * Este arquivo define todas as permissões disponíveis no sistema,
 * organizadas por grupos para facilitar a manutenção e consistência.
 */

// Permissões relacionadas a jogadores
export const PLAYER_PERMISSIONS = {
  VIEW: "players.view",
  VIEW_OWN: "players.view_own", // Permissão para jogador ver seu próprio perfil
  EDIT_OWN: "players.edit_own", // Permissão para jogador editar seu próprio perfil
  CREATE: "players.create",
  EDIT: "players.edit",
  DELETE: "players.delete",
  DOCUMENTS: {
    VIEW: "players.documents.view",
    VERIFY: "players.documents.verify",
  },
  FINANCES: {
    VIEW: "players.finances.view",
    EDIT: "players.finances.edit",
  },
  EVALUATION: {
    VIEW: "players.evaluation.view",
    EDIT: "players.evaluation.edit",
  },
};

// Permissões relacionadas a partidas
export const MATCH_PERMISSIONS = {
  VIEW: "matches.view",
  CREATE: "matches.create",
  EDIT: "matches.edit",
  DELETE: "matches.delete",
  LINEUP: "matches.lineup",
  EVENTS: "matches.events",
};

// Permissões relacionadas a treinamentos
export const TRAINING_PERMISSIONS = {
  VIEW: "trainings.view",
  CREATE: "trainings.create",
  EDIT: "trainings.edit",
  DELETE: "trainings.delete",
};

// Permissões relacionadas a departamentos
export const DEPARTMENT_PERMISSIONS = {
  VIEW: "departments.view",
  CREATE: "departments.create",
  EDIT: "departments.edit",
  DELETE: "departments.delete",
};

// Permissões relacionadas a usuários
export const USER_PERMISSIONS = {
  VIEW: "users.view",
  CREATE: "users.create",
  EDIT: "users.edit",
  DELETE: "users.delete",
  PERMISSIONS: "users.permissions",
};

// Permissões relacionadas a configurações
export const SETTINGS_PERMISSIONS = {
  VIEW: "settings.view",
  EDIT: "settings.edit",
};

// Permissões relacionadas a finanças
export const FINANCE_PERMISSIONS = {
  VIEW: "finances.view",
  CREATE: "finances.create",
  EDIT: "finances.edit",
  DELETE: "finances.delete",
};

// Permissões relacionadas a área médica
export const MEDICAL_PERMISSIONS = {
  VIEW: "medical.view",
  CREATE: "medical.create",
  EDIT: "medical.edit",
  DELETE: "medical.delete",
};

// Permissões relacionadas a profissionais médicos
export const MEDICAL_PROFESSIONAL_PERMISSIONS = {
  VIEW: "medical_professionals.view",
  CREATE: "medical_professionals.create",
  EDIT: "medical_professionals.edit",
  DELETE: "medical_professionals.delete",
  EDIT_OWN: "medical_professionals.edit_own",
};

// Permissões relacionadas a agenda
export const AGENDA_PERMISSIONS = {
  VIEW: "agenda.view",
  CREATE: "agenda.create",
  EDIT: "agenda.edit",
  DELETE: "agenda.delete",
};

// Permissões relacionadas a categorias
export const CATEGORY_PERMISSIONS = {
  VIEW: "categories.view",
  CREATE: "categories.create",
  EDIT: "categories.edit",
  DELETE: "categories.delete",
};

// Permissões relacionadas a alojamentos
export const ACCOMMODATION_PERMISSIONS = {
  VIEW: "accommodations.view",
  CREATE: "accommodations.create",
  EDIT: "accommodations.edit",
  DELETE: "accommodations.delete",
};

// Permissões relacionadas a convocações
export const CALLUP_PERMISSIONS = {
  VIEW: "callups.view",
  CREATE: "callups.create",
  EDIT: "callups.edit",
  DELETE: "callups.delete",
};

// Permissões relacionadas a relatórios
export const REPORT_PERMISSIONS = {
  VIEW: "reports.view",
  GENERATE: "reports.generate",
};

// Permissões relacionadas a estatísticas
export const STATISTICS_PERMISSIONS = {
  VIEW: "statistics.view",
};

// Permissões relacionadas a analytics
export const ANALYTICS_PERMISSIONS = {
  VIEW: "analytics.view",
};

// Permissões relacionadas a comunicação
export const COMMUNICATION_PERMISSIONS = {
  VIEW: "communication.view",
  SEND: "communication.send",
};

// Permissões relacionadas a logs de auditoria
export const AUDIT_PERMISSIONS = {
  VIEW: "audit_logs.view",
  EXPORT: "audit_logs.export",
};

// Permissões relacionadas a presidente (especiais)
export const PRESIDENT_PERMISSIONS = {
  CLUB_OWNERSHIP: "president.club_ownership",
  FINANCIAL_APPROVAL: "president.financial_approval",
};

// Permissões relacionadas ao dashboard
export const DASHBOARD_PERMISSIONS = {
  VIEW: "dashboard.view",
};

// Permissões relacionadas ao estoque
export const INVENTORY_PERMISSIONS = {
  VIEW: "inventory.view",
  CREATE: "inventory.create",
  EDIT: "inventory.edit",
  DELETE: "inventory.delete",
  REPORTS: "inventory.reports",
  REQUESTS: {
    VIEW: "inventory.requests.view",
    CREATE: "inventory.requests.create",
    EDIT: "inventory.requests.edit",
    DELETE: "inventory.requests.delete",
    APPROVE: "inventory.requests.approve",
    PROCESS: "inventory.requests.process"
  }
};

// Todos os grupos de permissões organizados para UI
export const PERMISSION_GROUPS = {
  dashboard: {
    label: "Dashboard",
    permissions: {
      [DASHBOARD_PERMISSIONS.VIEW]: "Visualizar dashboard",
    },
  },
  playerProfile: {
    label: "Perfil do Jogador",
    permissions: {
      [PLAYER_PERMISSIONS.VIEW_OWN]: "Visualizar próprio perfil",
      [PLAYER_PERMISSIONS.EDIT_OWN]: "Editar próprio perfil",
      [PLAYER_PERMISSIONS.EVALUATION.VIEW]: "Visualizar próprio pré cadastro",
    },
  },
  players: {
    label: "Jogadores",
    permissions: {
      [PLAYER_PERMISSIONS.VIEW]: "Visualizar jogadores",
      [PLAYER_PERMISSIONS.CREATE]: "Criar jogadores",
      [PLAYER_PERMISSIONS.EDIT]: "Editar jogadores",
      [PLAYER_PERMISSIONS.DELETE]: "Excluir jogadores",
      [PLAYER_PERMISSIONS.DOCUMENTS.VIEW]: "Visualizar documentos",
      [PLAYER_PERMISSIONS.DOCUMENTS.VERIFY]: "Verificar documentos",
      [PLAYER_PERMISSIONS.FINANCES.VIEW]: "Visualizar finanças",
      [PLAYER_PERMISSIONS.FINANCES.EDIT]: "Editar finanças",
      [PLAYER_PERMISSIONS.EVALUATION.VIEW]: "Visualizar avaliações",
      [PLAYER_PERMISSIONS.EVALUATION.EDIT]: "Editar avaliações",
    },
  },
  matches: {
    label: "Partidas",
    permissions: {
      [MATCH_PERMISSIONS.VIEW]: "Visualizar partidas",
      [MATCH_PERMISSIONS.CREATE]: "Criar partidas",
      [MATCH_PERMISSIONS.EDIT]: "Editar partidas",
      [MATCH_PERMISSIONS.DELETE]: "Excluir partidas",
      [MATCH_PERMISSIONS.LINEUP]: "Gerenciar escalação",
      [MATCH_PERMISSIONS.EVENTS]: "Registrar eventos",
    },
  },
  trainings: {
    label: "Treinamentos",
    permissions: {
      [TRAINING_PERMISSIONS.VIEW]: "Visualizar treinamentos",
      [TRAINING_PERMISSIONS.CREATE]: "Criar treinamentos",
      [TRAINING_PERMISSIONS.EDIT]: "Editar treinamentos",
      [TRAINING_PERMISSIONS.DELETE]: "Excluir treinamentos",
    },
  },
  departments: {
    label: "Departamentos",
    permissions: {
      [DEPARTMENT_PERMISSIONS.VIEW]: "Visualizar departamentos",
      [DEPARTMENT_PERMISSIONS.CREATE]: "Criar departamentos",
      [DEPARTMENT_PERMISSIONS.EDIT]: "Editar departamentos",
      [DEPARTMENT_PERMISSIONS.DELETE]: "Excluir departamentos",
    },
  },
  users: {
    label: "Usuários",
    permissions: {
      [USER_PERMISSIONS.VIEW]: "Visualizar usuários",
      [USER_PERMISSIONS.CREATE]: "Criar usuários",
      [USER_PERMISSIONS.EDIT]: "Editar usuários",
      [USER_PERMISSIONS.DELETE]: "Excluir usuários",
      [USER_PERMISSIONS.PERMISSIONS]: "Gerenciar permissões",
    },
  },
  finances: {
    label: "Finanças",
    permissions: {
      [FINANCE_PERMISSIONS.VIEW]: "Visualizar finanças",
      [FINANCE_PERMISSIONS.CREATE]: "Criar transações",
      [FINANCE_PERMISSIONS.EDIT]: "Editar transações",
      [FINANCE_PERMISSIONS.DELETE]: "Excluir transações",
    },
  },
  medical: {
    label: "Departamento Médico",
    permissions: {
      [MEDICAL_PERMISSIONS.VIEW]: "Visualizar registros médicos",
      [MEDICAL_PERMISSIONS.CREATE]: "Criar registros médicos",
      [MEDICAL_PERMISSIONS.EDIT]: "Editar registros médicos",
      [MEDICAL_PERMISSIONS.DELETE]: "Excluir registros médicos",
    },
  },
  medical_professionals: {
    label: "Profissionais Médicos",
    permissions: {
      [MEDICAL_PROFESSIONAL_PERMISSIONS.VIEW]: "Visualizar profissionais médicos",
      [MEDICAL_PROFESSIONAL_PERMISSIONS.CREATE]: "Cadastrar profissionais médicos",
      [MEDICAL_PROFESSIONAL_PERMISSIONS.EDIT]: "Editar profissionais médicos",
      [MEDICAL_PROFESSIONAL_PERMISSIONS.DELETE]: "Excluir profissionais médicos",
      [MEDICAL_PROFESSIONAL_PERMISSIONS.EDIT_OWN]: "Editar próprio perfil médico",
    },
  },
  agenda: {
    label: "Agenda",
    permissions: {
      [AGENDA_PERMISSIONS.VIEW]: "Visualizar agenda",
      [AGENDA_PERMISSIONS.CREATE]: "Criar eventos",
      [AGENDA_PERMISSIONS.EDIT]: "Editar eventos",
      [AGENDA_PERMISSIONS.DELETE]: "Excluir eventos",
    },
  },
  categories: {
    label: "Categorias",
    permissions: {
      [CATEGORY_PERMISSIONS.VIEW]: "Visualizar categorias",
      [CATEGORY_PERMISSIONS.CREATE]: "Criar categorias",
      [CATEGORY_PERMISSIONS.EDIT]: "Editar categorias",
      [CATEGORY_PERMISSIONS.DELETE]: "Excluir categorias",
    },
  },
  accommodations: {
    label: "Alojamentos",
    permissions: {
      [ACCOMMODATION_PERMISSIONS.VIEW]: "Visualizar alojamentos",
      [ACCOMMODATION_PERMISSIONS.CREATE]: "Criar alojamentos",
      [ACCOMMODATION_PERMISSIONS.EDIT]: "Editar alojamentos",
      [ACCOMMODATION_PERMISSIONS.DELETE]: "Excluir alojamentos",
    },
  },
  callups: {
    label: "Convocações",
    permissions: {
      [CALLUP_PERMISSIONS.VIEW]: "Visualizar convocações",
      [CALLUP_PERMISSIONS.CREATE]: "Criar convocações",
      [CALLUP_PERMISSIONS.EDIT]: "Editar convocações",
      [CALLUP_PERMISSIONS.DELETE]: "Excluir convocações",
    },
  },
  reports: {
    label: "Relatórios",
    permissions: {
      [REPORT_PERMISSIONS.VIEW]: "Visualizar relatórios",
      [REPORT_PERMISSIONS.GENERATE]: "Gerar relatórios",
    },
  },
  statistics: {
    label: "Estatísticas",
    permissions: {
      [STATISTICS_PERMISSIONS.VIEW]: "Visualizar estatísticas",
    },
  },
  analytics: {
    label: "Analytics",
    permissions: {
      [ANALYTICS_PERMISSIONS.VIEW]: "Visualizar analytics",
    },
  },
  communication: {
    label: "Comunicação",
    permissions: {
      [COMMUNICATION_PERMISSIONS.VIEW]: "Visualizar comunicações",
      [COMMUNICATION_PERMISSIONS.SEND]: "Enviar comunicações",
    },
  },
  settings: {
    label: "Configurações",
    permissions: {
      [SETTINGS_PERMISSIONS.VIEW]: "Visualizar configurações",
      [SETTINGS_PERMISSIONS.EDIT]: "Editar configurações",
    },
  },
  audit: {
    label: "Logs de Auditoria",
    permissions: {
      [AUDIT_PERMISSIONS.VIEW]: "Visualizar logs de auditoria",
      [AUDIT_PERMISSIONS.EXPORT]: "Exportar logs de auditoria",
    },
  },
  inventory: {
    label: "Estoque",
    permissions: {
      [INVENTORY_PERMISSIONS.VIEW]: "Visualizar estoque",
      [INVENTORY_PERMISSIONS.CREATE]: "Adicionar produtos",
      [INVENTORY_PERMISSIONS.EDIT]: "Editar produtos",
      [INVENTORY_PERMISSIONS.DELETE]: "Excluir produtos",
      [INVENTORY_PERMISSIONS.REPORTS]: "Gerar relatórios de estoque",
      [INVENTORY_PERMISSIONS.REQUESTS.VIEW]: "Visualizar solicitações de estoque",
      [INVENTORY_PERMISSIONS.REQUESTS.CREATE]: "Criar solicitações de estoque",
      [INVENTORY_PERMISSIONS.REQUESTS.EDIT]: "Editar solicitações de estoque",
      [INVENTORY_PERMISSIONS.REQUESTS.DELETE]: "Excluir solicitações de estoque",
      [INVENTORY_PERMISSIONS.REQUESTS.APPROVE]: "Aprovar solicitações de estoque",
      [INVENTORY_PERMISSIONS.REQUESTS.PROCESS]: "Processar solicitações de estoque",
    },
  },
  president: {
    label: "Presidente",
    permissions: {
      [PRESIDENT_PERMISSIONS.CLUB_OWNERSHIP]: "Propriedade do clube",
      [PRESIDENT_PERMISSIONS.FINANCIAL_APPROVAL]: "Aprovação financeira",
    },
  },
};

// Definição de papéis predefinidos
export const ROLES = {
  president: {
    label: "Presidente",
    description: "Acesso completo a todas as funcionalidades, incluindo propriedade do clube",
  },
  admin: {
    label: "Administrador",
    description: "Acesso completo a todas as funcionalidades, exceto propriedade do clube",
  },
  manager: {
    label: "Gerente",
    description: "Acesso a gerenciamento de jogadores e partidas",
  },
  coach: {
    label: "Treinador",
    description: "Acesso a jogadores, partidas e treinamentos",
  },
  medical: {
    label: "Médico",
    description: "Acesso ao departamento médico e jogadores",
  },
  staff: {
    label: "Funcionário",
    description: "Acesso limitado a visualização",
  },
  player: {
    label: "Jogador",
    description: "Acesso apenas ao próprio perfil",
  },
};

// Permissões predefinidas para cada papel
export const ROLE_PERMISSIONS = {
  // Presidente tem todas as permissões
  president: Object.values(PERMISSION_GROUPS).reduce((acc, group) => {
    Object.keys(group.permissions).forEach((perm) => {
      acc[perm] = true;
    });
    return acc;
  }, {} as Record<string, boolean>),

  // Admin tem todas as permissões exceto as de presidente
  admin: Object.values(PERMISSION_GROUPS)
    .filter(group => group.label !== "Presidente")
    .reduce((acc, group) => {
      Object.keys(group.permissions).forEach((perm) => {
        acc[perm] = true;
      });
      return acc;
    }, {} as Record<string, boolean>),

  // Gerente tem acesso amplo mas não completo
  manager: {
    [PLAYER_PERMISSIONS.VIEW]: true,
    [PLAYER_PERMISSIONS.CREATE]: true,
    [PLAYER_PERMISSIONS.EDIT]: true,
    [PLAYER_PERMISSIONS.DOCUMENTS.VIEW]: true,
    [PLAYER_PERMISSIONS.DOCUMENTS.VERIFY]: true,
    [PLAYER_PERMISSIONS.FINANCES.VIEW]: true,
    [MATCH_PERMISSIONS.VIEW]: true,
    [MATCH_PERMISSIONS.CREATE]: true,
    [MATCH_PERMISSIONS.EDIT]: true,
    [MATCH_PERMISSIONS.LINEUP]: true,
    [MATCH_PERMISSIONS.EVENTS]: true,
    [TRAINING_PERMISSIONS.VIEW]: true,
    [TRAINING_PERMISSIONS.CREATE]: true,
    [TRAINING_PERMISSIONS.EDIT]: true,
    [DEPARTMENT_PERMISSIONS.VIEW]: true,
    [USER_PERMISSIONS.VIEW]: true,
    [SETTINGS_PERMISSIONS.VIEW]: true,
    [FINANCE_PERMISSIONS.VIEW]: true,
    [MEDICAL_PERMISSIONS.VIEW]: true,
    [AGENDA_PERMISSIONS.VIEW]: true,
    [AGENDA_PERMISSIONS.CREATE]: true,
    [AGENDA_PERMISSIONS.EDIT]: true,
    [CATEGORY_PERMISSIONS.VIEW]: true,
    [ACCOMMODATION_PERMISSIONS.VIEW]: true,
    [CALLUP_PERMISSIONS.VIEW]: true,
    [CALLUP_PERMISSIONS.CREATE]: true,
    [CALLUP_PERMISSIONS.EDIT]: true,
    [REPORT_PERMISSIONS.VIEW]: true,
    [REPORT_PERMISSIONS.GENERATE]: true,
    [STATISTICS_PERMISSIONS.VIEW]: true,
    [AUDIT_PERMISSIONS.VIEW]: true,
    [INVENTORY_PERMISSIONS.VIEW]: true,
    [INVENTORY_PERMISSIONS.CREATE]: true,
    [INVENTORY_PERMISSIONS.EDIT]: true,
    [INVENTORY_PERMISSIONS.REPORTS]: true,
    [INVENTORY_PERMISSIONS.REQUESTS.VIEW]: true,
    [INVENTORY_PERMISSIONS.REQUESTS.CREATE]: true,
    [INVENTORY_PERMISSIONS.REQUESTS.EDIT]: true,
    [INVENTORY_PERMISSIONS.REQUESTS.DELETE]: true,
    [INVENTORY_PERMISSIONS.REQUESTS.APPROVE]: true,
    [INVENTORY_PERMISSIONS.REQUESTS.PROCESS]: true,
  },

  // Treinador tem acesso a jogadores e partidas
  coach: {
    [PLAYER_PERMISSIONS.VIEW]: true,
    [PLAYER_PERMISSIONS.EDIT]: true,
    [MATCH_PERMISSIONS.VIEW]: true,
    [MATCH_PERMISSIONS.LINEUP]: true,
    [MATCH_PERMISSIONS.EVENTS]: true,
    [TRAINING_PERMISSIONS.VIEW]: true,
    [TRAINING_PERMISSIONS.CREATE]: true,
    [TRAINING_PERMISSIONS.EDIT]: true,
    [AGENDA_PERMISSIONS.VIEW]: true,
    [CATEGORY_PERMISSIONS.VIEW]: true,
    [CALLUP_PERMISSIONS.VIEW]: true,
    [CALLUP_PERMISSIONS.CREATE]: true,
    [CALLUP_PERMISSIONS.EDIT]: true,
    [STATISTICS_PERMISSIONS.VIEW]: true,
  },

  // Médico tem acesso a registros médicos
  medical: {
    [PLAYER_PERMISSIONS.VIEW]: true,
    [MEDICAL_PERMISSIONS.VIEW]: true,
    [MEDICAL_PERMISSIONS.CREATE]: true,
    [MEDICAL_PERMISSIONS.EDIT]: true,
    [MEDICAL_PROFESSIONAL_PERMISSIONS.VIEW]: true,
    [MEDICAL_PROFESSIONAL_PERMISSIONS.EDIT_OWN]: true,
    [AGENDA_PERMISSIONS.VIEW]: true,
    [AGENDA_PERMISSIONS.CREATE]: true,
    [AGENDA_PERMISSIONS.EDIT]: true,
    // Adicionar permissões específicas de agendamentos médicos
    "medical.appointments.view": true,
    "medical.appointments.create": true,
    "medical.appointments.edit": true,
    "medical.appointments.delete": true,
  },

  // Funcionário tem acesso limitado
  staff: {
    [PLAYER_PERMISSIONS.VIEW]: true,
    [MATCH_PERMISSIONS.VIEW]: true,
    [AGENDA_PERMISSIONS.VIEW]: true,
  },

  // Jogador tem acesso apenas ao próprio perfil
  player: {
    // Permissões mínimas para jogadores
    [DASHBOARD_PERMISSIONS.VIEW]: true,
    [PLAYER_PERMISSIONS.VIEW_OWN]: true,
    [PLAYER_PERMISSIONS.EDIT_OWN]: true,
    [PLAYER_PERMISSIONS.EVALUATION.VIEW]: true
  },
};
