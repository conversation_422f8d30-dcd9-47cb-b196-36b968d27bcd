-- Create player_evaluation_invitations table to store evaluation registration links
CREATE TABLE IF NOT EXISTS player_evaluation_invitations (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  token TEXT NOT NULL UNIQUE,
  email TEXT NOT NULL,
  cpf TEXT,
  status TEXT DEFAULT 'pending', -- 'pending', 'used', 'expired'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE,
  created_by UUID REFERENCES auth.users(id),
  used_at TIMESTAMP WITH TIME ZONE,
  player_id UUID REFERENCES players(id),
  
  -- Additional fields for evaluation scheduling
  evaluation_date TIMESTAMP WITH TIME ZONE,
  evaluation_location TEXT,
  evaluation_requirements TEXT,
  evaluation_notes TEXT
);

-- Add indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_player_evaluation_invitations_token ON player_evaluation_invitations(token);
CREATE INDEX IF NOT EXISTS idx_player_evaluation_invitations_email ON player_evaluation_invitations(email);
CREATE INDEX IF NOT EXISTS idx_player_evaluation_invitations_status ON player_evaluation_invitations(status);
CREATE INDEX IF NOT EXISTS idx_player_evaluation_invitations_club_id ON player_evaluation_invitations(club_id);

-- Add RLS policies
ALTER TABLE player_evaluation_invitations ENABLE ROW LEVEL SECURITY;

-- Policy for club members to view their own invitations
CREATE POLICY "Club members can view their own invitations"
  ON player_evaluation_invitations
  FOR SELECT
  USING (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

-- Policy for club members to insert invitations
CREATE POLICY "Club members can insert invitations"
  ON player_evaluation_invitations
  FOR INSERT
  WITH CHECK (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

-- Policy for club members to update their own invitations
CREATE POLICY "Club members can update their own invitations"
  ON player_evaluation_invitations
  FOR UPDATE
  USING (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

-- Add comment to explain table purpose
COMMENT ON TABLE player_evaluation_invitations IS 'Stores invitations for player evaluation registration';
