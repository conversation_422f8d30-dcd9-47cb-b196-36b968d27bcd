import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, DialogTitle } from "../ui/dialog";
import { Input } from "../ui/input";
import { Button } from "../ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { createContract, updateContract, deleteContract } from "../../api/api";

interface ContractDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  contract?: any;
  clubId: number;
  onSave?: () => void;
}

export function ContractDialog({ open, onOpenChange, contract, clubId, onSave }: ContractDialogProps) {
  const [title, setTitle] = useState(contract?.title || "");
  const [type, setType] = useState(contract?.type || "patrocinio");
  const [value, setValue] = useState(contract?.value?.toString() || "");
  const [startDate, setStartDate] = useState(contract?.start_date || "");
  const [endDate, setEndDate] = useState(contract?.end_date || "");
  const [status, setStatus] = useState(contract?.status || "Ativo");
  const [details, setDetails] = useState(contract?.details || "");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    if (contract) {
      setTitle(contract.title || "");
      setType(contract.type || "patrocinio");
      setValue(contract.value?.toString() || "");
      setStartDate(contract.start_date || "");
      setEndDate(contract.end_date || "");
      setStatus(contract.status || "Ativo");
      setDetails(contract.details || "");
    } else {
      setTitle("");
      setType("patrocinio");
      setValue("");
      setStartDate("");
      setEndDate("");
      setStatus("Ativo");
      setDetails("");
    }
  }, [contract, open]);

  const handleSave = async () => {
    setIsLoading(true);
    setError("");
    try {
      if (contract?.id) {
        await updateContract(clubId, contract.id, {
          title,
          type,
          value: parseFloat(value),
          start_date: startDate,
          end_date: endDate,
          status,
          details,
        });
      } else {
        await createContract(clubId, {
          title,
          type,
          value: parseFloat(value),
          start_date: startDate,
          end_date: endDate,
          status,
          details,
        });
      }
      onSave?.();
      onOpenChange(false);
    } catch (e: any) {
      setError("Erro ao salvar contrato");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!contract?.id) return;
    setIsLoading(true);
    setError("");
    try {
      await deleteContract(clubId, contract.id);
      onSave?.();
      onOpenChange(false);
    } catch (e: any) {
      setError("Erro ao excluir contrato");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{contract ? "Editar Contrato" : "Novo Contrato"}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <Input
            placeholder="Título"
            value={title}
            onChange={e => setTitle(e.target.value)}
            disabled={isLoading}
          />
          <Select value={type} onValueChange={setType} disabled={isLoading}>
            <SelectTrigger>
              <SelectValue placeholder="Tipo de Contrato" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="patrocinio">Patrocínio</SelectItem>
              <SelectItem value="parceria">Parceria</SelectItem>
              <SelectItem value="fornecedor">Fornecedor</SelectItem>
              <SelectItem value="outro">Outro</SelectItem>
            </SelectContent>
          </Select>
          <Input
            placeholder="Valor"
            type="number"
            value={value}
            onChange={e => setValue(e.target.value)}
            disabled={isLoading}
          />
          <Input
            placeholder="Data de Início"
            type="date"
            value={startDate}
            onChange={e => setStartDate(e.target.value)}
            disabled={isLoading}
          />
          <Input
            placeholder="Data de Término"
            type="date"
            value={endDate}
            onChange={e => setEndDate(e.target.value)}
            disabled={isLoading}
          />
          <Input
            placeholder="Status"
            value={status}
            onChange={e => setStatus(e.target.value)}
            disabled={isLoading}
          />
          <Input
            placeholder="Detalhes"
            value={details}
            onChange={e => setDetails(e.target.value)}
            disabled={isLoading}
          />
          {error && <div className="text-red-500 text-sm mt-1">{error}</div>}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isLoading}>Cancelar</Button>
          {contract && (
            <Button variant="destructive" onClick={handleDelete} disabled={isLoading}>Excluir</Button>
          )}
          <Button onClick={handleSave} disabled={isLoading || !title || !type || !value || !startDate || !endDate}>
            {isLoading ? <span className="loader mr-2" /> : null}
            Salvar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
