-- Create collaborators table
CREATE TABLE IF NOT EXISTS collaborators (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  user_id UUID REFERENCES auth.users(id),
  registration_number TEXT NOT NULL,
  full_name TEXT NOT NULL,
  birth_date DATE,
  phone TEXT,
  role TEXT NOT NULL,
  role_type TEXT NOT NULL, -- 'technical', 'assistant_technical'
  cpf TEXT,
  zip_code TEXT,
  state TEXT,
  city TEXT,
  address TEXT,
  address_number TEXT,
  credential_number TEXT,
  email TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_collaborators_club_id ON collaborators(club_id);
CREATE INDEX IF NOT EXISTS idx_collaborators_user_id ON collaborators(user_id);
CREATE INDEX IF NOT EXISTS idx_collaborators_registration_number ON collaborators(registration_number);

-- Create collaborator_documents table
CREATE TABLE IF NOT EXISTS collaborator_documents (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  collaborator_id INTEGER REFERENCES collaborators(id) NOT NULL,
  document_type TEXT NOT NULL,
  file_url TEXT NOT NULL,
  status TEXT DEFAULT 'pending',
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  verified_at TIMESTAMP WITH TIME ZONE,
  verified_by UUID REFERENCES auth.users(id),
  rejection_reason TEXT
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_collaborator_documents_club_id ON collaborator_documents(club_id);
CREATE INDEX IF NOT EXISTS idx_collaborator_documents_collaborator_id ON collaborator_documents(collaborator_id);

-- Create suppliers table
CREATE TABLE IF NOT EXISTS suppliers (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  company_name TEXT NOT NULL,
  address TEXT,
  address_number TEXT,
  zip_code TEXT,
  state TEXT,
  city TEXT,
  phone1 TEXT,
  phone2 TEXT,
  email TEXT,
  expiration_date DATE,
  bank_name TEXT,
  bank_agency TEXT,
  bank_account TEXT,
  bank_pix TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_suppliers_club_id ON suppliers(club_id);

-- Create views for collaborators and suppliers with user information
CREATE OR REPLACE VIEW collaborators_view AS
SELECT 
  c.*,
  u.name as user_name,
  u.email as user_email
FROM collaborators c
LEFT JOIN users u ON c.user_id = u.id;

-- Create a function to generate unique registration numbers for collaborators
CREATE OR REPLACE FUNCTION generate_collaborator_registration_number()
RETURNS TRIGGER AS $$
BEGIN
  -- Generate a unique registration number in the format COL-YYYYMMDD-XXXX
  NEW.registration_number := 'COL-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || 
                            LPAD(CAST(NEXTVAL('collaborators_id_seq') AS TEXT), 4, '0');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically generate registration numbers
CREATE TRIGGER set_collaborator_registration_number
BEFORE INSERT ON collaborators
FOR EACH ROW
WHEN (NEW.registration_number IS NULL)
EXECUTE FUNCTION generate_collaborator_registration_number();

-- Nota: A parte abaixo foi removida porque a tabela storage.policies não existe no seu banco de dados
-- Para configurar políticas de armazenamento, use o painel de controle do Supabase
-- INSERT INTO storage.policies (name, definition, bucket_id, operation)
-- VALUES (
--   'Authenticated Users can upload to collaborator documents',
--   '(bucket_id = ''playerdocuments''::text) AND (auth.role() = ''authenticated''::text)',
--   'playerdocuments',
--   'INSERT'
-- );
