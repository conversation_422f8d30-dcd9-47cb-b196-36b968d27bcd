-- =====================================================
-- SCRIPT SQL PARA SISTEMA DE ALIMENTAÇÃO
-- Game Day Nexus Platform
-- =====================================================

-- Tabela para tipos de refeição
CREATE TABLE meal_types (
    id SERIAL PRIMARY KEY,
    club_id INTEGER NOT NULL REFERENCES club_info(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    location VARCHAR(255),
    address TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela para sessões de alimentação
CREATE TABLE meal_sessions (
    id SERIAL PRIMARY KEY,
    club_id INTEGER NOT NULL REFERENCES club_info(id) ON DELETE CASCADE,
    meal_type_id INTEGER NOT NULL REFERENCES meal_types(id) ON DELETE CASCADE,
    accommodation_id INTEGER NOT NULL REFERENCES accommodations(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    time TIME,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela para participantes das refeições
CREATE TABLE meal_participants (
    id SERIAL PRIMARY KEY,
    club_id INTEGER NOT NULL REFERENCES club_info(id) ON DELETE CASCADE,
    meal_session_id INTEGER NOT NULL REFERENCES meal_sessions(id) ON DELETE CASCADE,
    participant_id VARCHAR(255) NOT NULL,
    participant_type VARCHAR(50) NOT NULL CHECK (participant_type IN ('player', 'collaborator')),
    signed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- ÍNDICES PARA PERFORMANCE
-- =====================================================

-- Índices para meal_types
CREATE INDEX idx_meal_types_club_id ON meal_types(club_id);
CREATE INDEX idx_meal_types_name ON meal_types(name);

-- Índices para meal_sessions
CREATE INDEX idx_meal_sessions_club_id ON meal_sessions(club_id);
CREATE INDEX idx_meal_sessions_meal_type_id ON meal_sessions(meal_type_id);
CREATE INDEX idx_meal_sessions_accommodation_id ON meal_sessions(accommodation_id);
CREATE INDEX idx_meal_sessions_date ON meal_sessions(date);

-- Índices para meal_participants
CREATE INDEX idx_meal_participants_club_id ON meal_participants(club_id);
CREATE INDEX idx_meal_participants_meal_session_id ON meal_participants(meal_session_id);
CREATE INDEX idx_meal_participants_participant_id ON meal_participants(participant_id);
CREATE INDEX idx_meal_participants_participant_type ON meal_participants(participant_type);

-- Índice composto para evitar duplicatas
CREATE UNIQUE INDEX idx_meal_participants_unique ON meal_participants(meal_session_id, participant_id, participant_type);

-- =====================================================
-- POLÍTICAS RLS (ROW LEVEL SECURITY)
-- =====================================================

-- Habilitar RLS nas tabelas
ALTER TABLE meal_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE meal_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE meal_participants ENABLE ROW LEVEL SECURITY;

-- Políticas para meal_types
CREATE POLICY "Users can view meal_types from their club" ON meal_types
    FOR SELECT USING (
        club_id IN (
            SELECT club_id FROM club_members WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert meal_types for their club" ON meal_types
    FOR INSERT WITH CHECK (
        club_id IN (
            SELECT club_id FROM club_members WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update meal_types from their club" ON meal_types
    FOR UPDATE USING (
        club_id IN (
            SELECT club_id FROM club_members WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete meal_types from their club" ON meal_types
    FOR DELETE USING (
        club_id IN (
            SELECT club_id FROM club_members WHERE user_id = auth.uid()
        )
    );

-- Políticas para meal_sessions
CREATE POLICY "Users can view meal_sessions from their club" ON meal_sessions
    FOR SELECT USING (
        club_id IN (
            SELECT club_id FROM club_members WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert meal_sessions for their club" ON meal_sessions
    FOR INSERT WITH CHECK (
        club_id IN (
            SELECT club_id FROM club_members WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update meal_sessions from their club" ON meal_sessions
    FOR UPDATE USING (
        club_id IN (
            SELECT club_id FROM club_members WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete meal_sessions from their club" ON meal_sessions
    FOR DELETE USING (
        club_id IN (
            SELECT club_id FROM club_members WHERE user_id = auth.uid()
        )
    );

-- Políticas para meal_participants
CREATE POLICY "Users can view meal_participants from their club" ON meal_participants
    FOR SELECT USING (
        club_id IN (
            SELECT club_id FROM club_members WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert meal_participants for their club" ON meal_participants
    FOR INSERT WITH CHECK (
        club_id IN (
            SELECT club_id FROM club_members WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update meal_participants from their club" ON meal_participants
    FOR UPDATE USING (
        club_id IN (
            SELECT club_id FROM club_members WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete meal_participants from their club" ON meal_participants
    FOR DELETE USING (
        club_id IN (
            SELECT club_id FROM club_members WHERE user_id = auth.uid()
        )
    );

-- =====================================================
-- TRIGGERS E FUNÇÕES
-- =====================================================

-- Função para validar participantes
CREATE OR REPLACE FUNCTION validate_meal_participant()
RETURNS TRIGGER AS $$
BEGIN
    -- Verificar se o participante existe baseado no tipo
    IF NEW.participant_type = 'player' THEN
        IF NOT EXISTS (
            SELECT 1 FROM players 
            WHERE id = NEW.participant_id 
            AND club_id = NEW.club_id
        ) THEN
            RAISE EXCEPTION 'Jogador não encontrado no clube';
        END IF;
    ELSIF NEW.participant_type = 'collaborator' THEN
        IF NOT EXISTS (
            SELECT 1 FROM collaborators 
            WHERE id::text = NEW.participant_id 
            AND club_id = NEW.club_id
        ) THEN
            RAISE EXCEPTION 'Colaborador não encontrado no clube';
        END IF;
    ELSE
        RAISE EXCEPTION 'Tipo de participante inválido';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para validar participantes
CREATE TRIGGER trigger_validate_meal_participant
    BEFORE INSERT OR UPDATE ON meal_participants
    FOR EACH ROW
    EXECUTE FUNCTION validate_meal_participant();

-- =====================================================
-- DADOS INICIAIS (OPCIONAL)
-- =====================================================

-- Inserir alguns tipos de refeição padrão para cada clube
-- (Descomente se quiser dados iniciais)

/*
INSERT INTO meal_types (club_id, name, location, address)
SELECT 
    id as club_id,
    'Café da Manhã' as name,
    'Refeitório Principal' as location,
    'Sede do Clube' as address
FROM club_info;

INSERT INTO meal_types (club_id, name, location, address)
SELECT 
    id as club_id,
    'Almoço' as name,
    'Refeitório Principal' as location,
    'Sede do Clube' as address
FROM club_info;

INSERT INTO meal_types (club_id, name, location, address)
SELECT 
    id as club_id,
    'Jantar' as name,
    'Refeitório Principal' as location,
    'Sede do Clube' as address
FROM club_info;

INSERT INTO meal_types (club_id, name, location, address)
SELECT 
    id as club_id,
    'Lanche da Tarde' as name,
    'Refeitório Principal' as location,
    'Sede do Clube' as address
FROM club_info;
*/

-- =====================================================
-- COMENTÁRIOS FINAIS
-- =====================================================

-- Este script cria a estrutura completa para o sistema de alimentação
-- incluindo tabelas, índices, políticas de segurança e validações.
-- 
-- Para executar:
-- 1. Conecte-se ao seu banco Supabase
-- 2. Execute este script no SQL Editor
-- 3. Verifique se todas as tabelas foram criadas corretamente
-- 
-- Estrutura criada:
-- - meal_types: Tipos de refeição (café, almoço, jantar, etc.)
-- - meal_sessions: Sessões de alimentação vinculadas a alojamentos
-- - meal_participants: Participantes das refeições com controle de assinatura
-- 
-- Recursos incluídos:
-- - Índices para performance otimizada
-- - Políticas RLS para segurança por clube
-- - Triggers para validação de dados
-- - Constraints para integridade referencial
