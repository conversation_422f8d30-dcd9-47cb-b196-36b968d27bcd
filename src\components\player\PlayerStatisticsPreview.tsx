import { useState, useEffect } from "react";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { getPlayerAggregatedStatistics } from "@/api/api";
import { useToast } from "@/hooks/use-toast";
import { Skeleton } from "@/components/ui/skeleton";

interface PlayerStatisticsPreviewProps {
  playerId: string;
}

export function PlayerStatisticsPreview({ playerId }: PlayerStatisticsPreviewProps) {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<any>(null);

  useEffect(() => {
    const fetchStats = async () => {
      if (!clubId || !playerId || !user?.id) return;

      try {
        setLoading(true);
        const data = await getPlayerAggregatedStatistics(clubId, playerId, user.id);
        setStats(data);
      } catch (error) {
        console.error("Error fetching player statistics:", error);
        toast({
          title: "Erro",
          description: "Não foi possível carregar as estatísticas do jogador",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [clubId, playerId, user?.id, toast]);

  if (loading) {
    return (
      <div className="space-y-4">
        <p className="text-muted-foreground mb-4">Carregando estatísticas...</p>
        <div className="space-y-4">
          <div>
            <div className="flex items-center justify-between mb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-12" />
            </div>
            <Skeleton className="h-2 w-full" />
          </div>
          <div>
            <div className="flex items-center justify-between mb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-12" />
            </div>
            <Skeleton className="h-2 w-full" />
          </div>
          <div>
            <div className="flex items-center justify-between mb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-12" />
            </div>
            <Skeleton className="h-2 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="space-y-4">
        <p className="text-muted-foreground mb-4">Nenhuma estatística disponível</p>
      </div>
    );
  }

  // Calcular a largura das barras de progresso (máximo 100%)
  const minutesWidth = Math.min(100, (stats.total_minutes / 500) * 100);
  const goalsWidth = Math.min(100, (stats.total_goals / 5) * 100);
  const assistsWidth = Math.min(100, (stats.total_assists / 5) * 100);

  return (
    <div className="space-y-4">
      <p className="text-muted-foreground mb-4">Estatísticas Gerais</p>
      <div className="space-y-4">
        <div>
          <div className="flex items-center justify-between mb-2">
            <span>Minutos jogados</span>
            <span className="font-medium">{stats.total_minutes}'</span>
          </div>
          <div className="h-2 bg-muted rounded-full overflow-hidden">
            <div className="h-full bg-team-blue" style={{ width: `${minutesWidth}%` }} />
          </div>
        </div>
        <div>
          <div className="flex items-center justify-between mb-2">
            <span>Gols</span>
            <span className="font-medium">{stats.total_goals}</span>
          </div>
          <div className="h-2 bg-muted rounded-full overflow-hidden">
            <div className="h-full bg-team-blue" style={{ width: `${goalsWidth}%` }} />
          </div>
        </div>
        <div>
          <div className="flex items-center justify-between mb-2">
            <span>Assistências</span>
            <span className="font-medium">{stats.total_assists}</span>
          </div>
          <div className="h-2 bg-muted rounded-full overflow-hidden">
            <div className="h-full bg-team-blue" style={{ width: `${assistsWidth}%` }} />
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-4 mt-4 text-sm">
        <div className="flex justify-between">
          <span className="text-muted-foreground">Jogos:</span>
          <span className="font-medium">{stats.total_matches}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-muted-foreground">Finalizações:</span>
          <span className="font-medium">{stats.total_shots}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-muted-foreground">Cartões Amarelos:</span>
          <span className="font-medium">{stats.total_yellow_cards}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-muted-foreground">Cartões Vermelhos:</span>
          <span className="font-medium">{stats.total_red_cards}</span>
        </div>
      </div>
    </div>
  );
}
