import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { Collaborator, updateCollaborator } from "@/api/api";
import { useUser } from "@/context/UserContext";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  FileText,
  Upload,
  ExternalLink,
  Loader2,
  Trash2,
  AlertCircle
} from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { validateCPF } from "@/lib/validators";
import { fetchAddressByCEP } from "@/lib/cep";
import { formatDate } from "@/lib/utils";
import { v4 as uuidv4 } from "uuid";

// Document types
const COLLABORATOR_DOCUMENT_TYPES = [
  "identity",
  "cpf",
  "credential",
  "certificate",
  "other"
];

const COLLABORATOR_DOCUMENT_LABELS: Record<string, string> = {
  "identity": "Documento de Identidade",
  "cpf": "CPF",
  "credential": "Credencial Profissional",
  "certificate": "Certificado",
  "other": "Outro Documento"
};

// Tipo para documentos de colaborador
interface CollaboratorDocument {
  id: number;
  club_id: number;
  collaborator_id: number;
  document_type: string;
  file_url: string;
  status: string;
  uploaded_at: string;
  verified_at?: string;
  verified_by?: string;
  rejection_reason?: string;
  verifier_name?: string;
}

interface ColaboradorCombinadoDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  collaborator: Collaborator;
  onSuccess?: () => void;
}

export function ColaboradorCombinadoDialog({
  open,
  onOpenChange,
  clubId,
  collaborator,
  onSuccess
}: ColaboradorCombinadoDialogProps) {
  const { user } = useUser();
  const [activeTab, setActiveTab] = useState("basic");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Campos básicos
  const [fullName, setFullName] = useState(collaborator.full_name || "");
  const [role, setRole] = useState(collaborator.role || "");
  const [phone, setPhone] = useState(collaborator.phone || "");
  const [email, setEmail] = useState(collaborator.email || "");

  // Campos de documentos
  const [cpf, setCpf] = useState(collaborator.cpf || "");
  const [birthDate, setBirthDate] = useState(collaborator.birth_date ?
    new Date(collaborator.birth_date).toISOString().split('T')[0] : "");
  const [credentialNumber, setCredentialNumber] = useState(collaborator.credential_number || "");

  // Campos de endereço
  const [zipCode, setZipCode] = useState(collaborator.zip_code || "");
  const [state, setState] = useState(collaborator.state || "");
  const [city, setCity] = useState(collaborator.city || "");
  const [address, setAddress] = useState(collaborator.address || "");
  const [addressNumber, setAddressNumber] = useState(collaborator.address_number || "");

  // Estado para documentos
  const [documents, setDocuments] = useState<CollaboratorDocument[]>([]);
  const [documentsLoading, setDocumentsLoading] = useState(false);
  const [uploadingDocumentType, setUploadingDocumentType] = useState<string | null>(null);
  const [documentFile, setDocumentFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<CollaboratorDocument | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);

  // Atualizar campos quando o colaborador mudar
  useEffect(() => {
    setFullName(collaborator.full_name || "");
    setRole(collaborator.role || "");
    setPhone(collaborator.phone || "");
    setEmail(collaborator.email || "");
    setCpf(collaborator.cpf || "");
    setBirthDate(collaborator.birth_date ?
      new Date(collaborator.birth_date).toISOString().split('T')[0] : "");
    setCredentialNumber(collaborator.credential_number || "");
    setZipCode(collaborator.zip_code || "");
    setState(collaborator.state || "");
    setCity(collaborator.city || "");
    setAddress(collaborator.address || "");
    setAddressNumber(collaborator.address_number || "");
  }, [collaborator]);

  // Carregar documentos quando o diálogo for aberto ou quando mudar para a tab de documentos
  useEffect(() => {
    if (open && collaborator && activeTab === "documents") {
      fetchDocuments();
    }
  }, [open, collaborator, activeTab]);

  // Função para buscar endereço pelo CEP
  const handleZipCodeBlur = async () => {
    if (zipCode.length === 8 || zipCode.length === 9) {
      try {
        const addressData = await fetchAddressByCEP(zipCode);
        if (addressData) {
          setState(addressData.state);
          setCity(addressData.city);
          setAddress(addressData.street);
        }
      } catch (err) {
        console.error("Erro ao buscar CEP:", err);
      }
    }
  };

  // Função para buscar documentos
  const fetchDocuments = async () => {
    try {
      setDocumentsLoading(true);
      setError(null);

      // Buscar documentos diretamente do Supabase usando SQL
      const { data, error } = await supabase.rpc('get_collaborator_documents', {
        p_club_id: clubId,
        p_collaborator_id: collaborator.id
      });

      if (error) {
        throw new Error(`Erro ao buscar documentos: ${error.message}`);
      }

      // Converter os dados para o formato esperado
      const docs = Array.isArray(data) ? data.map(doc => ({
        ...doc,
        verifier_name: null // Simplificando para evitar problemas de tipagem
      })) : [];

      setDocuments(docs as CollaboratorDocument[]);
    } catch (err: any) {
      console.error("Erro ao buscar documentos:", err);
      setError(err.message || "Erro ao buscar documentos");
      toast({
        title: "Erro",
        description: err.message || "Erro ao buscar documentos",
        variant: "destructive",
      });
    } finally {
      setDocumentsLoading(false);
    }
  };

  // Função para fazer upload de documento
  const handleUploadDocument = async () => {
    if (!uploadingDocumentType || !documentFile || !user) {
      toast({
        title: "Erro",
        description: "Selecione um tipo de documento e um arquivo",
        variant: "destructive",
      });
      return;
    }

    try {
      setUploading(true);

      // 1. Fazer upload do arquivo para o Storage
      const fileExt = documentFile.name.split(".").pop();
      const fileName = `${uploadingDocumentType}-${uuidv4()}.${fileExt}`;
      const filePath = `${clubId}/collaborators/${collaborator.id}/${fileName}`;

      // Upload do arquivo
      const { error: uploadError } = await supabase.storage
        .from("playerdocuments") // Usando o mesmo bucket dos documentos de jogadores
        .upload(filePath, documentFile, {
          cacheControl: "3600",
          upsert: true,
          contentType: documentFile.type,
        });

      if (uploadError) {
        throw new Error(`Erro ao fazer upload do documento: ${uploadError.message}`);
      }

      // Obter URL pública
      const { data: urlData } = supabase.storage
        .from("playerdocuments")
        .getPublicUrl(filePath);

      // 2. Registrar o documento no banco de dados usando SQL direto para evitar problemas de tipagem
      const { error: insertError } = await supabase.rpc('insert_collaborator_document', {
        p_club_id: clubId,
        p_collaborator_id: collaborator.id,
        p_document_type: uploadingDocumentType,
        p_file_url: urlData.publicUrl,
        p_status: 'pending'
      });

      if (insertError) {
        throw new Error(`Erro ao registrar documento: ${insertError.message}`);
      }

      // Recarregar documentos
      await fetchDocuments();

      toast({
        title: "Sucesso",
        description: "Documento enviado com sucesso",
      });

      // Limpar estado
      setUploadingDocumentType(null);
      setDocumentFile(null);
    } catch (err: any) {
      console.error("Erro ao enviar documento:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao enviar documento",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  // Função para iniciar o processo de exclusão de documento
  const handleDeleteDocument = (doc: CollaboratorDocument) => {
    setDocumentToDelete(doc);
    setDeleteDialogOpen(true);
  };

  // Função para confirmar a exclusão do documento
  const confirmDeleteDocument = async () => {
    if (!documentToDelete) return;

    try {
      setDeleting(true);

      // 1. Extrair o caminho do arquivo da URL
      const fileUrl = documentToDelete.file_url;
      const storageUrl = supabase.storage.from('playerdocuments').getPublicUrl('').data.publicUrl;
      const filePath = fileUrl.replace(storageUrl, '');

      // 2. Excluir o arquivo do Storage
      if (filePath) {
        const { error: storageError } = await supabase.storage
          .from('playerdocuments')
          .remove([filePath]);

        if (storageError) {
          console.error("Erro ao excluir arquivo do storage:", storageError);
          // Continuar mesmo com erro no storage, para pelo menos remover o registro do banco
        }
      }

      // 3. Excluir o registro do banco de dados usando SQL direto para evitar problemas de tipagem
      const { error: dbError } = await supabase.rpc('delete_collaborator_document', {
        p_document_id: documentToDelete.id
      });

      if (dbError) {
        throw new Error(`Erro ao excluir documento: ${dbError.message}`);
      }

      // 4. Atualizar a lista de documentos
      setDocuments(documents.filter(d => d.id !== documentToDelete.id));

      toast({
        title: "Sucesso",
        description: "Documento excluído com sucesso",
      });
    } catch (err: any) {
      console.error("Erro ao excluir documento:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao excluir documento",
        variant: "destructive",
      });
    } finally {
      setDeleting(false);
      setDeleteDialogOpen(false);
      setDocumentToDelete(null);
    }
  };

  // Função para renderizar o status do documento
  const renderDocumentStatus = (status: string) => {
    switch (status) {
      case "verified":
        return <Badge className="bg-green-100 text-green-800">Verificado</Badge>;
      case "rejected":
        return <Badge variant="destructive">Rejeitado</Badge>;
      default:
        return <Badge variant="outline">Pendente</Badge>;
    }
  };

  // Função para salvar o colaborador
  const handleSave = async () => {
    try {
      setLoading(true);
      setError(null);

      // Validar campos obrigatórios
      if (!fullName) {
        throw new Error("O nome completo é obrigatório");
      }

      if (!role) {
        throw new Error("A função é obrigatória");
      }

      // Validar CPF se preenchido
      if (cpf && !validateCPF(cpf)) {
        throw new Error("CPF inválido");
      }

      // Atualizar o colaborador
      await updateCollaborator(
        clubId,
        user?.id || "",
        collaborator.id,
        {
          full_name: fullName,
          role,
          role_type: "technical", // Mantido para compatibilidade com o banco de dados
          phone: phone || undefined,
          email: email || undefined,
          cpf: cpf || undefined,
          birth_date: birthDate || undefined,
          credential_number: credentialNumber || undefined,
          zip_code: zipCode || undefined,
          state: state || undefined,
          city: city || undefined,
          address: address || undefined,
          address_number: addressNumber || undefined,
        }
      );

      toast({
        title: "Sucesso",
        description: "Colaborador atualizado com sucesso",
      });

      // Chamar callback de sucesso
      if (onSuccess) {
        onSuccess();
      }
    } catch (err: any) {
      console.error("Erro ao atualizar colaborador:", err);
      setError(err.message || "Erro ao atualizar colaborador");
      toast({
        title: "Erro",
        description: err.message || "Erro ao atualizar colaborador",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Editar Colaborador: {collaborator.full_name}</DialogTitle>
          </DialogHeader>

          {error && (
            <div className="bg-red-50 text-red-700 p-3 rounded-md text-sm mb-4">
              {error}
            </div>
          )}

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-3 mb-4">
              <TabsTrigger value="basic">Informações Básicas</TabsTrigger>
              <TabsTrigger value="documents">Documentos</TabsTrigger>
              <TabsTrigger value="address">Endereço</TabsTrigger>
            </TabsList>

            {/* Tab de Informações Básicas */}
            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="fullName">Nome Completo *</Label>
                  <Input
                    id="fullName"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    placeholder="Nome completo do colaborador"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="role">Função *</Label>
                  <Select
                    value={role}
                    onValueChange={setRole}
                  >
                    <SelectTrigger id="role">
                      <SelectValue placeholder="Selecione a função" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Técnico">Técnico</SelectItem>
                      <SelectItem value="Auxiliar técnico">Auxiliar técnico</SelectItem>
                      <SelectItem value="Preparador de goleiro">Preparador de goleiro</SelectItem>
                      <SelectItem value="Preparador físico">Preparador físico</SelectItem>
                      <SelectItem value="Supervisor">Supervisor</SelectItem>
                      <SelectItem value="Massagista">Massagista</SelectItem>
                      <SelectItem value="Fisioterapeuta">Fisioterapeuta</SelectItem>
                      <SelectItem value="Fisiologista">Fisiologista</SelectItem>
                      <SelectItem value="Psicóloga">Psicóloga</SelectItem>
                      <SelectItem value="Nutricionista">Nutricionista</SelectItem>
                      <SelectItem value="Coordenador">Coordenador</SelectItem>
                      <SelectItem value="Motorista">Motorista</SelectItem>
                      <SelectItem value="Roupeiro">Roupeiro</SelectItem>
                      <SelectItem value="Massa terapeuta">Massa terapeuta</SelectItem>
                      <SelectItem value="Gerente de futebol">Gerente de futebol</SelectItem>
                      <SelectItem value="CEO">CEO</SelectItem>
                      <SelectItem value="Gerente administrativo">Gerente administrativo</SelectItem>
                      <SelectItem value="Cozinheira">Cozinheira</SelectItem>
                      <SelectItem value="Cozinheiro">Cozinheiro</SelectItem>
                      <SelectItem value="Gerente de operações">Gerente de operações</SelectItem>
                      <SelectItem value="Presidente">Presidente</SelectItem>
                      <SelectItem value="Vice presidente">Vice presidente</SelectItem>
                      <SelectItem value="Sócio">Sócio</SelectItem>
                      <SelectItem value="Diretor">Diretor</SelectItem>
                      <SelectItem value="Auxiliar de limpeza">Auxiliar de limpeza</SelectItem>
                      <SelectItem value="Serviço geral">Serviço geral</SelectItem>
                      <SelectItem value="Jardineiro">Jardineiro</SelectItem>
                      <SelectItem value="Lavadora de roupas">Lavadora de roupas</SelectItem>
                      <SelectItem value="Assistente social">Assistente social</SelectItem>
                      <SelectItem value="Outros">Outros</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="phone">Telefone</Label>
                    <Input
                      id="phone"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      placeholder="(00) 00000-0000"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">E-mail</Label>
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Tab de Documentos */}
            <TabsContent value="documents" className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="cpf">CPF</Label>
                    <Input
                      id="cpf"
                      value={cpf}
                      onChange={(e) => setCpf(e.target.value)}
                      placeholder="000.000.000-00"
                      maxLength={14}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="birthDate">Data de Nascimento</Label>
                    <Input
                      id="birthDate"
                      type="date"
                      value={birthDate}
                      onChange={(e) => setBirthDate(e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="credentialNumber">Número de Registro no Conselho</Label>
                  <Input
                    id="credentialNumber"
                    value={credentialNumber}
                    onChange={(e) => setCredentialNumber(e.target.value)}
                    placeholder="Número de registro profissional"
                  />
                </div>

                {/* Seção de upload de documentos */}
                <div className="bg-muted/30 p-4 rounded-md space-y-3 mt-4">
                  <h3 className="text-sm font-medium">Enviar novo documento</h3>
                  <div className="grid grid-cols-2 gap-3">
                    <Select
                      value={uploadingDocumentType || ""}
                      onValueChange={setUploadingDocumentType}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o tipo de documento" />
                      </SelectTrigger>
                      <SelectContent>
                        {COLLABORATOR_DOCUMENT_TYPES.map((type: string) => (
                          <SelectItem key={type} value={type}>
                            {COLLABORATOR_DOCUMENT_LABELS[type]}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <div className="flex items-center gap-2">
                      <input
                        type="file"
                        id="documentFile"
                        className="hidden"
                        accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                        onChange={(e) => {
                          if (e.target.files && e.target.files[0]) {
                            setDocumentFile(e.target.files[0]);
                          }
                        }}
                      />
                      <Button
                        variant="outline"
                        onClick={() => document.getElementById("documentFile")?.click()}
                        className="flex-1"
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        {documentFile ? documentFile.name : "Selecionar arquivo"}
                      </Button>
                      <Button
                        onClick={handleUploadDocument}
                        disabled={!uploadingDocumentType || !documentFile || uploading}
                      >
                        {uploading ? (
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        ) : (
                          <FileText className="h-4 w-4 mr-2" />
                        )}
                        Enviar
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Lista de documentos */}
                {documentsLoading ? (
                  <div className="text-center py-4">
                    <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                    <p className="text-sm text-muted-foreground mt-2">Carregando documentos...</p>
                  </div>
                ) : documents.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <FileText className="h-12 w-12 mx-auto mb-2 opacity-20" />
                    <p>Nenhum documento encontrado</p>
                  </div>
                ) : (
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Tipo</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Data de Envio</TableHead>
                          <TableHead className="text-right">Ações</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {documents.map((doc) => (
                          <TableRow key={doc.id}>
                            <TableCell>
                              {COLLABORATOR_DOCUMENT_LABELS[doc.document_type as string] || doc.document_type}
                            </TableCell>
                            <TableCell>{renderDocumentStatus(doc.status)}</TableCell>
                            <TableCell>{formatDate(doc.uploaded_at)}</TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end space-x-2">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  asChild
                                >
                                  <a href={doc.file_url} target="_blank" rel="noopener noreferrer">
                                    <ExternalLink className="h-4 w-4" />
                                  </a>
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="text-red-500 hover:text-red-700 hover:bg-red-50"
                                  onClick={() => handleDeleteDocument(doc)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </div>
            </TabsContent>

            {/* Tab de Endereço */}
            <TabsContent value="address" className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="zipCode">CEP</Label>
                    <Input
                      id="zipCode"
                      value={zipCode}
                      onChange={(e) => setZipCode(e.target.value)}
                      onBlur={handleZipCodeBlur}
                      placeholder="00000-000"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="state">Estado</Label>
                    <Input
                      id="state"
                      value={state}
                      onChange={(e) => setState(e.target.value)}
                      placeholder="UF"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="city">Cidade</Label>
                  <Input
                    id="city"
                    value={city}
                    onChange={(e) => setCity(e.target.value)}
                    placeholder="Cidade"
                  />
                </div>

                <div className="grid grid-cols-4 gap-4">
                  <div className="col-span-3 space-y-2">
                    <Label htmlFor="address">Endereço</Label>
                    <Input
                      id="address"
                      value={address}
                      onChange={(e) => setAddress(e.target.value)}
                      placeholder="Rua, Avenida, etc."
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="addressNumber">Número</Label>
                    <Input
                      id="addressNumber"
                      value={addressNumber}
                      onChange={(e) => setAddressNumber(e.target.value)}
                      placeholder="Nº"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancelar
            </Button>
            <Button onClick={handleSave} disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Salvar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo de confirmação para exclusão de documento */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Excluir documento</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir este documento? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleting}>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                confirmDeleteDocument();
              }}
              disabled={deleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {deleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Excluindo...
                </>
              ) : (
                'Excluir'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}