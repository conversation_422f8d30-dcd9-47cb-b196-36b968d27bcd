import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { InventoryProduct, registerInventoryTransaction } from "@/api/api";
import { ArrowUpCircle, ArrowDownCircle } from "lucide-react";

// Esquema de validação
const transactionSchema = z.object({
  quantity: z.coerce.number().positive("Quantidade deve ser maior que zero"),
  notes: z.string().optional(),
});

type TransactionFormValues = z.infer<typeof transactionSchema>;

interface MovimentarEstoqueDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  product: InventoryProduct | null;
  transactionType: 'entrada' | 'saida';
  onSuccess: () => void;
}

export function MovimentarEstoqueDialog({
  open,
  onOpenChange,
  product,
  transactionType,
  onSuccess,
}: MovimentarEstoqueDialogProps) {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  // Inicializar formulário
  const form = useForm<TransactionFormValues>({
    resolver: zodResolver(transactionSchema),
    defaultValues: {
      quantity: 1,
      notes: "",
    },
  });

  // Função para registrar a transação
  const onSubmit = async (data: TransactionFormValues) => {
    if (!product) return;

    try {
      setIsLoading(true);

      // Verificar se há quantidade suficiente para saída
      if (transactionType === 'saida' && data.quantity > product.quantity) {
        toast({
          title: "Quantidade insuficiente",
          description: `Quantidade disponível: ${product.quantity}`,
          variant: "destructive",
        });
        return;
      }

      // Registrar a transação
      await registerInventoryTransaction(
        clubId,
        product.id,
        data.quantity,
        transactionType,
        data.notes,
        user?.id
      );

      toast({
        title: "Transação registrada",
        description: `${transactionType === 'entrada' ? 'Entrada' : 'Saída'} de ${data.quantity} unidades registrada com sucesso.`,
      });

      // Fechar o diálogo e atualizar a lista
      onOpenChange(false);
      onSuccess();
    } catch (error) {
      console.error("Erro ao registrar transação:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao registrar a transação. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!product) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {transactionType === 'entrada' ? (
              <>
                <ArrowUpCircle className="h-5 w-5 text-green-500" />
                <span>Registrar Entrada</span>
              </>
            ) : (
              <>
                <ArrowDownCircle className="h-5 w-5 text-red-500" />
                <span>Registrar Saída</span>
              </>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="py-2">
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <p className="text-sm font-medium mb-1">Produto</p>
              <p className="text-base">{product.name}</p>
            </div>
            <div>
              <p className="text-sm font-medium mb-1">Estoque Atual</p>
              <p className="text-base">{product.quantity} unidades</p>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <p className="text-sm font-medium mb-1">Departamento</p>
              <p className="text-base">{product.department}</p>
            </div>
            <div>
              <p className="text-sm font-medium mb-1">Localização</p>
              <p className="text-base">{product.location || "Não especificada"}</p>
            </div>
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="quantity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Quantidade</FormLabel>
                  <FormControl>
                    <Input type="number" min="1" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Observações</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Observações sobre a transação"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                isLoading={isLoading}
                variant={transactionType === 'entrada' ? "default" : "destructive"}
              >
                {transactionType === 'entrada' ? "Registrar Entrada" : "Registrar Saída"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
