import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { createSalaryAdvance, getTotalAdvances } from "@/api/api";
import { formatCurrency } from "@/utils/formatters";

// Schema para validação
const advanceSchema = z.object({
  amount: z.coerce.number().positive("Valor deve ser maior que zero"),
  description: z.string().optional(),
  advance_date: z.string().min(1, "Data é obrigatória"),
  payment_method: z.string().min(1, "Método de pagamento é obrigatório"),
  month: z.coerce.number().min(1).max(12, "Mês deve estar entre 1 e 12"),
  year: z.coerce.number().min(2000).max(2100, "Ano inválido"),
});

type AdvanceFormValues = z.infer<typeof advanceSchema>;

interface AdicionarValeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  personId: number;
  personType: 'player' | 'collaborator';
  personName: string;
  personRole?: string;
  salary?: number;
  onSuccess: () => void;
}

export function AdicionarValeDialog({
  open,
  onOpenChange,
  personId,
  personType,
  personName,
  personRole,
  salary,
  onSuccess,
}: AdicionarValeDialogProps) {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [totalAdvances, setTotalAdvances] = useState(0);

  // Inicializar formulário
  const form = useForm<AdvanceFormValues>({
    resolver: zodResolver(advanceSchema),
    defaultValues: {
      amount: 0,
      description: "",
      advance_date: new Date().toISOString().split("T")[0],
      payment_method: "pix",
      month: new Date().getMonth() + 1, // Mês atual (1-12)
      year: new Date().getFullYear(), // Ano atual
    },
  });

  // Carregar total de adiantamentos quando o diálogo é aberto
  useEffect(() => {
    if (open && personId) {
      const month = form.getValues("month");
      const year = form.getValues("year");
      loadTotalAdvances(month, year);
    }
  }, [open, personId, personType]);

  // Carregar total de adiantamentos quando mês/ano mudam
  useEffect(() => {
    const subscription = form.watch((_, { name }) => {
      if (name === "month" || name === "year") {
        const month = form.getValues("month");
        const year = form.getValues("year");
        if (month && year) {
          loadTotalAdvances(month, year);
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [form.watch]);

  // Carregar total de adiantamentos
  const loadTotalAdvances = async (month: number, year: number) => {
    try {
      const total = await getTotalAdvances(clubId, personType, personId, month, year);
      setTotalAdvances(total);
    } catch (error) {
      console.error("Erro ao carregar total de adiantamentos:", error);
    }
  };

  // Função para registrar o adiantamento
  const onSubmit = async (data: AdvanceFormValues) => {
    if (!user?.id) {
      toast({
        title: "Erro",
        description: "Usuário não autenticado",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);

      // Verificar se o valor do adiantamento é maior que o salário disponível
      if (salary && data.amount > (salary - totalAdvances)) {
        toast({
          title: "Valor excede o disponível",
          description: `O valor do adiantamento excede o salário disponível (${formatCurrency(salary - totalAdvances)})`,
          variant: "destructive",
        });
        return;
      }

      // Criar adiantamento
      await createSalaryAdvance(clubId, user.id, {
        person_id: personId,
        person_type: personType,
        amount: data.amount,
        description: data.description,
        advance_date: data.advance_date,
        payment_method: data.payment_method,
        month: data.month,
        year: data.year,
        status: "active",
      });

      toast({
        title: "Adiantamento registrado",
        description: `Adiantamento de ${formatCurrency(data.amount)} registrado com sucesso para ${personName}`,
      });

      // Fechar o diálogo e atualizar a lista
      onOpenChange(false);
      onSuccess();
    } catch (error: any) {
      console.error("Erro ao registrar adiantamento:", error);
      toast({
        title: "Erro",
        description: error.message || "Ocorreu um erro ao registrar o adiantamento",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Adicionar Vale (Adiantamento)</DialogTitle>
        </DialogHeader>

        <div className="py-2">
          <div className="grid grid-cols-1 gap-4 mb-4">
            <div>
              <p className="text-sm font-medium mb-1">Pessoa</p>
              <p className="text-base">{personName}</p>
              {personRole && <p className="text-sm text-muted-foreground">{personRole}</p>}
            </div>
            {salary && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium mb-1">Salário</p>
                  <p className="text-base">{formatCurrency(salary)}</p>
                </div>
                <div>
                  <p className="text-sm font-medium mb-1">Adiantamentos no mês</p>
                  <p className="text-base">{formatCurrency(totalAdvances)}</p>
                </div>
                <div>
                  <p className="text-sm font-medium mb-1">Disponível</p>
                  <p className="text-base font-semibold">{formatCurrency(salary - totalAdvances)}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="month"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mês</FormLabel>
                    <Select
                      value={field.value.toString()}
                      onValueChange={(value) => field.onChange(parseInt(value))}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o mês" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="1">Janeiro</SelectItem>
                        <SelectItem value="2">Fevereiro</SelectItem>
                        <SelectItem value="3">Março</SelectItem>
                        <SelectItem value="4">Abril</SelectItem>
                        <SelectItem value="5">Maio</SelectItem>
                        <SelectItem value="6">Junho</SelectItem>
                        <SelectItem value="7">Julho</SelectItem>
                        <SelectItem value="8">Agosto</SelectItem>
                        <SelectItem value="9">Setembro</SelectItem>
                        <SelectItem value="10">Outubro</SelectItem>
                        <SelectItem value="11">Novembro</SelectItem>
                        <SelectItem value="12">Dezembro</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="year"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ano</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Valor</FormLabel>
                  <FormControl>
                    <Input type="number" step="0.01" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="advance_date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Data</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="payment_method"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Método de Pagamento</FormLabel>
                  <Select
                    value={field.value}
                    onValueChange={field.onChange}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o método" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="pix">PIX</SelectItem>
                      <SelectItem value="transfer">Transferência</SelectItem>
                      <SelectItem value="cash">Dinheiro</SelectItem>
                      <SelectItem value="check">Cheque</SelectItem>
                      <SelectItem value="other">Outro</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Observações</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Observações sobre o adiantamento"
                      className="resize-none"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                isLoading={isLoading}
              >
                Registrar Adiantamento
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
