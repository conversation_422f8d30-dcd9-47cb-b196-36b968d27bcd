import { useState, useRef, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, Save, Trash2 } from "lucide-react";
import { useUser } from "@/context/UserContext";
import { supabase } from "@/integrations/supabase/client";
import { updateInventoryRequest } from "@/api/api";

interface AssinaturaDigitalSolicitacaoDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  requestId: number | null;
  clubId: number;
  onSuccess?: () => void;
}

export function AssinaturaDigitalSolicitacaoDialog({
  open,
  onOpenChange,
  requestId,
  clubId,
  onSuccess
}: AssinaturaDigitalSolicitacaoDialogProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [hasSignature, setHasSignature] = useState(false);
  const [loading, setLoading] = useState(false);
  const { user } = useUser();
  const { toast } = useToast();

  // Clear canvas when dialog opens
  useEffect(() => {
    if (open) {
      clearCanvas();
    }
  }, [open]);

  // Start drawing
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    setIsDrawing(true);

    // Get position
    let x, y;
    if ('touches' in e) {
      // Touch event
      const rect = canvas.getBoundingClientRect();
      x = e.touches[0].clientX - rect.left;
      y = e.touches[0].clientY - rect.top;
    } else {
      // Mouse event
      x = e.nativeEvent.offsetX;
      y = e.nativeEvent.offsetY;
    }

    ctx.beginPath();
    ctx.moveTo(x, y);
  };

  // Draw
  const draw = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Get position
    let x, y;
    if ('touches' in e) {
      // Touch event
      e.preventDefault(); // Prevent scrolling when drawing
      const rect = canvas.getBoundingClientRect();
      x = e.touches[0].clientX - rect.left;
      y = e.touches[0].clientY - rect.top;
    } else {
      // Mouse event
      x = e.nativeEvent.offsetX;
      y = e.nativeEvent.offsetY;
    }

    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.strokeStyle = '#000';

    ctx.lineTo(x, y);
    ctx.stroke();

    setHasSignature(true);
  };

  // End drawing
  const endDrawing = () => {
    setIsDrawing(false);
  };

  // Clear canvas
  const clearCanvas = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);
    setHasSignature(false);
  };

  // Convert data URL to Blob
  const dataURLtoBlob = (dataURL: string): Blob => {
    const arr = dataURL.split(',');
    const mime = arr[0].match(/:(.*?);/)![1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new Blob([u8arr], { type: mime });
  };

  // Save signature
  const handleSave = async () => {
    if (!requestId || !hasSignature) return;

    setLoading(true);

    try {
      // Get signature as data URL
      const canvas = canvasRef.current;
      if (!canvas) return;

      // Convert canvas to data URL
      const signatureDataUrl = canvas.toDataURL('image/png');

      // Upload signature image to Supabase Storage
      const fileName = `signatures/${clubId}/${requestId}_${Date.now()}.png`;
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('profileimages')
        .upload(fileName, dataURLtoBlob(signatureDataUrl), {
          contentType: 'image/png',
          upsert: true
        });

      if (uploadError) {
        throw new Error(`Erro ao fazer upload da assinatura: ${uploadError.message}`);
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('profileimages')
        .getPublicUrl(fileName);

      const signatureUrl = urlData.publicUrl;

      // Update the request with the signature URL
      await updateInventoryRequest(
        clubId,
        requestId,
        {
          requester_signature_url: signatureUrl
        },
        user?.id
      );

      toast({
        title: "Solicitação assinada",
        description: "A solicitação foi assinada digitalmente com sucesso.",
      });

      if (onSuccess) {
        onSuccess();
      }

      onOpenChange(false);
    } catch (error) {
      console.error("Error signing request:", error);
      toast({
        title: "Erro ao assinar solicitação",
        description: "Ocorreu um erro ao assinar a solicitação.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Assinar Solicitação</DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <p className="text-sm text-muted-foreground mb-4">
            Desenhe sua assinatura no campo abaixo para assinar digitalmente a solicitação.
          </p>

          <div className="border rounded-md p-1 bg-gray-50">
            <canvas
              ref={canvasRef}
              width={450}
              height={200}
              className="border border-dashed border-gray-300 rounded bg-white w-full touch-none"
              onMouseDown={startDrawing}
              onMouseMove={draw}
              onMouseUp={endDrawing}
              onMouseLeave={endDrawing}
              onTouchStart={startDrawing}
              onTouchMove={draw}
              onTouchEnd={endDrawing}
            />
          </div>

          <div className="flex justify-end mt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={clearCanvas}
              className="gap-1"
            >
              <Trash2 className="h-4 w-4" />
              Limpar
            </Button>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button
            onClick={handleSave}
            disabled={loading || !hasSignature}
            className="gap-2"
          >
            {loading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Save className="h-4 w-4" />
            )}
            Assinar Solicitação
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
