import { create } from "zustand";
import { ClubInfo } from "../api/api";
import { getClubInfo, updateClubInfo } from "../api/api";

interface ClubInfoState {
  clubInfo: ClubInfo | null;
  loading: boolean;
  error: string | null;
  fetchClubInfo: (clubId: number) => Promise<void>;
  updateClubInfo: (clubId: number, info: Partial<ClubInfo>) => Promise<void>;
}

export const useClubInfoStore = create<ClubInfoState>((set) => ({
  clubInfo: null,
  loading: false,
  error: null,

  fetchClubInfo: async (clubId: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const clubInfo = await getClubInfo(clubId);
      set({ clubInfo, loading: false });
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao buscar informações do clube", loading: false });
    }
  },

  updateClubInfo: async (clubId: number, info: Partial<ClubInfo>): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const updated = await updateClubInfo(clubId, info);
      if (updated) {
        set({ clubInfo: updated, loading: false });
      } else {
        set({ error: "Informações do clube não encontradas", loading: false });
      }
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao atualizar informações do clube", loading: false });
    }
  },
}));
