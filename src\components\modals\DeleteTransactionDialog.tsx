import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FinancialTransaction } from "@/api/api";
import { useFinancialStore } from "@/store/useFinancialStore";
import { toast } from "@/hooks/use-toast";
import { useState } from "react";
import { Trash } from "lucide-react";

interface DeleteTransactionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  transaction: FinancialTransaction | null;
  clubId: number;
}

export function DeleteTransactionDialog({ open, onOpenChange, transaction, clubId }: DeleteTransactionDialogProps) {
  const [loading, setLoading] = useState(false);
  const { deleteTransaction } = useFinancialStore();

  const handleDelete = async () => {
    if (!transaction) return;

    setLoading(true);
    try {
      await deleteTransaction(clubId, transaction.id);
      toast({
        title: "Sucesso",
        description: "Transação excluída com sucesso",
      });
      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao excluir transação:", error);
      toast({
        title: "Erro",
        description: "Não foi possível excluir a transação",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Excluir Transação</DialogTitle>
          <DialogDescription>
            Esta ação não pode ser desfeita. Isso excluirá permanentemente a transação.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <p>
            Você tem certeza que deseja excluir a transação{" "}
            <strong>{transaction?.description}</strong> no valor de{" "}
            <strong>
              {typeof transaction?.amount === 'string'
                ? `R$ ${parseFloat(transaction.amount).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`
                : transaction?.amount ? `R$ ${transaction.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}` : ""}
            </strong>?
          </p>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button variant="destructive" onClick={handleDelete} disabled={loading}>
            <Trash className="h-4 w-4 mr-2" />
            {loading ? "Excluindo..." : "Excluir"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
