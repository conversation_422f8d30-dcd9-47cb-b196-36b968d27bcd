-- <PERSON><PERSON><PERSON> para corrigir as permissões de usuários existentes
-- <PERSON><PERSON> script atualiza a role e as permissões de usuários existentes para garantir
-- que eles tenham acesso adequado ao sistema após a implementação do sistema de permissões

-- 0. G<PERSON><PERSON>r que a tabela audit_logs exista
CREATE TABLE IF NOT EXISTS audit_logs (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL,
  user_id UUID NOT NULL,
  action VARCHAR(255) NOT NULL,
  details JSONB DEFAULT '{}'::jsonb,
  success BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 1. Atualizar a role de todos os usuários que são donos de clubes para 'president'
UPDATE club_members
SET role = 'president'
WHERE user_id IN (
    SELECT user_id
    FROM club_members
    WHERE status = 'ativo'
);

-- 2. <PERSON><PERSON><PERSON><PERSON> as permissões para usuários com role 'president'
UPDATE club_members
SET permissions = '{
  "players.view": true,
  "players.create": true,
  "players.edit": true,
  "players.delete": true,
  "players.documents.view": true,
  "players.documents.verify": true,
  "players.finances.view": true,
  "players.finances.edit": true,
  "matches.view": true,
  "matches.create": true,
  "matches.edit": true,
  "matches.delete": true,
  "matches.lineup": true,
  "matches.events": true,
  "trainings.view": true,
  "trainings.create": true,
  "trainings.edit": true,
  "trainings.delete": true,
  "departments.view": true,
  "departments.create": true,
  "departments.edit": true,
  "departments.delete": true,
  "users.view": true,
  "users.create": true,
  "users.edit": true,
  "users.delete": true,
  "users.permissions": true,
  "settings.view": true,
  "settings.edit": true,
  "finances.view": true,
  "finances.create": true,
  "finances.edit": true,
  "finances.delete": true,
  "medical.view": true,
  "medical.create": true,
  "medical.edit": true,
  "medical.delete": true,
  "agenda.view": true,
  "agenda.create": true,
  "agenda.edit": true,
  "agenda.delete": true,
  "categories.view": true,
  "categories.create": true,
  "categories.edit": true,
  "categories.delete": true,
  "accommodations.view": true,
  "accommodations.create": true,
  "accommodations.edit": true,
  "accommodations.delete": true,
  "reports.view": true,
  "reports.generate": true,
  "statistics.view": true,
  "analytics.view": true,
  "communication.view": true,
  "communication.send": true,
  "audit_logs.view": true,
  "audit_logs.export": true,
  "president.club_ownership": true,
  "president.financial_approval": true
}'::jsonb
WHERE role = 'president';

-- 3. Atualizar a role de todos os usuários que são membros de clubes para 'admin' se ainda não tiverem uma role
UPDATE club_members
SET role = 'admin'
WHERE role IS NULL OR role = 'user';

-- 4. Adicionar permissões básicas para usuários com role 'admin'
UPDATE club_members
SET permissions = '{
  "players.view": true,
  "players.create": true,
  "players.edit": true,
  "players.documents.view": true,
  "matches.view": true,
  "matches.create": true,
  "matches.edit": true,
  "matches.lineup": true,
  "trainings.view": true,
  "trainings.create": true,
  "users.view": true,
  "settings.view": true,
  "agenda.view": true,
  "categories.view": true,
  "reports.view": true,
  "statistics.view": true
}'::jsonb
WHERE role = 'admin' AND (permissions IS NULL OR permissions = '{}'::jsonb);

-- 5. Garantir que todos os usuários tenham pelo menos permissões básicas
UPDATE club_members
SET permissions = COALESCE(permissions, '{}'::jsonb) || '{"players.view": true, "matches.view": true, "agenda.view": true}'::jsonb
WHERE permissions IS NULL OR permissions = '{}'::jsonb;

-- 6. Atualizar a tabela de usuários para garantir que tenham roles consistentes
UPDATE users u
SET role = cm.role
FROM club_members cm
WHERE u.id = cm.user_id AND (u.role IS NULL OR u.role = 'user') AND cm.role IN ('president', 'admin');

-- 7. Adicionar um log de auditoria para registrar esta operação
INSERT INTO audit_logs (club_id, user_id, action, details, success)
SELECT
    cm.club_id,
    cm.user_id,
    'system.fix_permissions',
    jsonb_build_object(
        'old_role', 'user',
        'new_role', cm.role,
        'permissions', cm.permissions
    ),
    true
FROM club_members cm
WHERE cm.role IN ('president', 'admin');

-- Mensagem de conclusão
DO $$
BEGIN
    RAISE NOTICE 'Permissões de usuários atualizadas com sucesso!';
END $$;
