import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { useAdministrativeDocumentsStore } from "@/store/useAdministrativeDocumentsStore";
import { AdministrativeDocument } from "@/api/api";
import { RichTextEditor } from "@/components/ui/rich-text-editor";

interface EditarDocumentoDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  document: AdministrativeDocument | null;
}

export function EditarDocumentoDialog({
  open,
  onOpenChange,
  clubId,
  document
}: EditarDocumentoDialogProps) {
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [documentType, setDocumentType] = useState<'oficio' | 'memorando'>('oficio');
  const [error, setError] = useState("");

  const { updateDocument, loading } = useAdministrativeDocumentsStore();

  // Update form when document changes
  useEffect(() => {
    if (document) {
      setTitle(document.title);
      setContent(document.content);
      setDocumentType(document.document_type);
    }
  }, [document]);

  const handleSave = async () => {
    if (!document) {
      return;
    }

    if (!title.trim()) {
      setError("O título é obrigatório.");
      return;
    }

    if (!content.trim()) {
      setError("O conteúdo é obrigatório.");
      return;
    }

    setError("");

    try {
      await updateDocument(clubId, document.id, {
        title,
        content,
        document_type: documentType
      });

      toast({
        title: "Documento atualizado",
        description: "O documento foi atualizado com sucesso.",
      });

      onOpenChange(false);
    } catch (err) {
      setError("Erro ao atualizar documento.");
      toast({
        title: "Erro ao atualizar documento",
        description: "Ocorreu um erro ao atualizar o documento.",
        variant: "destructive",
      });
    }
  };

  if (!document) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Editar Documento #{document.document_number}</DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="document-type" className="text-right">
              Tipo
            </Label>
            <Select
              value={documentType}
              onValueChange={(value) => setDocumentType(value as 'oficio' | 'memorando')}
            >
              <SelectTrigger id="document-type" className="col-span-3">
                <SelectValue placeholder="Selecione o tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="oficio">Ofício</SelectItem>
                <SelectItem value="memorando">Memorando</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="title" className="text-right">
              Título
            </Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="col-span-3"
            />
          </div>

          <div className="grid grid-cols-4 items-start gap-4">
            <Label htmlFor="content" className="text-right pt-2">
              Conteúdo
            </Label>
            <div className="col-span-3">
              <RichTextEditor
                content={content}
                onChange={setContent}
              />
            </div>
          </div>

          {error && (
            <div className="text-red-500 text-sm col-span-4 text-center">
              {error}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            {loading ? "Salvando..." : "Salvar"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
