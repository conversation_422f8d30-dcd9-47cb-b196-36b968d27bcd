import { supabase } from "@/integrations/supabase/client";

export type CollaboratorFinancialEntry = {
  month: number;
  year: number;
  amount: number;
  description: string;
};

export type CollaboratorBankingInfo = {
  bank_name?: string;
  account_number?: string;
  agency?: string;
  pix?: string;
};

export type CollaboratorFinancialData = {
  [key: string]: CollaboratorFinancialEntry[]; // Chave no formato "YYYY-MM"
};

/**
 * Obtém os dados financeiros de um colaborador
 * @param clubId ID do clube
 * @param collaboratorId ID do colaborador
 * @returns Dados financeiros do colaborador
 */
export async function getCollaboratorFinancialData(
  clubId: number,
  collaboratorId: number
): Promise<CollaboratorFinancialData> {
  try {
    const { data: collaborator, error } = await supabase
      .from("collaborators")
      .select("financial_data")
      .eq("club_id", clubId)
      .eq("id", collaboratorId)
      .single();

    if (error) {
      throw new Error(`Erro ao obter dados financeiros: ${error.message}`);
    }

    return collaborator.financial_data || {};
  } catch (error: any) {
    console.error("Erro ao obter dados financeiros:", error);
    throw new Error(error.message || "Erro ao obter dados financeiros");
  }
}

/**
 * Adiciona uma entrada financeira para um colaborador
 * @param clubId ID do clube
 * @param collaboratorId ID do colaborador
 * @param month Mês (1-12)
 * @param year Ano
 * @param amount Valor (positivo para receita, negativo para despesa)
 * @param description Descrição
 * @param category Categoria da transação (opcional, padrão: "salários")
 * @returns Dados financeiros atualizados
 */
export async function addCollaboratorFinancialEntry(
  clubId: number,
  collaboratorId: number,
  month: number,
  year: number,
  amount: number,
  description: string,
  category: string = "salários"
): Promise<CollaboratorFinancialData> {
  try {
    // Validar mês
    if (month < 1 || month > 12) {
      throw new Error("Mês inválido. Deve ser um número entre 1 e 12.");
    }

    // Obter dados financeiros atuais
    const financialData = await getCollaboratorFinancialData(clubId, collaboratorId);

    // Chave para o mês e ano
    const key = `${year}-${month.toString().padStart(2, "0")}`;

    // Adicionar nova entrada
    const entries = financialData[key] || [];
    entries.push({
      month,
      year,
      amount,
      description,
    });

    // Atualizar dados financeiros
    const updatedData = {
      ...financialData,
      [key]: entries,
    };

    // Obter nome do colaborador para a descrição da transação financeira
    const { data: collaboratorData, error: collaboratorError } = await supabase
      .from("collaborators")
      .select("full_name")
      .eq("club_id", clubId)
      .eq("id", collaboratorId)
      .single();

    if (collaboratorError) {
      console.error("Erro ao buscar nome do colaborador:", collaboratorError);
      throw new Error(`Erro ao buscar nome do colaborador: ${collaboratorError.message}`);
    }

    const collaboratorName = collaboratorData.full_name;

    // Criar uma transação financeira geral
    // Usar o primeiro dia do mês especificado para a transação
    const transactionDate = new Date(year, month - 1, 1).toISOString().split('T')[0];
    const transactionType = amount < 0 ? "despesa" : "receita";
    const transactionCategory = category;
    const transactionDescription = `${description} - ${collaboratorName}`;

    console.log("Criando transação financeira:", {
      date: transactionDate,
      type: transactionType,
      category: transactionCategory,
      amount: Math.abs(amount),
      description: transactionDescription,
      collaborator_id: collaboratorId
    });

    try {
      // Inserir na tabela financial_transactions
      const { error: transactionError } = await supabase
        .from("financial_transactions")
        .insert({
          club_id: clubId,
          date: transactionDate,
          type: transactionType,
          category: transactionCategory,
          amount: Math.abs(amount), // Valor absoluto, pois o tipo já indica se é receita ou despesa
          description: transactionDescription,
          collaborator_id: collaboratorId // Adicionar referência ao colaborador
        });

      if (transactionError) {
        console.error("Erro ao criar transação financeira:", transactionError);
      }
    } catch (err) {
      console.error("Exceção ao criar transação financeira:", err);
    }

    // Salvar no banco de dados
    const { data, error } = await supabase
      .from("collaborators")
      .update({
        financial_data: updatedData,
      })
      .eq("club_id", clubId)
      .eq("id", collaboratorId)
      .select("financial_data")
      .single();

    if (error) {
      throw new Error(`Erro ao adicionar entrada financeira: ${error.message}`);
    }

    return data.financial_data;
  } catch (error: any) {
    console.error("Erro ao adicionar entrada financeira:", error);
    throw new Error(error.message || "Erro ao adicionar entrada financeira");
  }
}

/**
 * Remove uma entrada financeira de um colaborador
 * @param clubId ID do clube
 * @param collaboratorId ID do colaborador
 * @param month Mês (1-12)
 * @param year Ano
 * @param index Índice da entrada a ser removida
 * @returns Dados financeiros atualizados
 */
export async function removeCollaboratorFinancialEntry(
  clubId: number,
  collaboratorId: number,
  month: number,
  year: number,
  index: number
): Promise<CollaboratorFinancialData> {
  try {
    // Obter dados financeiros atuais
    const financialData = await getCollaboratorFinancialData(clubId, collaboratorId);

    // Chave para o mês e ano
    const key = `${year}-${month.toString().padStart(2, "0")}`;

    // Verificar se existem entradas para o mês e ano especificados
    if (!financialData[key] || !financialData[key][index]) {
      throw new Error("Entrada financeira não encontrada.");
    }

    // Obter a entrada que será removida
    const entryToRemove = financialData[key][index];

    // Remover a entrada
    const entries = financialData[key].filter((_, i) => i !== index);

    // Atualizar dados financeiros
    const updatedData = {
      ...financialData,
      [key]: entries,
    };

    // Se não houver mais entradas para o mês, remover a chave
    if (entries.length === 0) {
      delete updatedData[key];
    }

    // Buscar transações financeiras relacionadas a esta entrada
    const transactionDate = new Date(year, month - 1, 1).toISOString().split('T')[0];
    const { data: transactions, error: fetchError } = await supabase
      .from("financial_transactions")
      .select("id")
      .eq("club_id", clubId)
      .eq("collaborator_id", collaboratorId)
      .eq("date", transactionDate)
      .eq("amount", Math.abs(entryToRemove.amount))
      .eq("type", entryToRemove.amount < 0 ? "despesa" : "receita");

    if (!fetchError && transactions && transactions.length > 0) {
      // Remover a transação financeira correspondente
      const { error: deleteError } = await supabase
        .from("financial_transactions")
        .delete()
        .eq("id", transactions[0].id);

      if (deleteError) {
        console.error("Erro ao remover transação financeira:", deleteError);
      }
    }

    // Salvar no banco de dados
    const { data, error } = await supabase
      .from("collaborators")
      .update({
        financial_data: updatedData,
      })
      .eq("club_id", clubId)
      .eq("id", collaboratorId)
      .select("financial_data")
      .single();

    if (error) {
      throw new Error(`Erro ao remover entrada financeira: ${error.message}`);
    }

    return data.financial_data;
  } catch (error: any) {
    console.error("Erro ao remover entrada financeira:", error);
    throw new Error(error.message || "Erro ao remover entrada financeira");
  }
}

/**
 * Calcula o saldo mensal de um colaborador
 * @param financialData Dados financeiros do colaborador
 * @param month Mês (1-12)
 * @param year Ano
 * @returns Saldo mensal
 */
export function calculateMonthlyBalance(
  financialData: CollaboratorFinancialData,
  month: number,
  year: number
): number {
  const key = `${year}-${month.toString().padStart(2, "0")}`;
  const entries = financialData[key] || [];
  return entries.reduce((total, entry) => total + entry.amount, 0);
}

/**
 * Calcula o saldo total de um colaborador
 * @param financialData Dados financeiros do colaborador
 * @returns Saldo total
 */
export function calculateTotalBalance(financialData: CollaboratorFinancialData): number {
  return Object.values(financialData).reduce(
    (total, entries) =>
      total + entries.reduce((monthTotal, entry) => monthTotal + entry.amount, 0),
    0
  );
}

/**
 * Obtém os dados financeiros de um colaborador para um ano específico
 * @param financialData Dados financeiros do colaborador
 * @param year Ano
 * @returns Dados financeiros do ano
 */
export function getYearlyFinancialData(
  financialData: CollaboratorFinancialData,
  year: number
): { [month: number]: CollaboratorFinancialEntry[] } {
  const yearlyData: { [month: number]: CollaboratorFinancialEntry[] } = {};

  // Inicializar todos os meses com arrays vazios
  for (let month = 1; month <= 12; month++) {
    yearlyData[month] = [];
  }

  // Preencher com os dados existentes
  Object.entries(financialData).forEach(([key, entries]) => {
    const [entryYear, entryMonth] = key.split("-").map(Number);
    if (entryYear === year) {
      yearlyData[entryMonth] = entries;
    }
  });

  return yearlyData;
}

/**
 * Obtém as informações bancárias de um colaborador
 * @param clubId ID do clube
 * @param collaboratorId ID do colaborador
 * @returns Informações bancárias do colaborador
 */
export async function getCollaboratorBankingInfo(
  clubId: number,
  collaboratorId: number
): Promise<CollaboratorBankingInfo> {
  try {
    const { data, error } = await supabase
      .from("collaborators")
      .select("bank_info")
      .eq("club_id", clubId)
      .eq("id", collaboratorId)
      .single();

    if (error) {
      throw new Error(`Erro ao obter informações bancárias: ${error.message}`);
    }

    return data.bank_info || {};
  } catch (error: any) {
    console.error("Erro ao obter informações bancárias:", error);
    throw new Error(error.message || "Erro ao obter informações bancárias");
  }
}

/**
 * Atualiza as informações bancárias de um colaborador
 * @param clubId ID do clube
 * @param collaboratorId ID do colaborador
 * @param bankingInfo Informações bancárias
 * @returns Informações bancárias atualizadas
 */
export async function updateCollaboratorBankingInfo(
  clubId: number,
  collaboratorId: number,
  bankingInfo: CollaboratorBankingInfo
): Promise<CollaboratorBankingInfo> {
  try {
    const { data, error } = await supabase
      .from("collaborators")
      .update({
        bank_info: bankingInfo,
      })
      .eq("club_id", clubId)
      .eq("id", collaboratorId)
      .select("bank_info")
      .single();

    if (error) {
      throw new Error(`Erro ao atualizar informações bancárias: ${error.message}`);
    }

    return data.bank_info;
  } catch (error: any) {
    console.error("Erro ao atualizar informações bancárias:", error);
    throw new Error(error.message || "Erro ao atualizar informações bancárias");
  }
}
