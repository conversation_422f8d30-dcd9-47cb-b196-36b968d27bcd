import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useState, useEffect } from "react";
import { useTrainingsStore } from "@/store/useTrainingsStore";
import { useStaffStore } from "@/store/useStaffStore";
import { useCategoriesStore } from "@/store/useCategoriesStore";
import { toast } from "@/hooks/use-toast";
import { Upload, X, Image as ImageIcon } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { Collaborator, getCollaborators } from "@/api/api";
import { addTrainingImage } from "@/api/trainings";

interface NovoTreinoDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
}

export function NovoTreinoDialog({ open, onOpenChange, clubId }: NovoTreinoDialogProps) {
  const [title, setTitle] = useState("");
  const [date, setDate] = useState("");
  const [location, setLocation] = useState("");
  const [type, setType] = useState("");
  const [startTime, setStartTime] = useState("");
  const [endTime, setEndTime] = useState("");
  const [status, setStatus] = useState("agendado");
  const [coach, setCoach] = useState("");
  const [description, setDescription] = useState("");
  const [requiredMaterials, setRequiredMaterials] = useState("");
  const [categoryId, setCategoryId] = useState<number | null>(null);
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [images, setImages] = useState<File[]>([]);
  const [imageUrls, setImageUrls] = useState<string[]>([]);
  const [uploadProgress, setUploadProgress] = useState<number>(0);

  // Stores
  const addTraining = useTrainingsStore(state => state.addTraining);
  const { staff, fetchStaff, loading: loadingStaff } = useStaffStore();
  const { categories, fetchCategories, loading: loadingCategories } = useCategoriesStore();

  // State for collaborators
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [loadingCollaborators, setLoadingCollaborators] = useState(false);

  useEffect(() => {
    if (open && clubId) {
      fetchStaff(clubId);
      fetchCategories(clubId);

      // Fetch collaborators
      const fetchCollaboratorsData = async () => {
        try {
          setLoadingCollaborators(true);
          const data = await getCollaborators(clubId);
          setCollaborators(data);
        } catch (error) {
          console.error("Error fetching collaborators:", error);
          toast({
            title: "Erro ao carregar colaboradores",
            variant: "destructive"
          });
        } finally {
          setLoadingCollaborators(false);
        }
      };

      fetchCollaboratorsData();
    }
  }, [open, clubId, fetchStaff, fetchCategories]);

  // Get all collaborators sorted alphabetically
  const sortedCollaborators = [...collaborators].sort((a, b) => {
    const nameA = a.full_name.toLowerCase();
    const nameB = b.full_name.toLowerCase();
    return nameA.localeCompare(nameB);
  });

  // Função para lidar com a seleção de imagens
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files);

      // Limitar a 5 imagens no total
      const totalImages = [...images, ...newFiles].slice(0, 5);
      setImages(totalImages);

      // Criar URLs para preview
      const newUrls = newFiles.map(file => URL.createObjectURL(file));
      setImageUrls(prev => [...prev, ...newUrls].slice(0, 5));
    }
  };

  // Função para remover uma imagem
  const removeImage = (index: number) => {
    const newImages = [...images];
    newImages.splice(index, 1);
    setImages(newImages);

    // Revogar URL do objeto para liberar memória
    URL.revokeObjectURL(imageUrls[index]);
    const newUrls = [...imageUrls];
    newUrls.splice(index, 1);
    setImageUrls(newUrls);
  };

  // Função para fazer upload de imagens
  const uploadImages = async (trainingId: number): Promise<void> => {
    if (images.length === 0) return;

    setUploadProgress(0);

    try {
      for (let i = 0; i < images.length; i++) {
        const file = images[i];
        const fileExt = file.name.split('.').pop();
        const fileName = `${Date.now()}-${i}.${fileExt}`;
        const filePath = `training-images/${clubId}/${trainingId}/${fileName}`;

        // Upload da imagem para o storage
        const { error: uploadError } = await supabase.storage
          .from('trainings')
          .upload(filePath, file);

        if (uploadError) {
          console.error('Erro ao fazer upload da imagem:', uploadError);
          toast({
            title: "Erro no upload",
            description: `Erro ao fazer upload da imagem: ${uploadError.message}`,
            variant: "destructive"
          });
          continue;
        }

        // Obter URL pública da imagem
        const { data: urlData } = supabase.storage
          .from('trainings')
          .getPublicUrl(filePath);

        if (urlData) {
          // Salvar referência da imagem no banco de dados usando a API
          try {
            await addTrainingImage(
              clubId,
              trainingId,
              urlData.publicUrl,
              i
            );
          } catch (dbError) {
            console.error('Erro ao salvar referência da imagem:', dbError);
            toast({
              title: "Erro no banco de dados",
              description: "Erro ao salvar referência da imagem no banco de dados.",
              variant: "destructive"
            });
          }
        }

        // Atualizar progresso
        setUploadProgress(Math.round(((i + 1) / images.length) * 100));
      }
    } catch (error) {
      console.error('Erro ao processar imagens:', error);
      toast({
        title: "Aviso",
        description: "Algumas imagens podem não ter sido carregadas corretamente.",
        variant: "destructive"
      });
    }
  };

  const handleSave = async () => {
    if (!title.trim()) {
      setError("O título do treino é obrigatório.");
      return;
    }
    if (!date) {
      setError("A data é obrigatória.");
      return;
    }
    if (!location.trim()) {
      setError("O local é obrigatório.");
      return;
    }
    if (!startTime || !endTime) {
      setError("Horário de início e fim são obrigatórios.");
      return;
    }
    setError("");
    setIsLoading(true);
    try {
      // Criar o treino
      const newTraining = await addTraining(clubId, {
        club_id: clubId,
        name: title,
        type: type.trim() || "tático",
        date,
        time: `${startTime}-${endTime}`,
        location,
        status: status as "agendado" | "em andamento" | "concluído",
        progress: status === "concluído" ? 100 : status === "em andamento" ? 50 : 0,
        coach,
        participants: 0,
        description,
        category_id: categoryId || undefined,
        required_materials: requiredMaterials
      });

      // Se houver imagens, fazer upload
      if (images.length > 0 && newTraining.id) {
        await uploadImages(newTraining.id);
      }

      toast({ title: "Treino criado com sucesso!", variant: "default" });

      // Limpar formulário
      setTitle("");
      setDate("");
      setLocation("");
      setType("");
      setStartTime("");
      setEndTime("");
      setStatus("agendado");
      setCoach("");
      setDescription("");
      setRequiredMaterials("");
      setCategoryId(null);
      setImages([]);
      setImageUrls([]);
      setUploadProgress(0);

      onOpenChange(false);
    } catch (e) {
      console.error("Erro detalhado ao salvar treino:", e);
      setError(`Erro ao salvar treino: ${e instanceof Error ? e.message : 'Erro desconhecido'}`);
      toast({
        title: "Erro ao salvar treino.",
        description: e instanceof Error ? e.message : 'Erro desconhecido',
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Novo Treino</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 max-h-[70vh] overflow-y-auto pr-2">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="title">Título do treino*</Label>
              <Input id="title" placeholder="Título do treino" value={title} onChange={e => setTitle(e.target.value)} />
            </div>
            <div>
              <Label htmlFor="date">Data*</Label>
              <Input id="date" type="date" value={date} onChange={e => setDate(e.target.value)} />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="location">Local*</Label>
              <Input id="location" placeholder="Local" value={location} onChange={e => setLocation(e.target.value)} />
            </div>
            <div>
              <Label htmlFor="type">Tipo de treino</Label>
              <Input
                id="type"
                list="training-types"
                placeholder="Digite ou selecione o tipo de treino"
                value={type}
                onChange={e => setType(e.target.value)}
              />
              <datalist id="training-types">
                <option value="tático" />
                <option value="técnico" />
                <option value="físico" />
                <option value="coletivo" />
                <option value="regenerativo" />
                <option value="outro" />
              </datalist>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="time">Horário*</Label>
              <div className="flex gap-2">
                <Input id="startTime" type="time" value={startTime} onChange={e => setStartTime(e.target.value)} placeholder="Início" />
                <Input id="endTime" type="time" value={endTime} onChange={e => setEndTime(e.target.value)} placeholder="Fim" />
              </div>
            </div>
            <div>
              <Label htmlFor="status">Status</Label>
              <select
                id="status"
                className="w-full border rounded p-2 text-sm"
                value={status}
                onChange={e => setStatus(e.target.value)}
              >
                <option value="agendado">Agendado</option>
                <option value="em andamento">Em andamento</option>
                <option value="concluído">Concluído</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="coach">Técnico responsável</Label>
              <select
                id="coach"
                className="w-full border rounded p-2 text-sm"
                value={coach}
                onChange={e => setCoach(e.target.value)}
              >
                <option value="">Selecione o técnico responsável</option>
                {loadingCollaborators ? (
                  <option disabled>Carregando colaboradores...</option>
                ) : (
                  sortedCollaborators.map(collaborator => (
                    <option key={collaborator.id} value={collaborator.full_name}>
                      {collaborator.full_name} {collaborator.role ? `(${collaborator.role})` : ''}
                    </option>
                  ))
                )}
              </select>
            </div>
            <div>
              <Label htmlFor="category">Categoria</Label>
              <select
                id="category"
                className="w-full border rounded p-2 text-sm"
                value={categoryId?.toString() || ""}
                onChange={e => setCategoryId(e.target.value ? parseInt(e.target.value) : null)}
              >
                <option value="">Selecione a categoria</option>
                {loadingCategories ? (
                  <option disabled>Carregando categorias...</option>
                ) : (
                  categories.map(c => (
                    <option key={c.id} value={c.id}>{c.name}</option>
                  ))
                )}
              </select>
              <p className="text-xs text-muted-foreground mt-1">
                Jogadores da categoria selecionada serão automaticamente associados ao treino.
              </p>
            </div>
          </div>

          <div>
            <Label htmlFor="description">Descrição</Label>
            <Textarea
              id="description"
              placeholder="Detalhes do treino"
              value={description}
              onChange={e => setDescription(e.target.value)}
              rows={5}
              className="resize-none"
            />
          </div>

          <div>
            <Label htmlFor="requiredMaterials">Materiais Necessários</Label>
            <Textarea
              id="requiredMaterials"
              placeholder="Liste os materiais necessários para o treino (ex: cones, bolas, coletes, etc.)"
              value={requiredMaterials}
              onChange={e => setRequiredMaterials(e.target.value)}
              rows={3}
              className="resize-none"
            />
          </div>

          <div>
            <Label>Imagens do treino (máximo 5)</Label>
            <div className="mt-2 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2">
              {/* Previews de imagens */}
              {imageUrls.map((url, index) => (
                <div key={index} className="relative border rounded-md overflow-hidden h-32">
                  <img
                    src={url}
                    alt={`Preview ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                  <button
                    type="button"
                    onClick={() => removeImage(index)}
                    className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              ))}

              {/* Botão de upload */}
              {imageUrls.length < 5 && (
                <label className="border border-dashed rounded-md flex flex-col items-center justify-center h-32 cursor-pointer hover:bg-gray-50">
                  <Upload className="h-8 w-8 text-gray-400 mb-2" />
                  <span className="text-sm text-gray-500">Adicionar imagem</span>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageChange}
                    className="hidden"
                  />
                </label>
              )}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Adicione até 5 imagens para ilustrar o treino.
            </p>
          </div>

          {uploadProgress > 0 && uploadProgress < 100 && (
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-blue-600 h-2.5 rounded-full"
                style={{ width: `${uploadProgress}%` }}
              ></div>
              <p className="text-xs text-muted-foreground mt-1">
                Enviando imagens: {uploadProgress}%
              </p>
            </div>
          )}

          {error && <div className="text-red-500 text-sm mt-1">{error}</div>}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancelar</Button>
          <Button onClick={handleSave} disabled={isLoading}>
            {isLoading ? <span className="loader mr-2" /> : null}
            Salvar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
