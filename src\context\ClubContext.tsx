import React, { createContext, useContext, ReactNode } from "react";

// O tipo do contexto: clubId pode ser number ou undefined
export const ClubContext = createContext<number | undefined>(undefined);

export function useCurrentClubId(): number {
  const ctx = useContext(ClubContext);
  if (ctx === undefined) {
    throw new Error("ClubContext not found. Certifique-se de envolver sua aplicação com <ClubContext.Provider />");
  }
  return ctx;
}

interface ClubProviderProps {
  clubId: number;
  children: ReactNode;
}

export function ClubProvider({ clubId, children }: ClubProviderProps) {
  return <ClubContext.Provider value={clubId}>{children}</ClubContext.Provider>;
}
