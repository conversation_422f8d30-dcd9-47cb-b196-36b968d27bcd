-- Criar tabela de profissionais médicos
CREATE TABLE IF NOT EXISTS medical_professionals (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  user_id UUID REFERENCES users(id),
  name TEXT NOT NULL,
  cpf TEXT,
  birth_date DATE,
  zip_code TEXT,
  state TEXT,
  city TEXT,
  address TEXT,
  credential TEXT,
  role TEXT NOT NULL, -- 'médico', 'fisioterapeuta', 'enfermeiro', 'massagista', 'outros'
  certificate_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Criar índices para melhorar a performance
CREATE INDEX IF NOT EXISTS idx_medical_professionals_club_id ON medical_professionals(club_id);
CREATE INDEX IF NOT EXISTS idx_medical_professionals_user_id ON medical_professionals(user_id);

-- Ativar Row Level Security (RLS)
ALTER TABLE medical_professionals ENABLE ROW LEVEL SECURITY;

-- Política para isolamento por clube
CREATE POLICY medical_professionals_club_isolation ON medical_professionals
    USING (club_id IN (
        SELECT club_id FROM club_members WHERE user_id = auth.uid()
    ));

-- Política para permitir que médicos vejam apenas seus próprios dados
CREATE POLICY medical_professionals_self_view ON medical_professionals
    FOR SELECT
    USING (
        user_id = auth.uid() OR
        auth.uid() IN (
            SELECT user_id FROM club_members 
            WHERE club_id = medical_professionals.club_id 
            AND role IN ('admin', 'technical')
        )
    );

-- Política para permitir que apenas admins e técnicos criem médicos
CREATE POLICY medical_professionals_insert ON medical_professionals
    FOR INSERT
    WITH CHECK (
        auth.uid() IN (
            SELECT user_id FROM club_members 
            WHERE club_id = medical_professionals.club_id 
            AND role IN ('admin', 'technical')
        )
    );

-- Política para permitir que médicos editem apenas seus próprios dados
CREATE POLICY medical_professionals_update ON medical_professionals
    FOR UPDATE
    USING (
        user_id = auth.uid() OR
        auth.uid() IN (
            SELECT user_id FROM club_members 
            WHERE club_id = medical_professionals.club_id 
            AND role IN ('admin', 'technical')
        )
    );

-- Política para permitir que apenas admins e técnicos excluam médicos
CREATE POLICY medical_professionals_delete ON medical_professionals
    FOR DELETE
    USING (
        auth.uid() IN (
            SELECT user_id FROM club_members 
            WHERE club_id = medical_professionals.club_id 
            AND role IN ('admin', 'technical')
        )
    );

-- Criar bucket para armazenar certificados médicos (se ainda não existir)
-- Nota: Isso precisa ser feito via interface do Supabase ou API de gerenciamento
