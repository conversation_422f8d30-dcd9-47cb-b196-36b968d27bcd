import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || "https://qoujacltecwxvymynbsh.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFvdWphY2x0ZWN3eHZ5bXluYnNoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ5NDAxNTYsImV4cCI6MjA2MDUxNjE1Nn0.YKsYHPtM7VaMNUge_bEt-RIszA_n8ZHBO0T3ahjyyeI";
const SITE_URL = import.meta.env.VITE_SITE_URL || "http://localhost:3000";

/**
 * Cria um cliente Supabase com o club_id configurado nos headers
 * @param clubId ID do clube a ser incluído nos headers
 * @returns Cliente Supabase configurado
 */
export function getSupabaseClientWithClubId(clubId: number) {
  console.log(`[DEBUG] Criando cliente Supabase com club_id: ${clubId}`);

  // Garantir que o clubId seja um número válido
  if (!clubId || isNaN(Number(clubId)) || Number(clubId) <= 0) {
    console.error(`[ERROR] club_id inválido: ${clubId}`);
    throw new Error(`Invalid club_id: ${clubId}`);
  }

  const clubIdStr = clubId.toString();
  console.log(`[DEBUG] club_id como string: ${clubIdStr}`);

  const client = createClient<Database>(
    SUPABASE_URL,
    SUPABASE_PUBLISHABLE_KEY,
    {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        flowType: 'pkce',
        redirectTo: SITE_URL
      },
      global: {
        headers: {
          'x-club-id': clubIdStr
        }
      }
    }
  );

  // Adicionar interceptor para verificar se o header está sendo enviado
  const originalFetch = client.rest.fetch;
  client.rest.fetch = async (url, options) => {
    console.log(`[DEBUG] Enviando requisição para ${url}`);
    console.log(`[DEBUG] Headers:`, options?.headers);
    return originalFetch(url, options);
  };

  return client;
}
