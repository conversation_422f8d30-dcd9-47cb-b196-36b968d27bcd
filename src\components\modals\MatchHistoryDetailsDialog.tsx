import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import type { MatchHistory } from "@/api/api";

interface MatchHistoryDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  match: MatchHistory | null;
}

export function MatchHistoryDetailsDialog({ open, onOpenChange, match }: MatchHistoryDetailsDialogProps) {
  if (!match) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Detalhes da Partida</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <div className="text-lg font-semibold mb-1">{match.opponent}</div>
            <div className="text-muted-foreground text-sm mb-2">{match.date} • {match.competition} • {match.location}</div>
            <div className="flex gap-2 items-center mb-2">
              <span className="font-bold text-xl">{match.score_home}</span>
              <span className="text-muted-foreground">x</span>
              <span className="font-bold text-xl">{match.score_away}</span>
              <span className="ml-2 text-base">({match.result === "win" ? "Vitória" : match.result === "draw" ? "Empate" : "Derrota"})</span>
            </div>
          </div>

          {/* Estatísticas */}
          {match.estatisticas && (
            <div>
              <h4 className="font-semibold mb-1">Estatísticas</h4>
              <ul className="grid grid-cols-2 gap-2 text-sm">
                <li>Posse: {match.estatisticas.posse}%</li>
                <li>Chutes: {match.estatisticas.chutes}</li>
                <li>Chutes no gol: {match.estatisticas.chutesNoGol}</li>
                <li>Escanteios: {match.estatisticas.escanteios}</li>
                <li>Faltas: {match.estatisticas.faltas}</li>
                <li>Impedimentos: {match.estatisticas.impedimentos}</li>
              </ul>
            </div>
          )}

          {/* Estatísticas avançadas do banco */}
          <div className="mt-2">
            <h4 className="font-semibold mb-1">Estatísticas Avançadas (Banco)</h4>
            <ul className="grid grid-cols-2 gap-2 text-sm">
              <li>Finalizações: {match.shots ?? 0}</li>
              <li>Finalizações no gol: {match.shots_on_target ?? 0}</li>
              <li>Escanteios: {match.corners ?? 0}</li>
              <li>Faltas: {match.fouls ?? 0}</li>
              <li>Impedimentos: {match.offsides ?? 0}</li>
            </ul>
          </div>

          {/* Gols */}
          {match.gols && match.gols.length > 0 && (
            <div>
              <h4 className="font-semibold mb-1">Gols</h4>
              <ul className="list-disc pl-5">
                {match.gols.map((gol, idx) => (
                  <li key={idx}>
                    {gol.minuto !== undefined ? `${gol.minuto}' - ` : ''}{gol.jogador}
                    {gol.assist ? ` (assistência: ${gol.assist})` : ''}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Cartões */}
          {match.cartoes && match.cartoes.length > 0 && (
            <div>
              <h4 className="font-semibold mb-1">Cartões</h4>
              <ul className="list-disc pl-5">
                {match.cartoes.map((cartao, idx) => (
                  <li key={idx}>
                    {cartao.jogador} ({cartao.tipo})
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Escalação */}
          {match.escalacao && match.escalacao.length > 0 && (
            <div>
              <h4 className="font-semibold mb-1">Escalação</h4>
              <div className="flex flex-wrap gap-2">
                {match.escalacao.map((nome, idx) => (
                  <span key={idx} className="px-2 py-1 rounded bg-muted text-xs">{nome}</span>
                ))}
              </div>
            </div>
          )}

          {/* Resumo */}
          {match.summary && (
            <div>
              <h4 className="font-semibold mb-1">Resumo</h4>
              <div className="text-sm text-muted-foreground">{match.summary}</div>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button variant="ghost" onClick={() => onOpenChange(false)}>Fechar</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
