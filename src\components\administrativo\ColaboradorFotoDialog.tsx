import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";
import { Loader2, Upload, User } from "lucide-react";
import { Collaborator, updateCollaborator } from "@/api/api";
import { useUser } from "@/context/UserContext";
import { supabase } from "@/integrations/supabase/client";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface ColaboradorFotoDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  collaborator: Collaborator;
  onSuccess?: () => void;
}

export function ColaboradorFotoDialog({
  open,
  onOpenChange,
  clubId,
  collaborator,
  onSuccess
}: ColaboradorFotoDialogProps) {
  const { user } = useUser();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [image, setImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(collaborator.image || null);

  // Função para lidar com o upload de imagem
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImage(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Função para salvar a foto
  const handleSave = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!image && !imagePreview) {
        throw new Error("Selecione uma imagem para o colaborador");
      }

      if (!user) {
        throw new Error("Usuário não autenticado");
      }

      let imageUrl = collaborator.image;

      // Upload da nova imagem, se houver
      if (image) {
        const { data: imageData, error: imageError } = await supabase.storage
          .from('profileimages')
          .upload(`collaborators/${clubId}/${Date.now()}_profile`, image, {
            upsert: true
          });

        if (imageError) {
          throw new Error(`Erro ao fazer upload da imagem: ${imageError.message}`);
        }

        const { data: urlData } = supabase.storage
          .from('profileimages')
          .getPublicUrl(imageData.path);

        imageUrl = urlData.publicUrl;
      }

      // Atualizar o colaborador com a nova imagem
      await updateCollaborator(
        clubId,
        user.id,
        collaborator.id,
        {
          image: imageUrl
        }
      );

      toast({
        title: "Sucesso",
        description: "Foto do colaborador atualizada com sucesso",
      });

      // Fechar o diálogo
      onOpenChange(false);

      // Chamar callback de sucesso
      if (onSuccess) {
        onSuccess();
      }
    } catch (err: any) {
      console.error("Erro ao atualizar foto do colaborador:", err);
      setError(err.message || "Erro ao atualizar foto do colaborador");
      toast({
        title: "Erro",
        description: err.message || "Erro ao atualizar foto do colaborador",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Atualizar Foto do Colaborador</DialogTitle>
        </DialogHeader>

        {error && (
          <div className="bg-red-50 text-red-700 p-3 rounded-md text-sm mb-4">
            {error}
          </div>
        )}

        <div className="flex flex-col items-center gap-4 py-4">
          <div className="relative w-32 h-32 rounded-full overflow-hidden border-2 border-gray-200">
            {imagePreview ? (
              <img src={imagePreview} alt="Preview" className="w-full h-full object-cover" />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gray-100">
                <User className="h-16 w-16 text-gray-400" />
              </div>
            )}
            <label htmlFor="profile-image" className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity cursor-pointer">
              <Upload className="h-8 w-8 text-white" />
              <input
                type="file"
                id="profile-image"
                className="sr-only"
                accept="image/*"
                onChange={handleImageChange}
              />
            </label>
          </div>
          <p className="text-sm text-muted-foreground">
            Clique na imagem para selecionar uma nova foto
          </p>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Salvar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
