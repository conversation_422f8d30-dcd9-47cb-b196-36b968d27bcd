# Game Day Nexus Platform

Bem-vindo à documentação oficial do projeto!

Este SaaS foi desenvolvido para gestão esportiva de clubes, oferecendo módulos completos para elenco, partidas, treinamentos, finanças, saúde e muito mais. O sistema é multi-clube, multi-usuário e totalmente integrado ao Supabase.

---

## 📚 Sumário

1. [Documentação da API](#api)
2. [Documentação do Banco de Dados](#banco-de-dados)
3. [Documentação das Funcionalidades](#funcionalidades)

---

## <a name="api"></a>1. Documentação da API

- Endpoints principais (CRUD de jogadores, partidas, treinos, objetivos, etc)
- Autenticação e autorização
- Exemplos de requests e responses
- Fluxos importantes (cadastro, atualização, finalização de partidas, etc)
- Tratamento de erros

[Ver documentação completa da API →](docs/api.md)

---

## <a name="banco-de-dados"></a>2. Documentação do Banco de Dados

- Diagrama de entidades e relações
- Descrição das tabelas principais (players, matches, gols, seasons, training_goals, etc)
- Campos, tipos, constraints e regras de negócio
- Migrações e versionamento

[Ver documentação completa do Banco de Dados →](docs/database.md)

---

## <a name="funcionalidades"></a>3. Documentação das Funcionalidades do SaaS

- Visão geral do produto
- Funcionalidades por módulo (Elenco, Partidas, Treinamentos, Financeiro, Médico, Staff, etc)
- Fluxos do usuário (passo a passo das principais jornadas)
- Permissões, multi-clube e diferenciais

[Ver documentação completa das Funcionalidades →](docs/features.md)

---

## Como contribuir

1. Leia a documentação de cada área antes de propor mudanças.
2. Siga os padrões de código, nomenclatura e estrutura definidos.
3. Atualize a documentação sempre que alterar endpoints, tabelas ou fluxos importantes.

---

> Para dúvidas ou sugestões, abra uma issue ou entre em contato com o time responsável.
