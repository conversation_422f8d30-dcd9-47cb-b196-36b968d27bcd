import { useState, useEffect, useRef } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format, parseISO } from "date-fns";
import { ptBR } from "date-fns/locale";
import { CalendarIcon, FileText, Upload, Download, Trash2, Check } from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { useMedicalExamsStore } from "@/store/useMedicalExamsStore";
import { MedicalExamForm } from "./MedicalExamForm";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { usePermission } from "@/hooks/usePermission";
import { Label } from "@/components/ui/label";

interface MedicalExamListProps {
  recordId: number;
  playerId: string;
  title?: string;
  description?: string;
  showAddButton?: boolean;
}

export function MedicalExamList({
  recordId,
  playerId,
  title = "Exames Médicos",
  description = "Acompanhe os exames médicos solicitados",
  showAddButton = true,
}: MedicalExamListProps) {
  const { toast } = useToast();
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { role, can } = usePermission();
  const {
    exams,
    loading,
    error,
    fetchExams,
    updateExam,
    deleteExam,
    uploadExamFile,
  } = useMedicalExamsStore();

  // State for dialogs
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [examToDelete, setExamToDelete] = useState<number | null>(null);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [examToUpload, setExamToUpload] = useState<number | null>(null);
  const [resultDialogOpen, setResultDialogOpen] = useState(false);
  const [examToAddResult, setExamToAddResult] = useState<number | null>(null);

  // State for form inputs
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [examResult, setExamResult] = useState("");
  const [examDate, setExamDate] = useState<Date | undefined>(new Date());

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load exams when component mounts
  useEffect(() => {
    if (clubId && recordId) {
      loadExams();
    }
  }, [clubId, recordId]);

  // Load exams
  const loadExams = async () => {
    try {
      await fetchExams(clubId, recordId);
    } catch (error) {
      console.error("Erro ao carregar exames:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar os exames médicos",
        variant: "destructive",
      });
    }
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
    }
  };

  // Handle file upload
  const handleUploadFile = async () => {
    if (!examToUpload || !selectedFile) return;

    try {
      await uploadExamFile(clubId, user?.id || "", examToUpload, selectedFile);
      toast({
        title: "Arquivo enviado",
        description: "O arquivo do exame foi enviado com sucesso",
      });
      setUploadDialogOpen(false);
      setExamToUpload(null);
      setSelectedFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
      loadExams();
    } catch (error) {
      console.error("Erro ao enviar arquivo:", error);
      toast({
        title: "Erro",
        description: "Não foi possível enviar o arquivo do exame",
        variant: "destructive",
      });
    }
  };

  // Handle adding exam result
  const handleAddResult = async () => {
    if (!examToAddResult || !examDate) return;

    try {
      const formattedExamDate = format(examDate, "yyyy-MM-dd");

      await updateExam(clubId, user?.id || "", examToAddResult, {
        exam_date: formattedExamDate,
        result: examResult,
        status: "Realizado",
      });

      toast({
        title: "Resultado adicionado",
        description: "O resultado do exame foi adicionado com sucesso",
      });

      setResultDialogOpen(false);
      setExamToAddResult(null);
      setExamResult("");
      setExamDate(new Date());
      loadExams();
    } catch (error) {
      console.error("Erro ao adicionar resultado:", error);
      toast({
        title: "Erro",
        description: "Não foi possível adicionar o resultado do exame",
        variant: "destructive",
      });
    }
  };

  // Handle exam deletion
  const handleDeleteExam = async () => {
    if (!examToDelete) return;

    try {
      await deleteExam(clubId, user?.id || "", examToDelete);
      toast({
        title: "Exame excluído",
        description: "O exame foi excluído com sucesso",
      });
      setDeleteDialogOpen(false);
      setExamToDelete(null);
      loadExams();
    } catch (error) {
      console.error("Erro ao excluir exame:", error);
      toast({
        title: "Erro",
        description: "Não foi possível excluir o exame",
        variant: "destructive",
      });
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Solicitado":
        return <Badge variant="outline">Solicitado</Badge>;
      case "Agendado":
        return <Badge className="bg-blue-500">Agendado</Badge>;
      case "Realizado":
        return <Badge className="bg-green-500">Realizado</Badge>;
      case "Cancelado":
        return <Badge variant="destructive">Cancelado</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>{title}</CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          {showAddButton && role === "medical" && (
            <MedicalExamForm recordId={recordId} playerId={playerId} onExamAdded={loadExams} />
          )}
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <p>Carregando exames...</p>
            </div>
          ) : error ? (
            <div className="flex justify-center py-8">
              <p className="text-red-500">{error}</p>
            </div>
          ) : exams.length === 0 ? (
            <div className="flex justify-center py-8">
              <p className="text-muted-foreground">Nenhum exame solicitado</p>
            </div>
          ) : (
            <div className="space-y-4">
              {exams.map((exam) => (
                <Card key={exam.id} className="overflow-hidden">
                  <CardHeader className="bg-muted p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{exam.exam_type}</span>
                        {getStatusBadge(exam.status)}
                      </div>
                      <div className="flex space-x-2">
                        {exam.file_url && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => window.open(exam.file_url, "_blank")}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        )}

                        {role === "medical" && exam.status !== "Realizado" && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setExamToUpload(exam.id);
                              setUploadDialogOpen(true);
                            }}
                          >
                            <Upload className="h-4 w-4" />
                          </Button>
                        )}

                        {role === "medical" && exam.status !== "Realizado" && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setExamToAddResult(exam.id);
                              setResultDialogOpen(true);
                            }}
                          >
                            <Check className="h-4 w-4" />
                          </Button>
                        )}

                        {role === "medical" && can("medical.exams.delete") && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-500 hover:text-red-700"
                            onClick={() => {
                              setExamToDelete(exam.id);
                              setDeleteDialogOpen(true);
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="space-y-2">
                      <div className="flex items-center space-x-4">
                        <div>
                          <p className="text-sm text-muted-foreground">Solicitado em:</p>
                          <p className="font-medium">
                            {format(parseISO(exam.request_date), "dd/MM/yyyy", { locale: ptBR })}
                          </p>
                        </div>

                        {exam.exam_date && (
                          <div>
                            <p className="text-sm text-muted-foreground">Realizado em:</p>
                            <p className="font-medium">
                              {format(parseISO(exam.exam_date), "dd/MM/yyyy", { locale: ptBR })}
                            </p>
                          </div>
                        )}
                      </div>

                      {exam.notes && (
                        <div>
                          <p className="text-sm text-muted-foreground">Observações:</p>
                          <p>{exam.notes}</p>
                        </div>
                      )}

                      {exam.result && (
                        <div>
                          <p className="text-sm text-muted-foreground">Resultado:</p>
                          <p>{exam.result}</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Excluir Exame</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Tem certeza que deseja excluir este exame? Esta ação não pode ser desfeita.</p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancelar
            </Button>
            <Button variant="destructive" onClick={handleDeleteExam}>
              Excluir
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Upload File Dialog */}
      <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Enviar Arquivo do Exame</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Label htmlFor="examFile">Selecione o arquivo do exame</Label>
            <Input
              id="examFile"
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              className="mt-2"
              accept=".pdf,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx,.dicom"
            />
            <p className="text-sm text-muted-foreground mt-2">
              Formatos aceitos: PDF, imagens, documentos do Office e DICOM
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setUploadDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleUploadFile} disabled={!selectedFile}>
              Enviar Arquivo
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Result Dialog */}
      <Dialog open={resultDialogOpen} onOpenChange={setResultDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Adicionar Resultado do Exame</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="examDate" className="text-right">
                Data do Exame*
              </Label>
              <div className="col-span-3">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !examDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {examDate ? format(examDate, "PPP", { locale: ptBR }) : <span>Selecione uma data</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={examDate}
                      onSelect={setExamDate}
                      initialFocus
                      locale={ptBR}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="examResult" className="text-right">
                Resultado
              </Label>
              <Textarea
                id="examResult"
                placeholder="Descreva o resultado do exame"
                value={examResult}
                onChange={(e) => setExamResult(e.target.value)}
                className="col-span-3"
                rows={4}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setResultDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleAddResult} disabled={!examDate}>
              Salvar Resultado
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
