import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PlayerSalary } from "@/integrations/supabase/types";
import { getPlayers } from "@/api";

interface SalaryDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (salary: Omit<PlayerSalary, 'id' | 'created_at'>) => void;
  initialData?: Partial<Omit<PlayerSalary, 'id' | 'created_at'>>;
  clubId: number;
}

export function SalaryDialog({ open, onOpenChange, onSave, initialData = {}, clubId }: SalaryDialogProps) {
  // Defensive: ensure initialData is always an object, never null
  const safeInitialData = initialData || {};
  const [playerId, setPlayerId] = useState(safeInitialData.player_id || "");
  const [value, setValue] = useState(safeInitialData.value || "");
  const [startDate, setStartDate] = useState(safeInitialData.start_date || "");
  const [endDate, setEndDate] = useState(safeInitialData.end_date || "");
  const [status, setStatus] = useState(safeInitialData.status || "Ativo");
  const [details, setDetails] = useState(safeInitialData.details || "");
  const [players, setPlayers] = useState<{ id: string; name: string }[]>([]);

  useEffect(() => {
    getPlayers(clubId).then((data) => {
      setPlayers(data.map((p: any) => ({ id: p.id, name: p.name })));
    });
  }, [clubId]);

  function handleSave() {
    if (!playerId || !value || !startDate) return;
    onSave({
      player_id: playerId,
      value: Number(value),
      start_date: startDate,
      end_date: endDate || null,
      status,
      details,
      club_id: clubId,
    });
    onOpenChange(false);
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{safeInitialData ? "Editar Salário" : "Novo Salário"}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium">Jogador</label>
            <select
              className="w-full border rounded px-2 py-1"
              value={playerId}
              onChange={e => setPlayerId(e.target.value)}
            >
              <option value="">Selecione um jogador</option>
              {players.map(p => (
                <option key={p.id} value={p.id}>{p.name}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium">Valor (R$)</label>
            <Input
              type="number"
              value={value}
              onChange={e => setValue(e.target.value)}
              min="0"
            />
          </div>
          <div>
            <label className="block text-sm font-medium">Data de Início</label>
            <Input
              type="date"
              value={startDate}
              onChange={e => setStartDate(e.target.value)}
            />
          </div>
          <div>
            <label className="block text-sm font-medium">Data de Fim</label>
            <Input
              type="date"
              value={endDate || ""}
              onChange={e => setEndDate(e.target.value)}
            />
          </div>
          <div>
            <label className="block text-sm font-medium">Status</label>
            <select
              className="w-full border rounded px-2 py-1"
              value={status}
              onChange={e => setStatus(e.target.value)}
            >
              <option value="Ativo">Ativo</option>
              <option value="Encerrado">Encerrado</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium">Detalhes</label>
            <Input
              value={details}
              onChange={e => setDetails(e.target.value)}
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSave} disabled={!playerId || !value || !startDate}>
            Salvar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
