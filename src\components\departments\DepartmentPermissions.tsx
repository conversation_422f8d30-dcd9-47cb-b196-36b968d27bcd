import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { getUserPermissions, updateUserPermissions } from "@/api/api";
import { Save } from "lucide-react";
import { PERMISSION_GROUPS, ROLES, ROLE_PERMISSIONS } from "@/constants/permissions";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

interface DepartmentPermissionsProps {
  departmentId: number;
  departmentName?: string;
}

export function DepartmentPermissions({
  departmentId,
  departmentName = "Departamento",
}: DepartmentPermissionsProps) {
  const clubId = useCurrentClubId();
  const [permissions, setPermissions] = useState<Record<string, boolean>>({});
  const [role, setRole] = useState<string>("staff");
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("permissions");

  // Carregar permissões do departamento
  useEffect(() => {
    const fetchDepartmentPermissions = async () => {
      try {
        setLoading(true);
        
        // Aqui você deve implementar uma função para obter as permissões do departamento
        // Por enquanto, vamos usar um objeto vazio
        const departmentPermissions = {};
        
        setPermissions(departmentPermissions);
        setRole("staff"); // Papel padrão
      } catch (err: any) {
        console.error("Erro ao carregar permissões do departamento:", err);
        toast({
          title: "Erro",
          description: "Não foi possível carregar as permissões do departamento",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDepartmentPermissions();
  }, [clubId, departmentId]);

  // Função para atualizar uma permissão
  const handlePermissionChange = (permission: string, value: boolean) => {
    setPermissions((prev) => ({
      ...prev,
      [permission]: value,
    }));
  };

  // Função para aplicar um papel predefinido
  const handleRoleChange = (newRole: string) => {
    setRole(newRole);
    
    // Aplicar permissões do papel selecionado
    if (ROLE_PERMISSIONS[newRole]) {
      setPermissions(ROLE_PERMISSIONS[newRole]);
    }
  };

  // Função para salvar as permissões
  const handleSave = async () => {
    try {
      setSaving(true);
      
      // Aqui você deve implementar uma função para atualizar as permissões do departamento
      // Por enquanto, apenas simulamos o sucesso
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "Sucesso",
        description: "Permissões do departamento atualizadas com sucesso",
      });
    } catch (err: any) {
      console.error("Erro ao salvar permissões do departamento:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao salvar permissões do departamento",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Permissões do Departamento: {departmentName}</CardTitle>
        <Button onClick={handleSave} disabled={saving}>
          <Save className="h-4 w-4 mr-1" />
          {saving ? "Salvando..." : "Salvar"}
        </Button>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="permissions">Permissões</TabsTrigger>
              <TabsTrigger value="roles">Papéis Predefinidos</TabsTrigger>
            </TabsList>
            
            <TabsContent value="permissions">
              <div className="space-y-6">
                <Accordion type="multiple" className="w-full">
                  {Object.entries(PERMISSION_GROUPS).map(([groupKey, group]) => (
                    <AccordionItem key={groupKey} value={groupKey}>
                      <AccordionTrigger>{group.label}</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4 p-2">
                          {Object.entries(group.permissions).map(([permission, label]) => (
                            <div key={permission} className="flex items-center justify-between">
                              <Label htmlFor={permission} className="flex-1">
                                {label}
                              </Label>
                              <Switch
                                id={permission}
                                checked={!!permissions[permission]}
                                onCheckedChange={(checked) => handlePermissionChange(permission, checked)}
                              />
                            </div>
                          ))}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
            </TabsContent>
            
            <TabsContent value="roles">
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="role">Papel Predefinido</Label>
                  <Select value={role} onValueChange={handleRoleChange}>
                    <SelectTrigger id="role">
                      <SelectValue placeholder="Selecione um papel" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(ROLES).map(([key, value]) => (
                        <SelectItem key={key} value={key}>
                          {value.label} - {value.description}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground mt-2">
                    Selecionar um papel predefinido substituirá todas as permissões atuais.
                  </p>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        )}
      </CardContent>
    </Card>
  );
}
