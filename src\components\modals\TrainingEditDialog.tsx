import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useState, useEffect } from "react";
import { useTrainingsStore } from "@/store/useTrainingsStore";
import { useStaffStore } from "@/store/useStaffStore";
import { useCategoriesStore } from "@/store/useCategoriesStore";
import { toast } from "@/hooks/use-toast";
import { Upload, X, Image as ImageIcon, Trash2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { Training, TrainingImage, getTrainingImages, addTrainingImage, deleteTrainingImage } from "@/api/trainings";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { Collaborator, getCollaborators } from "@/api/api";

interface TrainingEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  training: Training | null;
  clubId: number;
}

export function TrainingEditDialog({ open, onOpenChange, training, clubId }: TrainingEditDialogProps) {
  const [title, setTitle] = useState("");
  const [date, setDate] = useState("");
  const [location, setLocation] = useState("");
  const [type, setType] = useState("");
  const [startTime, setStartTime] = useState("");
  const [endTime, setEndTime] = useState("");
  const [status, setStatus] = useState("agendado");
  const [coach, setCoach] = useState("");
  const [description, setDescription] = useState("");
  const [requiredMaterials, setRequiredMaterials] = useState("");
  const [categoryId, setCategoryId] = useState<number | null>(null);
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [images, setImages] = useState<TrainingImage[]>([]);
  const [newImages, setNewImages] = useState<File[]>([]);
  const [newImageUrls, setNewImageUrls] = useState<string[]>([]);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // Stores
  const { updateTraining, deleteTraining, syncTrainings } = useTrainingsStore();
  const { staff, fetchStaff, loading: loadingStaff } = useStaffStore();
  const { categories, fetchCategories, loading: loadingCategories } = useCategoriesStore();

  // State for collaborators
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [loadingCollaborators, setLoadingCollaborators] = useState(false);

  // Carregar dados quando o modal for aberto
  useEffect(() => {
    if (open && training && clubId) {
      setTitle(training.name || "");
      setDate(training.date || "");
      setLocation(training.location || "");
      setType(training.type || "");

      // Prioritize separate start_time and end_time if they exist
      if (training.start_time && training.end_time) {
        setStartTime(training.start_time || "");
        setEndTime(training.end_time || "");
      } else if (training.time) { // Fallback for older data with combined time
        const timeParts = (training.time || "").split("-");
        setStartTime(timeParts[0] || "");
        setEndTime(timeParts[1] || "");
      } else {
        setStartTime("");
        setEndTime("");
      }

      setStatus(training.status || "agendado");
      setCoach(training.coach || "");
      setDescription(training.description || "");
      setRequiredMaterials(training.required_materials || "");
      setCategoryId(training.category_id || null);

      // Buscar imagens do treino
      fetchTrainingImages();

      // Buscar categorias e staff
      fetchStaff(clubId);
      fetchCategories(clubId);

      // Fetch collaborators
      const fetchCollaboratorsData = async () => {
        try {
          setLoadingCollaborators(true);
          const data = await getCollaborators(clubId);
          setCollaborators(data);
        } catch (error) {
          console.error("Error fetching collaborators:", error);
          toast({
            title: "Erro ao carregar colaboradores",
            variant: "destructive"
          });
        } finally {
          setLoadingCollaborators(false);
        }
      };

      fetchCollaboratorsData();
    }
  }, [open, training, clubId, fetchStaff, fetchCategories]);

  // Limpar estado ao fechar o modal
  useEffect(() => {
    if (!open) {
      setNewImages([]);
      setNewImageUrls([]);
      setUploadProgress(0);

      // Revogar URLs de objetos para liberar memória
      newImageUrls.forEach(url => URL.revokeObjectURL(url));
    }
  }, [open]);

  const fetchTrainingImages = async () => {
    if (!training?.id) return;

    try {
      const trainingImages = await getTrainingImages(training.id);
      setImages(trainingImages);
    } catch (error) {
      console.error("Erro ao buscar imagens do treino:", error);
    }
  };

  // Get all collaborators sorted alphabetically
  const sortedCollaborators = [...collaborators].sort((a, b) => {
    const nameA = a.full_name.toLowerCase();
    const nameB = b.full_name.toLowerCase();
    return nameA.localeCompare(nameB);
  });

  // Função para lidar com a seleção de novas imagens
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files);

      // Limitar a 5 imagens no total (existentes + novas)
      const totalImages = [...newImages, ...newFiles].slice(0, 5 - images.length);
      setNewImages(totalImages);

      // Criar URLs para preview
      const newUrls = newFiles.map(file => URL.createObjectURL(file));
      setNewImageUrls(prev => [...prev, ...newUrls].slice(0, 5 - images.length));
    }
  };

  // Função para remover uma nova imagem
  const removeNewImage = (index: number) => {
    const newImagesArray = [...newImages];
    newImagesArray.splice(index, 1);
    setNewImages(newImagesArray);

    // Revogar URL do objeto para liberar memória
    URL.revokeObjectURL(newImageUrls[index]);
    const newUrls = [...newImageUrls];
    newUrls.splice(index, 1);
    setNewImageUrls(newUrls);
  };

  // Função para remover uma imagem existente
  const removeExistingImage = async (imageId: number) => {
    try {
      await deleteTrainingImage(imageId);
      setImages(images.filter(img => img.id !== imageId));
      toast({ title: "Imagem removida com sucesso", variant: "default" });
    } catch (error) {
      console.error("Erro ao remover imagem:", error);
      toast({ title: "Erro ao remover imagem", variant: "destructive" });
    }
  };

  // Função para fazer upload de novas imagens
  const uploadNewImages = async (trainingId: number): Promise<void> => {
    if (newImages.length === 0) return;

    try {
      for (let i = 0; i < newImages.length; i++) {
        const file = newImages[i];
        const fileExt = file.name.split('.').pop();
        const fileName = `${Date.now()}-${i}.${fileExt}`;
        const filePath = `training-images/${clubId}/${trainingId}/${fileName}`;

        // Upload da imagem para o storage
        const { error: uploadError } = await supabase.storage
          .from('trainings')
          .upload(filePath, file);

        if (uploadError) {
          console.error('Erro ao fazer upload da imagem:', uploadError);
          toast({
            title: "Erro no upload",
            description: `Erro ao fazer upload da imagem: ${uploadError.message}`,
            variant: "destructive"
          });
          continue;
        }

        // Obter URL pública da imagem
        const { data: urlData } = supabase.storage
          .from('trainings')
          .getPublicUrl(filePath);

        if (urlData) {
          try {
            // Salvar referência da imagem no banco de dados usando a API
            await addTrainingImage(
              clubId,
              trainingId,
              urlData.publicUrl,
              images.length + i
            );
          } catch (dbError) {
            console.error('Erro ao salvar referência da imagem:', dbError);
            toast({
              title: "Erro no banco de dados",
              description: "Erro ao salvar referência da imagem no banco de dados.",
              variant: "destructive"
            });
          }
        }

        // Atualizar progresso
        setUploadProgress(Math.round(((i + 1) / newImages.length) * 100));
      }

      // Atualizar lista de imagens
      await fetchTrainingImages();
    } catch (error) {
      console.error('Erro ao processar imagens:', error);
      toast({
        title: "Aviso",
        description: "Algumas imagens podem não ter sido carregadas corretamente.",
        variant: "destructive"
      });
    }
  };

  const handleSave = async () => {
    if (!training) return;

    if (!title.trim()) {
      setError("O título do treino é obrigatório.");
      return;
    }
    if (!date) {
      setError("A data é obrigatória.");
      return;
    }
    if (!location.trim()) {
      setError("O local é obrigatório.");
      return;
    }
    if (!startTime || !endTime) {
      setError("Horário de início e fim são obrigatórios.");
      return;
    }

    setError("");
    setIsLoading(true);

    try {
      // Atualizar o treino
      await updateTraining(clubId, training.id, {
        name: title,
        type: type.trim() || "tático",
        date,
        start_time: startTime, // Added
        end_time: endTime,     // Added
        location,
        status: status as "agendado" | "em andamento" | "concluído",
        progress: status === "concluído" ? 100 : status === "em andamento" ? 50 : 0,
        coach,
        description,
        required_materials: requiredMaterials,
        category_id: categoryId
      });

      // Se houver novas imagens, fazer upload
      if (newImages.length > 0) {
        await uploadNewImages(training.id);
      }

      // Sincronizar treinos
      await syncTrainings(clubId);

      toast({ title: "Treino atualizado com sucesso!", variant: "default" });
      onOpenChange(false);
    } catch (e) {
      setError("Erro ao atualizar treino.");
      toast({ title: "Erro ao atualizar treino.", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!training) return;

    setIsLoading(true);
    try {
      await deleteTraining(clubId, training.id);
      await syncTrainings(clubId);
      toast({ title: "Treino excluído com sucesso!", variant: "default" });
      setDeleteDialogOpen(false);
      onOpenChange(false);
    } catch (e) {
      toast({ title: "Erro ao excluir treino.", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Editar Treino</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 max-h-[70vh] overflow-y-auto pr-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="title">Título do treino*</Label>
                <Input id="title" placeholder="Título do treino" value={title} onChange={e => setTitle(e.target.value)} />
              </div>
              <div>
                <Label htmlFor="date">Data*</Label>
                <Input id="date" type="date" value={date} onChange={e => setDate(e.target.value)} />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="location">Local*</Label>
                <Input id="location" placeholder="Local" value={location} onChange={e => setLocation(e.target.value)} />
              </div>
              <div>
                <Label htmlFor="type">Tipo de treino</Label>
                <Input
                  id="type"
                  list="training-types"
                  placeholder="Digite ou selecione o tipo de treino"
                  value={type}
                  onChange={e => setType(e.target.value)}
                />
                <datalist id="training-types">
                  <option value="tático" />
                  <option value="técnico" />
                  <option value="físico" />
                  <option value="coletivo" />
                  <option value="regenerativo" />
                  <option value="outro" />
                </datalist>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="time">Horário*</Label>
                <div className="flex gap-2">
                  <Input id="startTime" type="time" value={startTime} onChange={e => setStartTime(e.target.value)} placeholder="Início" />
                  <Input id="endTime" type="time" value={endTime} onChange={e => setEndTime(e.target.value)} placeholder="Fim" />
                </div>
              </div>
              <div>
                <Label htmlFor="status">Status</Label>
                <select
                  id="status"
                  className="w-full border rounded p-2 text-sm"
                  value={status}
                  onChange={e => setStatus(e.target.value)}
                >
                  <option value="agendado">Agendado</option>
                  <option value="em andamento">Em andamento</option>
                  <option value="concluído">Concluído</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="coach">Técnico responsável</Label>
                <select
                  id="coach"
                  className="w-full border rounded p-2 text-sm"
                  value={coach}
                  onChange={e => setCoach(e.target.value)}
                >
                  <option value="">Selecione o técnico responsável</option>
                  {loadingCollaborators ? (
                    <option disabled>Carregando colaboradores...</option>
                  ) : (
                    sortedCollaborators.map(collaborator => (
                      <option key={collaborator.id} value={collaborator.full_name}>
                        {collaborator.full_name} {collaborator.role ? `(${collaborator.role})` : ''}
                      </option>
                    ))
                  )}
                </select>
              </div>
              <div>
                <Label htmlFor="category">Categoria</Label>
                <select
                  id="category"
                  className="w-full border rounded p-2 text-sm"
                  value={categoryId?.toString() || ""}
                  onChange={e => setCategoryId(e.target.value ? parseInt(e.target.value) : null)}
                >
                  <option value="">Selecione a categoria</option>
                  {loadingCategories ? (
                    <option disabled>Carregando categorias...</option>
                  ) : (
                    categories.map(c => (
                      <option key={c.id} value={c.id}>{c.name}</option>
                    ))
                  )}
                </select>
                <p className="text-xs text-muted-foreground mt-1">
                  Alterar a categoria atualizará os jogadores associados ao treino.
                </p>
              </div>
            </div>

            <div>
              <Label htmlFor="description">Descrição</Label>
              <Textarea
                id="description"
                placeholder="Detalhes do treino"
                value={description}
                onChange={e => setDescription(e.target.value)}
                rows={5}
                className="resize-none"
              />
            </div>

            <div>
              <Label htmlFor="requiredMaterials">Materiais Necessários</Label>
              <Textarea
                id="requiredMaterials"
                placeholder="Liste os materiais necessários para o treino (ex: cones, bolas, coletes, etc.)"
                value={requiredMaterials}
                onChange={e => setRequiredMaterials(e.target.value)}
                rows={3}
                className="resize-none"
              />
            </div>

            <div>
              <Label>Imagens do treino (máximo 5)</Label>
              <div className="mt-2 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2">
                {/* Imagens existentes */}
                {images.map((image) => (
                  <div key={image.id} className="relative border rounded-md overflow-hidden h-32">
                    <img
                      src={image.image_url}
                      alt={`Imagem ${image.image_order + 1}`}
                      className="w-full h-full object-cover"
                    />
                    <button
                      type="button"
                      onClick={() => removeExistingImage(image.id)}
                      className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}

                {/* Previews de novas imagens */}
                {newImageUrls.map((url, index) => (
                  <div key={`new-${index}`} className="relative border rounded-md overflow-hidden h-32">
                    <img
                      src={url}
                      alt={`Nova imagem ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                    <button
                      type="button"
                      onClick={() => removeNewImage(index)}
                      className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}

                {/* Botão de upload */}
                {images.length + newImageUrls.length < 5 && (
                  <label className="border border-dashed rounded-md flex flex-col items-center justify-center h-32 cursor-pointer hover:bg-gray-50">
                    <Upload className="h-8 w-8 text-gray-400 mb-2" />
                    <span className="text-sm text-gray-500">Adicionar imagem</span>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleImageChange}
                      className="hidden"
                    />
                  </label>
                )}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Adicione até 5 imagens para ilustrar o treino.
              </p>
            </div>

            {uploadProgress > 0 && uploadProgress < 100 && (
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  className="bg-blue-600 h-2.5 rounded-full"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
                <p className="text-xs text-muted-foreground mt-1">
                  Enviando imagens: {uploadProgress}%
                </p>
              </div>
            )}

            {error && <div className="text-red-500 text-sm mt-1">{error}</div>}
          </div>
          <DialogFooter className="flex justify-between">
            <Button variant="destructive" onClick={() => setDeleteDialogOpen(true)}>
              <Trash2 className="h-4 w-4 mr-1" />
              Excluir
            </Button>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => onOpenChange(false)}>Cancelar</Button>
              <Button onClick={handleSave} disabled={isLoading}>
                {isLoading ? <span className="loader mr-2" /> : null}
                Salvar
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Excluir treino</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir este treino? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-500 hover:bg-red-600">
              {isLoading ? <span className="loader mr-2" /> : null}
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
