import { createContext, useContext, useEffect, useState, ReactNode } from "react";
import { supabase } from "@/api/supabaseClient";
import { ChangePasswordDialog } from "@/components/modals/ChangePasswordDialog";

export interface UserData {
  id: string;
  name: string;
  email: string;
  first_login?: boolean;
  profile_image?: string;
}

interface UserContextType {
  user: UserData | null;
  loading: boolean;
  refreshUser: (forceRefresh?: boolean) => Promise<void>;
  showChangePassword: boolean;
  setShowChangePassword: (show: boolean) => void;
}

const UserContext = createContext<UserContextType>({
  user: null,
  loading: true,
  refreshUser: async () => {},
  showChangePassword: false,
  setShowChangePassword: () => {},
});

export function UserProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);
  const [showChangePassword, setShowChangePassword] = useState(false);
  const [isFirstLogin, setIsFirstLogin] = useState(false);

  async function fetchUser(forceRefresh = false) {
    setLoading(true);
    const userId = localStorage.getItem("userId");

    if (!userId) {
      setUser(null);
      setLoading(false);
      return;
    }

    // Buscar dados do usuário - usar cache: false para garantir dados atualizados
    const timestamp = forceRefresh ? `?ts=${new Date().getTime()}` : '';

    const { data, error } = await supabase
      .from("users")
      .select("id, name, email, first_login, profile_image")
      .eq("id", userId)
      .single();

    if (error || !data) {
      setUser(null);
    } else {
      // Verificar se o profile_image está vazio e se há um valor no localStorage
      const cachedProfileImage = localStorage.getItem("userProfileImage");
      if (!data.profile_image && cachedProfileImage) {
        data.profile_image = cachedProfileImage;
      } else if (data.profile_image) {
        // Armazenar a imagem no localStorage para uso futuro
        localStorage.setItem("userProfileImage", data.profile_image);
      }

      setUser(data);

      // Verificar se é o primeiro login
      if (data.first_login) {
        setIsFirstLogin(true);
        setShowChangePassword(true);

        // Atualizar o flag de primeiro login para false
        await supabase
          .from("users")
          .update({ first_login: false })
          .eq("id", userId);
      }
    }

    setLoading(false);
  }

  // Função para lidar com o sucesso da alteração de senha
  const handlePasswordChangeSuccess = async () => {
    setIsFirstLogin(false);
    await fetchUser();
  };

  // Efeito para buscar o usuário quando o componente é montado
  // ou quando o userId muda no localStorage
  useEffect(() => {
    const checkUserAuth = () => {
      const userId = localStorage.getItem("userId");
      if (userId) {
        fetchUser();
      } else {
        setUser(null);
        setLoading(false);
      }
    };

    // Verificar autenticação inicialmente
    checkUserAuth();

    // Adicionar listener para mudanças no localStorage
    window.addEventListener('storage', (event) => {
      if (event.key === 'userId') {
        checkUserAuth();
      }
    });

    // Verificar a cada 2 segundos se o usuário está autenticado
    // Isso é um fallback para garantir que o contexto seja atualizado
    const interval = setInterval(checkUserAuth, 2000);

    return () => {
      clearInterval(interval);
    };
  }, []);

  return (
    <UserContext.Provider
      value={{
        user,
        loading,
        refreshUser: fetchUser,
        showChangePassword,
        setShowChangePassword
      }}
    >
      {children}

      {/* Diálogo de alteração de senha */}
      <ChangePasswordDialog
        open={showChangePassword}
        onOpenChange={setShowChangePassword}
        isFirstLogin={isFirstLogin}
        onSuccess={handlePasswordChangeSuccess}
      />
    </UserContext.Provider>
  );
}

export function useUser() {
  return useContext(UserContext);
}
