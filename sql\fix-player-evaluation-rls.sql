-- Drop existing policies
DROP POLICY IF EXISTS "Club members can view invitations" ON player_evaluation_invitations;
DROP POLICY IF EXISTS "Club members can insert invitations" ON player_evaluation_invitations;
DROP POLICY IF EXISTS "Club members can update invitations" ON player_evaluation_invitations;
DROP POLICY IF EXISTS "Club members can delete invitations" ON player_evaluation_invitations;

-- Create proper RLS policies with club_id check
CREATE POLICY "Club members can view invitations" 
ON player_evaluation_invitations
FOR SELECT 
USING (
  club_id = get_current_club_id()
);

CREATE POLICY "Club members can insert invitations" 
ON player_evaluation_invitations
FOR INSERT 
WITH CHECK (
  club_id = get_current_club_id()
);

CREATE POLICY "Club members can update invitations" 
ON player_evaluation_invitations
FOR UPDATE 
USING (
  club_id = get_current_club_id()
);

CREATE POLICY "Club members can delete invitations" 
ON player_evaluation_invitations
FOR DELETE 
USING (
  club_id = get_current_club_id()
);

-- Add comment to explain the policies
COMMENT ON TABLE player_evaluation_invitations IS 'Stores player evaluation invitations with proper RLS policies based on club_id';
