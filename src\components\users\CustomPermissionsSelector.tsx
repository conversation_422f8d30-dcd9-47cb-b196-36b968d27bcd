import { useState } from "react";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { PERMISSION_GROUPS, ROLES, ROLE_PERMISSIONS } from "@/constants/permissions";
import { Check, X } from "lucide-react";

interface CustomPermissionsSelectorProps {
  value: Record<string, boolean>;
  onChange: (permissions: Record<string, boolean>) => void;
  showRoleSelector?: boolean;
}

export function CustomPermissionsSelector({
  value,
  onChange,
  showRoleSelector = true,
}: CustomPermissionsSelectorProps) {
  const [permissions, setPermissions] = useState<Record<string, boolean>>(value || {});
  const [expandedGroups, setExpandedGroups] = useState<string[]>([]);

  // Função para aplicar um papel predefinido
  const applyRole = (role: string) => {
    const rolePermissions = ROLE_PERMISSIONS[role as keyof typeof ROLE_PERMISSIONS] || {};
    setPermissions(rolePermissions);
    onChange(rolePermissions);
  };

  // Função para alternar uma permissão
  const togglePermission = (permission: string) => {
    const newPermissions = {
      ...permissions,
      [permission]: !permissions[permission],
    };
    setPermissions(newPermissions);
    onChange(newPermissions);
  };

  // Função para alternar todas as permissões de um grupo
  const toggleGroupPermissions = (groupKey: string, groupPermissions: Record<string, string>, enabled: boolean) => {
    const newPermissions = { ...permissions };

    Object.keys(groupPermissions).forEach((permKey) => {
      newPermissions[permKey] = enabled;
    });

    setPermissions(newPermissions);
    onChange(newPermissions);
  };

  // Verificar se todas as permissões de um grupo estão habilitadas
  const areAllGroupPermissionsEnabled = (groupPermissions: Record<string, string>) => {
    return Object.keys(groupPermissions).every((permKey) => permissions[permKey]);
  };

  // Verificar se alguma permissão de um grupo está habilitada
  const areSomeGroupPermissionsEnabled = (groupPermissions: Record<string, string>) => {
    return Object.keys(groupPermissions).some((permKey) => permissions[permKey]);
  };

  // Alternar a expansão de um grupo
  const toggleGroupExpansion = (groupKey: string) => {
    setExpandedGroups((prev) =>
      prev.includes(groupKey)
        ? prev.filter(key => key !== groupKey)
        : [...prev, groupKey]
    );
  };

  return (
    <div className="space-y-4">
      {showRoleSelector && (
        <div className="flex flex-wrap gap-2 mb-4">
          {Object.entries(ROLES).map(([key, { label }]) => (
            <Button
              key={key}
              variant="outline"
              size="sm"
              onClick={() => applyRole(key)}
              className={`${
                JSON.stringify(permissions) === JSON.stringify(ROLE_PERMISSIONS[key as keyof typeof ROLE_PERMISSIONS])
                  ? "bg-primary text-primary-foreground"
                  : ""
              }`}
            >
              {label}
            </Button>
          ))}
        </div>
      )}

      <Accordion type="multiple" value={expandedGroups} className="w-full max-h-[50vh] overflow-y-auto pr-2">
        {Object.entries(PERMISSION_GROUPS).map(([groupKey, { label, permissions: groupPermissions }]) => {
          const allEnabled = areAllGroupPermissionsEnabled(groupPermissions);
          const someEnabled = areSomeGroupPermissionsEnabled(groupPermissions);

          return (
            <AccordionItem key={groupKey} value={groupKey}>
              <AccordionTrigger
                onClick={(e) => {
                  e.preventDefault();
                  toggleGroupExpansion(groupKey);
                }}
                className="hover:no-underline"
              >
                <div className="flex items-center justify-between w-full pr-4">
                  <span>{label}</span>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleGroupPermissions(groupKey, groupPermissions, true);
                      }}
                      className={`h-7 px-2 ${allEnabled ? 'bg-green-100 text-green-800 border-green-200' : ''}`}
                    >
                      <Check className="h-4 w-4 mr-1" />
                      Todos
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleGroupPermissions(groupKey, groupPermissions, false);
                      }}
                      className={`h-7 px-2 ${!someEnabled ? 'bg-red-100 text-red-800 border-red-200' : ''}`}
                    >
                      <X className="h-4 w-4 mr-1" />
                      Nenhum
                    </Button>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-2">
                  {Object.entries(groupPermissions).map(([permKey, permLabel]) => (
                    <div key={permKey} className="flex items-center justify-between">
                      <Label htmlFor={permKey} className="cursor-pointer">
                        {permLabel}
                      </Label>
                      <Switch
                        id={permKey}
                        checked={!!permissions[permKey]}
                        onCheckedChange={() => togglePermission(permKey)}
                      />
                    </div>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>
          );
        })}
      </Accordion>
    </div>
  );
}
