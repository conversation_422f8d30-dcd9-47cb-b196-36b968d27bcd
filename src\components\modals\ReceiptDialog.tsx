import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FinancialTransaction } from "@/api/api";
import { jsPDF } from "jspdf";
import { getClubInfo, getPlayerById } from "@/api";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";
import { Download } from "lucide-react";

interface ReceiptDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  transaction: FinancialTransaction | null;
  clubId: number;
}

export function ReceiptDialog({ open, onOpenChange, transaction, clubId }: ReceiptDialogProps) {
  const [loading, setLoading] = useState(false);
  const [clubInfo, setClubInfo] = useState<any>(null);

  useEffect(() => {
    if (open && clubId) {
      const fetchClubInfo = async () => {
        try {
          const info = await getClubInfo(clubId);
          setClubInfo(info);
        } catch (error) {
          console.error("Erro ao buscar informações do clube:", error);
        }
      };
      fetchClubInfo();
    }
  }, [open, clubId]);

  const generateReceipt = async () => {
    if (!transaction || !clubInfo) return;

    setLoading(true);
    try {
      // Create a new PDF document
      const doc = new jsPDF();

      // Format amount
      const formattedAmount = typeof transaction.amount === 'string'
        ? `R$ ${parseFloat(transaction.amount).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`
        : `R$ ${transaction.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;

      // Format date
      const formattedDate = transaction.date
        ? new Date(transaction.date).toLocaleDateString('pt-BR')
        : '-';

      // Check if this is a salary transaction
      const isSalaryTransaction = transaction.category === "salários";

      // Get person name and role
      let personName = "";
      let personRole = "";
      let paymentInfo = "";

      if (isSalaryTransaction) {
        // Try to get player or collaborator information
        if (transaction.player_id) {
          try {
            const playerData = await getPlayerById(clubId, transaction.player_id);
            personName = playerData.name;
            personRole = playerData.position || "Jogador";

            // Get payment information (PIX preferred, bank account as fallback)
            if (playerData.bank_pix_key) {
              paymentInfo = `PIX: ${playerData.bank_pix_key}`;
            } else if (playerData.bank_account_number && playerData.bank_branch) {
              paymentInfo = `Banco: ${playerData.bank_name || ""}, Agência: ${playerData.bank_branch}, Conta: ${playerData.bank_account_number}`;
            }
          } catch (error) {
            console.error("Erro ao buscar informações do jogador:", error);
          }
        } else if (transaction.collaborator_id) {
          try {
            // Get collaborator information
            const { data: collaboratorData, error } = await supabase
              .from("collaborators")
              .select("*")
              .eq("id", transaction.collaborator_id)
              .eq("club_id", clubId)
              .single();

            if (error) {
              throw error;
            }

            personName = collaboratorData.full_name;
            personRole = collaboratorData.role;

            // Get payment information from bank_info JSONB field
            if (collaboratorData.bank_info) {
              const bankInfo = collaboratorData.bank_info;
              if (bankInfo.pix) {
                paymentInfo = `PIX: ${bankInfo.pix}`;
              } else if (bankInfo.account_number && bankInfo.agency) {
                paymentInfo = `Banco: ${bankInfo.bank_name || ""}, Agência: ${bankInfo.agency}, Conta: ${bankInfo.account_number}`;
              }
            }
          } catch (error) {
            console.error("Erro ao buscar informações do colaborador:", error);
          }
        }
      }

      // Add club logo if available
      if (clubInfo.logo_url) {
        try {
          // Usar o logo do localStorage se disponível (mais seguro)
          const logoFromStorage = localStorage.getItem("teamLogo");
          const logoUrl = logoFromStorage || clubInfo.logo_url;

          if (logoUrl) {
            // Criar um canvas para manipular a imagem
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            img.crossOrigin = "Anonymous"; // Permitir carregamento cross-origin

            // Carregar a imagem
            await new Promise((resolve, reject) => {
              img.onload = resolve;
              img.onerror = reject;
              img.src = logoUrl;
            }).catch(() => {
              console.warn("Não foi possível carregar o logo, continuando sem ele");
            });

            if (img.complete && img.naturalHeight !== 0) {
              // Definir as dimensões do canvas
              canvas.width = img.width;
              canvas.height = img.height;

              // Desenhar a imagem no canvas
              if (ctx) {
                ctx.drawImage(img, 0, 0);

                // Converter o canvas para uma URL de dados
                const dataUrl = canvas.toDataURL('image/png');

                // Adicionar a imagem ao PDF com dimensões fixas
                doc.addImage(dataUrl, 'PNG', 10, 10, 30, 30);
              }
            }
          }
        } catch (e) {
          console.error("Erro ao adicionar logo:", e);
          // Continuar sem o logo
        }
      }

      // Add club name
      doc.setFontSize(18);
      doc.text(clubInfo.name || "Clube", 50, 20);

      if (isSalaryTransaction && personName) {
        // Create special salary receipt with two pages

        // ===== FIRST PAGE (CLUB COPY) =====

        // Add receipt title
        doc.setFontSize(16);
        doc.text("COMPROVANTE DE PAGAMENTO - VIA CLUBE", 105, 40, { align: "center" });

        // Add horizontal line
        doc.setLineWidth(0.5);
        doc.line(20, 45, 190, 45);

        // Add transaction details
        doc.setFontSize(12);

        // Add transaction information
        doc.text(`Número da Transação: ${transaction.id}`, 20, 60);
        doc.text(`Data: ${formattedDate}`, 20, 70);
        doc.text(`Descrição: ${transaction.description}`, 20, 80);
        doc.text(`Categoria: ${transaction.category}`, 20, 90);
        doc.text(`Valor: ${formattedAmount}`, 20, 100);
        doc.text(`Nome: ${personName}`, 20, 110);
        doc.text(`Função: ${personRole}`, 20, 120);

        if (paymentInfo) {
          doc.text(`Informação de Pagamento: ${paymentInfo}`, 20, 130);
        }

        // Add declaration text
        doc.setFontSize(11);
        doc.text(`Eu ${personName} recebi do clube ${clubInfo.name} o valor ${formattedAmount} referente ao salário.`, 20, 150);

        // Add signature line
        doc.setLineWidth(0.5);
        doc.line(20, 180, 120, 180);
        doc.setFontSize(10);
        doc.text("Assinatura do Funcionário", 70, 190, { align: "center" });

        // Add footer
        doc.setFontSize(10);
        doc.text(`Documento gerado em ${new Date().toLocaleDateString('pt-BR')} às ${new Date().toLocaleTimeString('pt-BR')}`, 105, 270, { align: "center" });
        doc.text(`${clubInfo.name || "Clube"} - CNPJ: ${clubInfo.cnpj || "N/A"}`, 105, 280, { align: "center" });

        // ===== SECOND PAGE (EMPLOYEE COPY) =====
        doc.addPage();

        // Add club logo again if available
        if (clubInfo.logo_url) {
          try {
            const logoFromStorage = localStorage.getItem("teamLogo");
            const logoUrl = logoFromStorage || clubInfo.logo_url;

            if (logoUrl) {
              const canvas = document.createElement('canvas');
              const ctx = canvas.getContext('2d');
              const img = new Image();
              img.crossOrigin = "Anonymous";

              await new Promise((resolve, reject) => {
                img.onload = resolve;
                img.onerror = reject;
                img.src = logoUrl;
              }).catch(() => {
                console.warn("Não foi possível carregar o logo, continuando sem ele");
              });

              if (img.complete && img.naturalHeight !== 0) {
                canvas.width = img.width;
                canvas.height = img.height;

                if (ctx) {
                  ctx.drawImage(img, 0, 0);
                  const dataUrl = canvas.toDataURL('image/png');
                  doc.addImage(dataUrl, 'PNG', 10, 10, 30, 30);
                }
              }
            }
          } catch (e) {
            console.error("Erro ao adicionar logo na segunda página:", e);
          }
        }

        // Add club name
        doc.setFontSize(18);
        doc.text(clubInfo.name || "Clube", 50, 20);

        // Add receipt title
        doc.setFontSize(16);
        doc.text("COMPROVANTE DE PAGAMENTO - VIA FUNCIONÁRIO", 105, 40, { align: "center" });

        // Add horizontal line
        doc.setLineWidth(0.5);
        doc.line(20, 45, 190, 45);

        // Add transaction details
        doc.setFontSize(12);

        // Add transaction information
        doc.text(`Número da Transação: ${transaction.id}`, 20, 60);
        doc.text(`Data: ${formattedDate}`, 20, 70);
        doc.text(`Descrição: ${transaction.description}`, 20, 80);
        doc.text(`Categoria: ${transaction.category}`, 20, 90);
        doc.text(`Valor: ${formattedAmount}`, 20, 100);
        doc.text(`Nome: ${personName}`, 20, 110);
        doc.text(`Função: ${personRole}`, 20, 120);

        if (paymentInfo) {
          doc.text(`Informação de Pagamento: ${paymentInfo}`, 20, 130);
        }

        // Add declaration text
        doc.setFontSize(11);
        doc.text(`Eu ${personName} recebi do clube ${clubInfo.name} o valor ${formattedAmount} referente ao salário.`, 20, 150);

        // Add signature line
        doc.setLineWidth(0.5);
        doc.line(20, 180, 120, 180);
        doc.setFontSize(10);
        doc.text("Assinatura do Funcionário", 70, 190, { align: "center" });

        // Add footer
        doc.setFontSize(10);
        doc.text(`Documento gerado em ${new Date().toLocaleDateString('pt-BR')} às ${new Date().toLocaleTimeString('pt-BR')}`, 105, 270, { align: "center" });
        doc.text(`${clubInfo.name || "Clube"} - CNPJ: ${clubInfo.cnpj || "N/A"}`, 105, 280, { align: "center" });
      } else {
        // Standard receipt for non-salary transactions

        // Add receipt title
        doc.setFontSize(16);
        doc.text("COMPROVANTE DE " + (transaction.type === "receita" ? "RECEITA" : "PAGAMENTO"), 105, 40, { align: "center" });

        // Add horizontal line
        doc.setLineWidth(0.5);
        doc.line(20, 45, 190, 45);

        // Add transaction details
        doc.setFontSize(12);

        // Add transaction information
        doc.text(`Número da Transação: ${transaction.id}`, 20, 60);
        doc.text(`Data: ${formattedDate}`, 20, 70);
        doc.text(`Descrição: ${transaction.description}`, 20, 80);
        doc.text(`Categoria: ${transaction.category}`, 20, 90);
        doc.text(`Tipo: ${transaction.type === "receita" ? "Receita" : "Despesa"}`, 20, 100);
        doc.text(`Valor: ${formattedAmount}`, 20, 110);

        if (transaction.player_name) {
          doc.text(`Jogador: ${transaction.player_name}`, 20, 120);
        }

        // Add footer
        doc.setFontSize(10);
        doc.text(`Documento gerado em ${new Date().toLocaleDateString('pt-BR')} às ${new Date().toLocaleTimeString('pt-BR')}`, 105, 270, { align: "center" });
        doc.text(`${clubInfo.name || "Clube"} - CNPJ: ${clubInfo.cnpj || "N/A"}`, 105, 280, { align: "center" });
      }

      // Save the PDF
      const fileName = `comprovante_${transaction.id}_${new Date().getTime()}.pdf`;
      doc.save(fileName);

      toast({
        title: "Sucesso",
        description: "Comprovante gerado com sucesso",
      });
    } catch (error) {
      console.error("Erro ao gerar comprovante:", error);
      toast({
        title: "Erro",
        description: "Não foi possível gerar o comprovante",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Gerar Comprovante</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <p>
            Você está prestes a gerar um comprovante para a transação{" "}
            <strong>{transaction?.description}</strong> no valor de{" "}
            <strong>
              {typeof transaction?.amount === 'string'
                ? `R$ ${parseFloat(transaction.amount).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`
                : transaction?.amount ? `R$ ${transaction.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}` : ""}
            </strong>.
          </p>
          <p>
            O comprovante será gerado em formato PDF e incluirá todas as informações relevantes da transação.
          </p>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={generateReceipt} disabled={loading}>
            <Download className="h-4 w-4 mr-2" />
            {loading ? "Gerando..." : "Gerar Comprovante"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
