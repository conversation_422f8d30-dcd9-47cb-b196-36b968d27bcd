
import React from 'react';
import { FormationSlots } from '@/data/formations';
import { Player } from '@/data/players';
import { Droppable, Draggable } from 'react-beautiful-dnd';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { MoreHorizontal, Loader2 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface FootballFieldProps {
  formation: { [key: string]: (Player | null)[] };
  formationSlots: FormationSlots;
  isLoading: boolean;
  theme: {
    primary: string;
    secondary: string;
    background: string;
    text: string;
  };
  onRemovePlayer: (position: string, index: number) => void;
}

const FootballField: React.FC<FootballFieldProps> = ({ 
  formation, 
  formationSlots, 
  isLoading, 
  theme,
  onRemovePlayer
}) => {
  // Mapeamento de posições para coordenadas no campo
  const getPositionCoordinates = (position: string, index: number, total: number): { top: string, left: string } => {
    const fieldWidth = 100; // Largura do campo em porcentagem
    const fieldHeight = 100; // Altura do campo em porcentagem
    
    // Posições base para cada tipo de jogador
    const basePositions: Record<string, any> = {
      goalkeeper: { top: 90, left: 50 },
      defender: { 
        baseTop: 70, 
        distribution: (i: number, t: number) => fieldWidth * (i + 1) / (t + 1) 
      },
      midfielder: { 
        baseTop: 45, 
        distribution: (i: number, t: number) => fieldWidth * (i + 1) / (t + 1) 
      },
      attacker: { 
        baseTop: 20, 
        distribution: (i: number, t: number) => fieldWidth * (i + 1) / (t + 1) 
      }
    };
    
    if (position === 'goalkeeper') {
      return { 
        top: `${basePositions[position].top}%`, 
        left: `${basePositions[position].left}%` 
      };
    }
    
    const pos = basePositions[position];
    return { 
      top: `${pos.baseTop}%`, 
      left: `${pos.distribution(index, total)}%` 
    };
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[400px]">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin" style={{ color: theme.primary }} />
          <p className="text-muted-foreground">Carregando campo...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full h-[500px] bg-green-600 rounded-lg overflow-hidden border-2 border-green-700">
      {/* Marcações do campo */}
      <div className="absolute top-1/2 left-0 right-0 h-[2px] bg-white/80 transform -translate-y-1/2" />
      <div className="absolute top-0 left-1/2 bottom-0 w-[2px] bg-white/80 transform -translate-x-1/2" />
      <div className="absolute top-[5%] left-1/2 w-[150px] h-[150px] border-2 border-white/80 rounded-full transform -translate-x-1/2" />
      
      {/* Área do gol (topo) */}
      <div className="absolute top-0 left-[30%] right-[30%] h-[10%] border-b-2 border-l-2 border-r-2 border-white/80" />
      
      {/* Área do gol (base) */}
      <div className="absolute bottom-0 left-[30%] right-[30%] h-[10%] border-t-2 border-l-2 border-r-2 border-white/80" />
      
      {/* Círculo central */}
      <div className="absolute top-1/2 left-1/2 w-[80px] h-[80px] border-2 border-white/80 rounded-full transform -translate-x-1/2 -translate-y-1/2" />
      
      {/* Marca do pênalti (topo) */}
      <div className="absolute top-[15%] left-1/2 w-[6px] h-[6px] bg-white/80 rounded-full transform -translate-x-1/2" />
      
      {/* Marca do pênalti (base) */}
      <div className="absolute bottom-[15%] left-1/2 w-[6px] h-[6px] bg-white/80 rounded-full transform -translate-x-1/2" />
      
      {/* Jogadores */}
      {Object.entries(formation).map(([position, players]) => (
        <React.Fragment key={position}>
          {players.map((player, index) => {
            const coords = getPositionCoordinates(position, index, players.length - 1);
            return (
              <Droppable key={`field-${position}-${index}`} droppableId={`field-${position}-${index}`}>
                {(provided, snapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    className="absolute w-[70px] h-[70px] transform -translate-x-1/2 -translate-y-1/2 flex items-center justify-center"
                    style={{ top: coords.top, left: coords.left }}
                  >
                    {player ? (
                      <Draggable draggableId={player.id.toString()} index={0}>
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className={`absolute w-[60px] h-[60px] rounded-full bg-white flex flex-col items-center justify-center shadow-md 
                              ${snapshot.isDragging ? 'opacity-70 ring-2' : ''}`}
                            style={{
                              ...provided.draggableProps.style,
                              backgroundColor: theme.secondary
                            }}
                          >
                            <Avatar className="h-12 w-12 border-2" style={{ borderColor: theme.primary }}>
                              <AvatarImage src={player.image || ""} />
                              <AvatarFallback className="text-white" style={{ backgroundColor: theme.primary }}>
                                {player.name.slice(0, 2).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <span className="text-xs font-bold mt-1" style={{ color: theme.primary }}>
                              {player.number}
                            </span>
                            <div className="absolute -top-1 -right-1">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm" className="h-5 w-5 p-0 rounded-full bg-white shadow">
                                    <MoreHorizontal className="h-3 w-3" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem onClick={() => onRemovePlayer(position, index)}>
                                    Remover
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>
                        )}
                      </Draggable>
                    ) : (
                      <div 
                        className="w-[40px] h-[40px] rounded-full border-2 border-dashed border-white/40 flex items-center justify-center"
                      >
                        <span className="text-white/60 text-xs font-medium">+</span>
                      </div>
                    )}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            );
          })}
        </React.Fragment>
      ))}
    </div>
  );
};

export default FootballField;
