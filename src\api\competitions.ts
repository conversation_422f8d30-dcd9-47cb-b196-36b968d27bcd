import { supabase } from "@/integrations/supabase/client";

// Tipos
export type Competition = {
  id: string;
  club_id: number;
  name: string;
  season_id: number | null;
  type: string | null;
  logo_url: string | null;
  start_date: string | null;
  end_date: string | null;
  created_at: string;
  updated_at: string;
};

// Funções para Competitions
export async function getCompetitions(clubId: number, seasonId?: number): Promise<Competition[]> {
  let query = supabase
    .from("competitions")
    .select("*")
    .eq("club_id", clubId);
  
  if (seasonId) {
    query = query.eq("season_id", seasonId);
  }
  
  const { data, error } = await query.order("name");

  if (error) {
    console.error("Erro ao buscar competições:", error);
    throw new Error(`Erro ao buscar competições: ${error.message}`);
  }

  return data as Competition[];
}

export async function getCompetitionById(clubId: number, id: string): Promise<Competition> {
  const { data, error } = await supabase
    .from("competitions")
    .select("*")
    .eq("club_id", clubId)
    .eq("id", id)
    .single();

  if (error) {
    console.error("Erro ao buscar competição:", error);
    throw new Error(`Erro ao buscar competição: ${error.message}`);
  }

  return data as Competition;
}

export async function createCompetition(clubId: number, competition: Omit<Competition, "id" | "club_id" | "created_at" | "updated_at">): Promise<Competition> {
  const { data, error } = await supabase
    .from("competitions")
    .insert({
      club_id: clubId,
      name: competition.name,
      season_id: competition.season_id,
      type: competition.type,
      logo_url: competition.logo_url,
      start_date: competition.start_date,
      end_date: competition.end_date,
    })
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar competição:", error);
    throw new Error(`Erro ao criar competição: ${error.message}`);
  }

  return data as Competition;
}

export async function updateCompetition(clubId: number, id: string, competition: Partial<Omit<Competition, "id" | "club_id" | "created_at" | "updated_at">>): Promise<Competition> {
  const { data, error } = await supabase
    .from("competitions")
    .update({
      name: competition.name,
      season_id: competition.season_id,
      type: competition.type,
      logo_url: competition.logo_url,
      start_date: competition.start_date,
      end_date: competition.end_date,
      updated_at: new Date().toISOString(),
    })
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error("Erro ao atualizar competição:", error);
    throw new Error(`Erro ao atualizar competição: ${error.message}`);
  }

  return data as Competition;
}

export async function deleteCompetition(clubId: number, id: string): Promise<boolean> {
  // Verificar se a competição está sendo usada em alguma partida
  const { data: matchesData, error: matchesError } = await supabase
    .from("matches")
    .select("id")
    .eq("competition_id", id)
    .limit(1);

  if (matchesError) {
    console.error("Erro ao verificar uso da competição:", matchesError);
    throw new Error(`Erro ao verificar uso da competição: ${matchesError.message}`);
  }

  if (matchesData && matchesData.length > 0) {
    throw new Error("Esta competição não pode ser excluída porque está sendo usada em partidas.");
  }

  const { error } = await supabase
    .from("competitions")
    .delete()
    .eq("club_id", clubId)
    .eq("id", id);

  if (error) {
    console.error("Erro ao deletar competição:", error);
    throw new Error(`Erro ao deletar competição: ${error.message}`);
  }

  return true;
}
