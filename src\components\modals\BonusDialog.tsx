import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { FinancialTransaction } from "@/api/api";
import { useFinancialStore } from "@/store/useFinancialStore";
import { toast } from "@/hooks/use-toast";
import { getPlayers } from "@/api";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface BonusDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  transaction?: FinancialTransaction | null;
  clubId: number;
}

export function BonusDialog({ open, onOpenChange, transaction, clubId }: BonusDialogProps) {
  const [amount, setAmount] = useState("");
  const [description, setDescription] = useState("");
  const [playerId, setPlayerId] = useState("");
  const [players, setPlayers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const { addTransaction } = useFinancialStore();

  // Fetch players when dialog opens
  useEffect(() => {
    if (open && clubId) {
      const fetchPlayers = async () => {
        try {
          const fetchedPlayers = await getPlayers(clubId);
          // Filter out inactive players
          const activePlayers = fetchedPlayers.filter(
            (player) => player.status !== "inativo" && player.status !== "emprestado"
          );
          setPlayers(activePlayers);
          
          // If transaction has a player_id, set it as selected
          if (transaction?.player_id) {
            setPlayerId(transaction.player_id);
          }
        } catch (error) {
          console.error("Erro ao buscar jogadores:", error);
        }
      };
      fetchPlayers();
    }
  }, [open, clubId, transaction]);

  // Set default description based on transaction
  useEffect(() => {
    if (transaction) {
      const playerName = transaction.player_name || "jogador";
      setDescription(`Bônus para ${playerName} - ${new Date().toLocaleDateString('pt-BR')}`);
    } else {
      setDescription(`Bônus - ${new Date().toLocaleDateString('pt-BR')}`);
    }
  }, [transaction]);

  const handleSave = async () => {
    if (!amount || !description) {
      toast({
        title: "Erro",
        description: "Preencha todos os campos obrigatórios",
        variant: "destructive",
      });
      return;
    }

    // Convert amount to number
    const numericAmount = parseFloat(amount);

    if (isNaN(numericAmount) || numericAmount <= 0) {
      toast({
        title: "Erro",
        description: "O valor deve ser um número positivo",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      // Create a new transaction for the bonus
      await addTransaction(clubId, {
        description,
        amount: numericAmount,
        category: "bônus",
        type: "despesa",
        date: new Date().toISOString().split('T')[0],
        club_id: clubId,
        player_id: playerId || undefined
      });

      toast({
        title: "Sucesso",
        description: "Bônus registrado com sucesso",
      });
      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao registrar bônus:", error);
      toast({
        title: "Erro",
        description: "Não foi possível registrar o bônus",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Registrar Bônus</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="player">Jogador</Label>
            <Select value={playerId} onValueChange={setPlayerId}>
              <SelectTrigger id="player">
                <SelectValue placeholder="Selecione um jogador" />
              </SelectTrigger>
              <SelectContent>
                {players.map((player) => (
                  <SelectItem key={player.id} value={player.id}>
                    {player.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="amount">Valor do Bônus (R$)</Label>
            <Input
              id="amount"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              placeholder="0,00"
              type="number"
              step="0.01"
            />
          </div>

          <div>
            <Label htmlFor="description">Descrição</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Descrição do bônus"
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSave} disabled={!amount || !description || loading}>
            {loading ? "Salvando..." : "Registrar Bônus"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
