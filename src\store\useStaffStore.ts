import { create } from "zustand";
import { Staff } from "@/api/api";
import { getStaff, createStaff, updateStaff, deleteStaff } from "@/api/api";

interface StaffState {
  staff: Staff[];
  loading: boolean;
  error: string | null;
  fetchStaff: (clubId: number) => Promise<void>;
  addStaff: (clubId: number, staff: Omit<Staff, "id">) => Promise<void>;
  updateStaff: (clubId: number, id: string | number, staff: Partial<Staff>) => Promise<void>;
  deleteStaff: (clubId: number, id: string | number) => Promise<void>;
}

export const useStaffStore = create<StaffState>((set, get) => ({
  staff: [],
  loading: false,
  error: null,
  fetchStaff: async (clubId: number) => {
    set({ loading: true, error: null });
    try {
      const staff = await getStaff(clubId);
      set({ staff, loading: false });
    } catch (err: unknown) {
      const error = err as Error;
      set({ error: error.message || "Erro ao buscar staff", loading: false });
    }
  },
  addStaff: async (clubId: number, staffData: Omit<Staff, "id">) => {
    set({ loading: true, error: null });
    try {
      const newStaff = await createStaff(clubId, staffData);
      set((state) => ({ staff: [...state.staff, newStaff], loading: false }));
    } catch (err: unknown) {
      const error = err as Error;
      set({ error: error.message || "Erro ao adicionar staff", loading: false });
    }
  },
  updateStaff: async (clubId: number, id: string | number, staffData: Partial<Staff>) => {
    set({ loading: true, error: null });
    try {
      const updated = await updateStaff(clubId, String(id), staffData);
      set((state) => ({
        staff: state.staff.map((s) => (s.id === String(id) ? { ...s, ...updated } : s)),
        loading: false,
      }));
    } catch (err: unknown) {
      const error = err as Error;
      set({ error: error.message || "Erro ao atualizar staff", loading: false });
    }
  },
  deleteStaff: async (clubId: number, id: string | number) => {
    set({ loading: true, error: null });
    try {
      await deleteStaff(clubId, String(id));
      set((state) => ({ staff: state.staff.filter((s) => s.id !== String(id)), loading: false }));
    } catch (err: unknown) {
      const error = err as Error;
      set({ error: error.message || "Erro ao remover staff", loading: false });
    }
  },
}));
