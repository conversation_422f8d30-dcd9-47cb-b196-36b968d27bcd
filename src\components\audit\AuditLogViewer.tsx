import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { toast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { getAuditLogs, getAuditLogActions, getAuditLogUsers, AuditLog } from "@/api/auditLogs";
import { Search, RefreshCw, FileDown, Check, X } from "lucide-react";
import { format } from "date-fns";

export function AuditLogViewer() {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [actions, setActions] = useState<string[]>([]);
  const [users, setUsers] = useState<{ id: string; name: string }[]>([]);
  
  // Filtros
  const [actionFilter, setActionFilter] = useState<string>("");
  const [userFilter, setUserFilter] = useState<string>("");
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  
  // Paginação
  const [limit] = useState(50);
  const [offset, setOffset] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  
  // Carregar logs de auditoria
  const fetchLogs = async (resetOffset = true) => {
    if (!user?.id) return;
    
    try {
      setLoading(true);
      
      const newOffset = resetOffset ? 0 : offset;
      if (resetOffset) {
        setOffset(0);
      }
      
      const filters: any = {};
      if (actionFilter) filters.action = actionFilter;
      if (userFilter) filters.user_id = userFilter;
      if (startDate) filters.start_date = startDate.toISOString();
      if (endDate) {
        // Definir o final do dia para a data final
        const endOfDay = new Date(endDate);
        endOfDay.setHours(23, 59, 59, 999);
        filters.end_date = endOfDay.toISOString();
      }
      
      const data = await getAuditLogs(clubId, user.id, filters, limit, newOffset);
      
      if (resetOffset) {
        setLogs(data);
      } else {
        setLogs([...logs, ...data]);
      }
      
      setHasMore(data.length === limit);
    } catch (err: any) {
      console.error("Erro ao carregar logs de auditoria:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao carregar logs de auditoria",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Carregar ações disponíveis para filtro
  const fetchActions = async () => {
    if (!user?.id) return;
    
    try {
      const data = await getAuditLogActions(clubId, user.id);
      setActions(data);
    } catch (err: any) {
      console.error("Erro ao carregar ações:", err);
    }
  };
  
  // Carregar usuários disponíveis para filtro
  const fetchUsers = async () => {
    if (!user?.id) return;
    
    try {
      const data = await getAuditLogUsers(clubId, user.id);
      setUsers(data);
    } catch (err: any) {
      console.error("Erro ao carregar usuários:", err);
    }
  };
  
  // Carregar dados iniciais
  useEffect(() => {
    if (clubId && user?.id) {
      fetchLogs();
      fetchActions();
      fetchUsers();
    }
  }, [clubId, user?.id]);
  
  // Função para carregar mais logs
  const handleLoadMore = () => {
    setOffset(offset + limit);
    fetchLogs(false);
  };
  
  // Função para aplicar filtros
  const handleApplyFilters = () => {
    fetchLogs();
  };
  
  // Função para limpar filtros
  const handleClearFilters = () => {
    setActionFilter("");
    setUserFilter("");
    setStartDate(undefined);
    setEndDate(undefined);
    fetchLogs();
  };
  
  // Função para exportar logs para CSV
  const handleExportCSV = () => {
    if (logs.length === 0) {
      toast({
        title: "Aviso",
        description: "Não há logs para exportar",
      });
      return;
    }
    
    // Criar cabeçalho do CSV
    const headers = ["ID", "Data", "Usuário", "Ação", "Status", "Detalhes"];
    
    // Criar linhas do CSV
    const rows = logs.map(log => [
      log.id,
      format(new Date(log.created_at), "dd/MM/yyyy HH:mm:ss"),
      log.user_name || log.user_id,
      log.action,
      log.success ? "Sucesso" : "Falha",
      JSON.stringify(log.details)
    ]);
    
    // Juntar cabeçalho e linhas
    const csvContent = [
      headers.join(","),
      ...rows.map(row => row.join(","))
    ].join("\n");
    
    // Criar blob e link para download
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", `audit-logs-${format(new Date(), "yyyy-MM-dd")}.csv`);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  // Função para formatar detalhes do log
  const formatDetails = (details: Record<string, any>) => {
    try {
      return JSON.stringify(details, null, 2);
    } catch (err) {
      return "Erro ao formatar detalhes";
    }
  };
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Logs de Auditoria</CardTitle>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => fetchLogs()} disabled={loading}>
            <RefreshCw className="h-4 w-4 mr-1" />
            Atualizar
          </Button>
          <Button variant="outline" onClick={handleExportCSV} disabled={logs.length === 0}>
            <FileDown className="h-4 w-4 mr-1" />
            Exportar CSV
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {/* Filtros */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div>
            <Select value={actionFilter} onValueChange={setActionFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filtrar por ação" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Todas as ações</SelectItem>
                {actions.map(action => (
                  <SelectItem key={action} value={action}>{action}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Select value={userFilter} onValueChange={setUserFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filtrar por usuário" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Todos os usuários</SelectItem>
                {users.map(user => (
                  <SelectItem key={user.id} value={user.id}>{user.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <DatePicker
              date={startDate}
              setDate={setStartDate}
              placeholder="Data inicial"
            />
          </div>
          
          <div>
            <DatePicker
              date={endDate}
              setDate={setEndDate}
              placeholder="Data final"
            />
          </div>
        </div>
        
        <div className="flex justify-end gap-2 mb-4">
          <Button variant="outline" onClick={handleClearFilters}>
            Limpar Filtros
          </Button>
          <Button onClick={handleApplyFilters}>
            <Search className="h-4 w-4 mr-1" />
            Aplicar Filtros
          </Button>
        </div>
        
        {/* Tabela de logs */}
        {loading && logs.length === 0 ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        ) : logs.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            Nenhum log de auditoria encontrado
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Data</TableHead>
                    <TableHead>Usuário</TableHead>
                    <TableHead>Ação</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Detalhes</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {logs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell className="whitespace-nowrap">
                        {format(new Date(log.created_at), "dd/MM/yyyy HH:mm:ss")}
                      </TableCell>
                      <TableCell>{log.user_name || log.user_id}</TableCell>
                      <TableCell>{log.action}</TableCell>
                      <TableCell>
                        {log.success ? (
                          <span className="flex items-center text-green-600">
                            <Check className="h-4 w-4 mr-1" />
                            Sucesso
                          </span>
                        ) : (
                          <span className="flex items-center text-red-600">
                            <X className="h-4 w-4 mr-1" />
                            Falha
                          </span>
                        )}
                      </TableCell>
                      <TableCell>
                        <pre className="text-xs overflow-x-auto max-w-xs">
                          {formatDetails(log.details)}
                        </pre>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            
            {hasMore && (
              <div className="flex justify-center mt-4">
                <Button variant="outline" onClick={handleLoadMore} disabled={loading}>
                  {loading ? "Carregando..." : "Carregar Mais"}
                </Button>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
