import { supabase } from "@/integrations/supabase/client";
import { withPermission, withAuditLog } from "./middleware";
import { PLAYER_PERMISSIONS } from "@/constants/permissions";

// Types
export type PlayerMatchStatistics = {
  id: number;
  club_id: number;
  match_id: string;
  player_id: string;
  minutes_played: number;
  goals: number;
  assists: number;
  shots: number;
  shots_on_target: number;
  passes: number;
  passes_completed: number;
  key_passes: number;
  tackles: number;
  interceptions: number;
  fouls_committed: number;
  fouls_suffered: number;
  yellow_cards: number;
  red_cards: number;
  created_at: string;
  updated_at: string;
};

/**
 * Get player statistics for a specific match
 * @param clubId Club ID
 * @param matchId Match ID
 * @param playerId Player ID (optional - if provided, returns stats for only this player)
 * @param userId User ID for permission check
 * @returns Player match statistics
 */
export async function getPlayerMatchStatistics(
  clubId: number,
  matchId: string,
  playerId?: string,
  userId?: string
): Promise<PlayerMatchStatistics[]> {
  // Build query
  let query = supabase
    .from("player_match_statistics")
    .select("*")
    .eq("club_id", clubId)
    .eq("match_id", matchId);

  // If player ID is provided, filter by player
  if (playerId) {
    query = query.eq("player_id", playerId);
  }

  // Execute query
  const { data, error } = await query;

  if (error) {
    throw new Error(`Error fetching player match statistics: ${error.message}`);
  }

  return data as PlayerMatchStatistics[];
}

/**
 * Save player statistics for a match
 * @param clubId Club ID
 * @param matchId Match ID
 * @param playerId Player ID
 * @param statistics Statistics data
 * @param userId User ID for permission check
 * @returns Saved player match statistics
 */
export async function savePlayerMatchStatistics(
  clubId: number,
  matchId: string,
  playerId: string,
  statistics: Partial<PlayerMatchStatistics>,
  userId: string
): Promise<PlayerMatchStatistics> {
  return withPermission(
    clubId,
    userId,
    PLAYER_PERMISSIONS.EDIT,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "player_statistics.save",
        { match_id: matchId, player_id: playerId },
        async () => {
          // Check if statistics already exist for this player and match
          const { data: existingStats } = await supabase
            .from("player_match_statistics")
            .select("*")
            .eq("club_id", clubId)
            .eq("match_id", matchId)
            .eq("player_id", playerId)
            .single();

          if (existingStats) {
            // Update existing statistics
            const { data, error } = await supabase
              .from("player_match_statistics")
              .update({
                ...statistics,
                updated_at: new Date().toISOString(),
              })
              .eq("club_id", clubId)
              .eq("match_id", matchId)
              .eq("player_id", playerId)
              .select()
              .single();

            if (error) {
              throw new Error(`Error updating player match statistics: ${error.message}`);
            }

            return data as PlayerMatchStatistics;
          } else {
            // Create new statistics
            const { data, error } = await supabase
              .from("player_match_statistics")
              .insert({
                club_id: clubId,
                match_id: matchId,
                player_id: playerId,
                ...statistics,
              })
              .select()
              .single();

            if (error) {
              throw new Error(`Error creating player match statistics: ${error.message}`);
            }

            return data as PlayerMatchStatistics;
          }
        }
      );
    }
  );
}

/**
 * Get aggregated player statistics across all matches
 * @param clubId Club ID
 * @param playerId Player ID
 * @param userId User ID for permission check
 * @returns Aggregated player statistics
 */
export async function getPlayerAggregatedStatistics(
  clubId: number,
  playerId: string,
  userId: string
): Promise<{
  total_matches: number;
  total_minutes: number;
  total_goals: number;
  total_assists: number;
  total_shots: number;
  total_shots_on_target: number;
  total_passes: number;
  total_passes_completed: number;
  total_key_passes: number;
  total_tackles: number;
  total_interceptions: number;
  total_yellow_cards: number;
  total_red_cards: number;
  pass_accuracy: number;
  shot_accuracy: number;
}> {
  // Get all statistics for this player
  const { data, error } = await supabase
    .from("player_match_statistics")
    .select("*")
    .eq("club_id", clubId)
    .eq("player_id", playerId);

  if (error) {
    throw new Error(`Error fetching player aggregated statistics: ${error.message}`);
  }

  const stats = data as PlayerMatchStatistics[];

  // Calculate aggregated statistics
  const total_matches = stats.length;
  const total_minutes = stats.reduce((sum, stat) => sum + (stat.minutes_played || 0), 0);
  const total_goals = stats.reduce((sum, stat) => sum + (stat.goals || 0), 0);
  const total_assists = stats.reduce((sum, stat) => sum + (stat.assists || 0), 0);
  const total_shots = stats.reduce((sum, stat) => sum + (stat.shots || 0), 0);
  const total_shots_on_target = stats.reduce((sum, stat) => sum + (stat.shots_on_target || 0), 0);
  const total_passes = stats.reduce((sum, stat) => sum + (stat.passes || 0), 0);
  const total_passes_completed = stats.reduce((sum, stat) => sum + (stat.passes_completed || 0), 0);
  const total_key_passes = stats.reduce((sum, stat) => sum + (stat.key_passes || 0), 0);
  const total_tackles = stats.reduce((sum, stat) => sum + (stat.tackles || 0), 0);
  const total_interceptions = stats.reduce((sum, stat) => sum + (stat.interceptions || 0), 0);
  const total_yellow_cards = stats.reduce((sum, stat) => sum + (stat.yellow_cards || 0), 0);
  const total_red_cards = stats.reduce((sum, stat) => sum + (stat.red_cards || 0), 0);

  // Calculate percentages
  const pass_accuracy = total_passes > 0 ? (total_passes_completed / total_passes) * 100 : 0;
  const shot_accuracy = total_shots > 0 ? (total_shots_on_target / total_shots) * 100 : 0;

  return {
    total_matches,
    total_minutes,
    total_goals,
    total_assists,
    total_shots,
    total_shots_on_target,
    total_passes,
    total_passes_completed,
    total_key_passes,
    total_tackles,
    total_interceptions,
    total_yellow_cards,
    total_red_cards,
    pass_accuracy,
    shot_accuracy,
  };
}

/**
 * Get player statistics with match details
 * @param clubId Club ID
 * @param playerId Player ID
 * @returns Player statistics with match details
 */
export async function getPlayerMatchesWithStats(
  clubId: number,
  playerId: string
): Promise<any[]> {
  // Get all statistics for this player
  const { data, error } = await supabase
    .from("player_match_statistics")
    .select(`
      *,
      matches:match_id (
        id,
        date,
        opponent,
        score_home,
        score_away,
        type
      )
    `)
    .eq("club_id", clubId)
    .eq("player_id", playerId);

  if (error) {
    throw new Error(`Error fetching player matches with statistics: ${error.message}`);
  }

  // Format the data for easier consumption
  return (data || []).map((item) => {
    const match = item.matches || {};
    return {
      ...item,
      match_id: item.match_id,
      match_date: match.date,
      opponent_name: match.opponent,
      score_home: match.score_home,
      score_away: match.score_away,
      match_type: match.type,
      // Remove the nested matches object
      matches: undefined
    };
  });
}