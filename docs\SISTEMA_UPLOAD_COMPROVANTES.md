# Sistema de Upload de Comprovantes - Game Day Nexus Platform

## Visão Geral

O Sistema de Upload de Comprovantes permite anexar comprovantes de pagamento às transações financeiras, proporcionando maior controle e rastreabilidade dos pagamentos realizados.

## Funcionalidades Implementadas

### 1. Upload de Comprovantes na Criação de Transações

#### Modal de Nova Transação (`NovaTransacaoDialog.tsx`):
- Campo de upload aparece automaticamente quando:
  - Tipo = "Despesa"
  - Status de Pagamento = "Pago"
- Formatos aceitos: PDF, JPG, JPEG, PNG
- Tamanho máximo: 5MB
- Upload automático após criação da transação

#### Fluxo de Criação:
1. Usuário preenche dados da transação
2. Seleciona status "Pago" para despesas
3. Campo de upload aparece
4. Usuário seleciona arquivo (opcional)
5. Transação é criada
6. Se h<PERSON> arquivo, upload é feito automaticamente
7. URL do comprovante é salva na transação

### 2. Upload de Comprovantes para Transações Existentes

#### Modal de Upload (`UploadTransactionReceiptDialog.tsx`):
- Acessível via menu dropdown das transações
- Disponível apenas para despesas pagas
- Exibe detalhes da transação
- Validação de arquivo (tipo e tamanho)
- Feedback visual durante upload

#### Acesso:
- Menu "⋮" na linha da transação
- Opção "Upload de Comprovante" (apenas para despesas pagas)

### 3. Visualização de Comprovantes

#### Modal de Detalhes (`TransactionDetailsDialog.tsx`):
- Exibe status de pagamento
- Botões para visualizar e baixar comprovante
- Disponível quando há comprovante anexado

#### Menu Dropdown:
- Opção "Baixar Comprovante" quando há arquivo anexado
- Link direto para visualização

### 4. Estrutura do Banco de Dados

#### Campo Adicionado:
```sql
-- Adicionar campo receipt_url à tabela financial_transactions
ALTER TABLE financial_transactions 
ADD COLUMN IF NOT EXISTS receipt_url TEXT;
```

#### Índice para Performance:
```sql
CREATE INDEX IF NOT EXISTS idx_financial_transactions_receipt_url 
ON financial_transactions(receipt_url) 
WHERE receipt_url IS NOT NULL;
```

### 5. Armazenamento de Arquivos

#### Bucket do Supabase:
- Bucket: `playerdocuments`
- Pasta: `financial_receipts/`
- Nomenclatura: `{clubId}_{transactionId}_{timestamp}.{extensao}`

#### Exemplo de Caminho:
```
financial_receipts/1_123_1703123456789.pdf
```

## Arquivos Modificados/Criados

### Novos Arquivos:
- `src/components/modals/UploadTransactionReceiptDialog.tsx`
- `sql/add_receipt_url_to_financial_transactions.sql`
- `docs/SISTEMA_UPLOAD_COMPROVANTES.md`

### Arquivos Modificados:
- `src/components/modals/NovaTransacaoDialog.tsx`
- `src/components/modals/TransactionDetailsDialog.tsx`
- `src/pages/Financeiro.tsx`
- `src/api/api.ts`
- `src/store/useFinancialStore.ts`

## Tipos TypeScript

### FinancialTransaction Atualizado:
```typescript
export type FinancialTransaction = {
  id: number;
  club_id: number;
  date: string;
  type: string;
  category: string;
  amount: number;
  description: string;
  player_id?: string;
  player_name?: string;
  collaborator_id?: number;
  collaborator_role?: string;
  medical_professional_id?: number;
  medical_professional_role?: string;
  payment_status?: string; // 'pending' or 'paid'
  receipt_url?: string; // URL do comprovante de pagamento
};
```

## Validações Implementadas

### Validação de Arquivo:
- Tipos permitidos: `.pdf`, `.jpg`, `.jpeg`, `.png`
- Tamanho máximo: 5MB
- Validação no frontend antes do upload

### Validação de Acesso:
- Upload apenas para despesas com status "pago"
- Verificação de permissões por clube
- Validação de existência da transação

## Segurança

### Row Level Security (RLS):
- Arquivos organizados por clube
- Acesso restrito aos membros do clube
- Validação de propriedade da transação

### Nomenclatura Segura:
- IDs únicos para evitar conflitos
- Timestamp para versionamento
- Extensão preservada para compatibilidade

## Próximos Passos

### Para Implementar:
1. **Executar Script SQL**: Adicionar campo `receipt_url` à tabela
2. **Regenerar Tipos**: Atualizar tipos TypeScript do Supabase
3. **Testar Funcionalidade**: Verificar upload e visualização
4. **Configurar Políticas**: Ajustar RLS se necessário

### Melhorias Futuras:
- Compressão automática de imagens
- Preview de arquivos no modal
- Histórico de comprovantes
- Notificações de upload
- Integração com sistema de aprovação

## Troubleshooting

### Problemas Comuns:
1. **Campo não existe**: Execute o script SQL
2. **Tipos incorretos**: Regenere tipos do Supabase
3. **Upload falha**: Verifique permissões do bucket
4. **Arquivo não aparece**: Verifique URL pública

### Logs Importantes:
- Console do navegador para erros de upload
- Logs do Supabase para problemas de storage
- Network tab para verificar requisições
