import { useState } from "react";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>eader, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>Footer 
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/use-toast";
import { createSupplier } from "@/api/api";
import { useUser } from "@/context/UserContext";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2 } from "lucide-react";
import { fetchAddressByCEP } from "@/lib/cep";

interface NovoFornecedorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  onSuccess?: () => void;
}

export function NovoFornecedorDialog({ 
  open, 
  onOpenChange, 
  clubId,
  onSuccess 
}: NovoFornecedorD<PERSON>ogProps) {
  const { user } = useUser();
  const [activeTab, setActiveTab] = useState("basic");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Campos básicos
  const [companyName, setCompanyName] = useState("");
  const [phone1, setPhone1] = useState("");
  const [phone2, setPhone2] = useState("");
  const [email, setEmail] = useState("");
  const [expirationDate, setExpirationDate] = useState("");
  
  // Campos de endereço
  const [zipCode, setZipCode] = useState("");
  const [state, setState] = useState("");
  const [city, setCity] = useState("");
  const [address, setAddress] = useState("");
  const [addressNumber, setAddressNumber] = useState("");
  
  // Campos bancários
  const [bankName, setBankName] = useState("");
  const [bankAgency, setBankAgency] = useState("");
  const [bankAccount, setBankAccount] = useState("");
  const [bankPix, setBankPix] = useState("");
  
  // Função para buscar endereço pelo CEP
  const handleZipCodeBlur = async () => {
    if (zipCode.length === 8 || zipCode.length === 9) {
      try {
        const addressData = await fetchAddressByCEP(zipCode);
        if (addressData) {
          setState(addressData.state);
          setCity(addressData.city);
          setAddress(addressData.street);
        }
      } catch (err) {
        console.error("Erro ao buscar CEP:", err);
      }
    }
  };

  // Função para salvar o fornecedor
  const handleSave = async () => {
    try {
      setLoading(true);
      setError(null);

      // Validar campos obrigatórios
      if (!companyName) {
        throw new Error("O nome da empresa é obrigatório");
      }

      // Criar o fornecedor
      await createSupplier(
        clubId,
        user?.id || "",
        {
          company_name: companyName,
          phone1: phone1 || undefined,
          phone2: phone2 || undefined,
          email: email || undefined,
          expiration_date: expirationDate || undefined,
          zip_code: zipCode || undefined,
          state: state || undefined,
          city: city || undefined,
          address: address || undefined,
          address_number: addressNumber || undefined,
          bank_name: bankName || undefined,
          bank_agency: bankAgency || undefined,
          bank_account: bankAccount || undefined,
          bank_pix: bankPix || undefined,
        }
      );

      toast({
        title: "Sucesso",
        description: "Fornecedor cadastrado com sucesso",
      });

      // Limpar campos
      setCompanyName("");
      setPhone1("");
      setPhone2("");
      setEmail("");
      setExpirationDate("");
      setZipCode("");
      setState("");
      setCity("");
      setAddress("");
      setAddressNumber("");
      setBankName("");
      setBankAgency("");
      setBankAccount("");
      setBankPix("");

      // Fechar o diálogo
      onOpenChange(false);

      // Chamar callback de sucesso
      if (onSuccess) {
        onSuccess();
      }
    } catch (err: any) {
      console.error("Erro ao cadastrar fornecedor:", err);
      setError(err.message || "Erro ao cadastrar fornecedor");
      toast({
        title: "Erro",
        description: err.message || "Erro ao cadastrar fornecedor",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Novo Fornecedor</DialogTitle>
        </DialogHeader>

        {error && (
          <div className="bg-red-50 text-red-700 p-3 rounded-md text-sm mb-4">
            {error}
          </div>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="basic">Informações Básicas</TabsTrigger>
            <TabsTrigger value="address">Endereço</TabsTrigger>
            <TabsTrigger value="bank">Dados Bancários</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="companyName">Nome da Empresa *</Label>
                <Input
                  id="companyName"
                  value={companyName}
                  onChange={(e) => setCompanyName(e.target.value)}
                  placeholder="Nome da empresa"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone1">Telefone Principal</Label>
                  <Input
                    id="phone1"
                    value={phone1}
                    onChange={(e) => setPhone1(e.target.value)}
                    placeholder="(00) 00000-0000"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone2">Telefone Secundário</Label>
                  <Input
                    id="phone2"
                    value={phone2}
                    onChange={(e) => setPhone2(e.target.value)}
                    placeholder="(00) 00000-0000"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email">E-mail</Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="expirationDate">Data de Vencimento</Label>
                  <Input
                    id="expirationDate"
                    type="date"
                    value={expirationDate}
                    onChange={(e) => setExpirationDate(e.target.value)}
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="address" className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="zipCode">CEP</Label>
                  <Input
                    id="zipCode"
                    value={zipCode}
                    onChange={(e) => setZipCode(e.target.value)}
                    onBlur={handleZipCodeBlur}
                    placeholder="00000-000"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="state">Estado</Label>
                  <Input
                    id="state"
                    value={state}
                    onChange={(e) => setState(e.target.value)}
                    placeholder="UF"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="city">Cidade</Label>
                <Input
                  id="city"
                  value={city}
                  onChange={(e) => setCity(e.target.value)}
                  placeholder="Cidade"
                />
              </div>

              <div className="grid grid-cols-4 gap-4">
                <div className="col-span-3 space-y-2">
                  <Label htmlFor="address">Endereço</Label>
                  <Input
                    id="address"
                    value={address}
                    onChange={(e) => setAddress(e.target.value)}
                    placeholder="Rua, Avenida, etc."
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="addressNumber">Número</Label>
                  <Input
                    id="addressNumber"
                    value={addressNumber}
                    onChange={(e) => setAddressNumber(e.target.value)}
                    placeholder="Nº"
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="bank" className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="bankName">Nome do Banco</Label>
                <Input
                  id="bankName"
                  value={bankName}
                  onChange={(e) => setBankName(e.target.value)}
                  placeholder="Nome do banco"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="bankAgency">Agência</Label>
                  <Input
                    id="bankAgency"
                    value={bankAgency}
                    onChange={(e) => setBankAgency(e.target.value)}
                    placeholder="Número da agência"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bankAccount">Conta</Label>
                  <Input
                    id="bankAccount"
                    value={bankAccount}
                    onChange={(e) => setBankAccount(e.target.value)}
                    placeholder="Número da conta"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="bankPix">Chave PIX</Label>
                <Input
                  id="bankPix"
                  value={bankPix}
                  onChange={(e) => setBankPix(e.target.value)}
                  placeholder="Chave PIX"
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Salvar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
