-- <PERSON>ript para atualizar as permissões de jogadores para permitir edição do próprio perfil
-- Este script garante que jogadores tenham as permissões necessárias para editar seu próprio perfil

-- 1. Atual<PERSON><PERSON> as permissões de todos os usuários com role 'player'
UPDATE club_members
SET permissions = '{
  "dashboard.view": true,
  "players.view_own": true,
  "players.edit_own": true
}'::jsonb
WHERE role = 'player';

-- 2. G<PERSON><PERSON><PERSON> que a tabela users tenha as permissões corretas para jogadores
UPDATE users
SET permissions = '{
  "dashboard.view": true,
  "players.view_own": true,
  "players.edit_own": true
}'::jsonb
WHERE role = 'player';

-- 3. Adicionar um log de auditoria para registrar esta operação
INSERT INTO audit_logs (club_id, user_id, action, details, success)
SELECT 
    cm.club_id,
    cm.user_id,
    'system.update_player_permissions',
    jsonb_build_object(
        'role', 'player',
        'permissions', '{
          "dashboard.view": true,
          "players.view_own": true,
          "players.edit_own": true
        }'::jsonb
    ),
    true
FROM club_members cm
WHERE cm.role = 'player';

-- Mensagem de conclusão
DO $$
BEGIN
    RAISE NOTICE 'Permissões de jogadores atualizadas com sucesso!';
END $$;
