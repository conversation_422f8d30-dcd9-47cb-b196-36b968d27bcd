import { create } from "zustand";
import { UpcomingMatch } from "../api/api";
import { getUpcomingMatches, deleteUpcomingMatch } from "../api/api";

interface GamesState {
  games: UpcomingMatch[];
  loading: boolean;
  error: string | null;
  fetchGames: (clubId: number, seasonId?: number) => Promise<void>;
  removeGame: (clubId: number, id: string) => Promise<void>;
  syncGames: (clubId: number, seasonId?: number) => Promise<void>;
}

export const useGamesStore = create<GamesState>((set) => ({
  games: [],
  loading: false,
  error: null,

  fetchGames: async (clubId: number, seasonId?: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const games = await getUpcomingMatches(clubId, seasonId ?? undefined);
      set({ games, loading: false });
    } catch (err: any) {
      set({ error: err.message || "Erro ao buscar jogos", loading: false });
    }
  },

  removeGame: async (clubId: number, id: string): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const ok = await deleteUpcomingMatch(clubId, id);
      if (ok) {
        set((state) => ({ games: state.games.filter(g => g.id !== id), loading: false }));
      } else {
        set({ error: "Jogo não encontrado", loading: false });
      }
    } catch (err: any) {
      set({ error: err.message || "Erro ao remover jogo", loading: false });
    }
  },

  // NOVAS FUNÇÕES DE SINCRONIZAÇÃO
  syncGames: async (clubId: number, seasonId?: number) => {
    const games = await getUpcomingMatches(clubId, seasonId ?? undefined);
    set({ games });
  },
}));
