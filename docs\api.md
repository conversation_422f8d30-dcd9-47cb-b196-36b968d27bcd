# Documentação da API

Esta seção detalha todos os endpoints/funções reais do sistema, baseados no arquivo src/api/api.ts.

## Sumário de Endpoints
- [Players](#players)
- [Youth Players](#youth-players)
- [Staff](#staff)
- [Trainings](#trainings)
- [Matches](#matches)
- [Match History](#match-history)
- [Agenda](#agenda)
- [Club Info](#club-info)
- [Tasks](#tasks)
- [Club Members](#club-members)
- [Medical Records](#medical-records)
- [Financeiro](#financeiro)
- [Sessions](#sessions)
- [Exercises](#exercises)
- [Training Goals](#training-goals)
- [Physical Progress](#physical-progress)
- [Training Exercises](#training-exercises)
- [Salaries/Contracts](#salaries-contracts)
- [Utilitários](#utilitarios)

---

## <a name="players"></a>Players

### Listar jogadores
- **Função:** `getPlayers(clubId)`
- **Descrição:** Retorna todos os jogadores do clube.
- **Parâmetros:**
  - `clubId` (number, obrigatório)
- **Exemplo de uso:**
```js
const players = await getPlayers(1);
```
- **Response:**
```json
[
  {
    "id": "uuid",
    "club_id": 1,
    "name": "João Silva",
    "position": "Atacante",
    "age": 25,
    "number": 9,
    "status": "disponivel",
    ...
  },
  ...
]
```

### Buscar jogador por ID
- **Função:** `getPlayerById(clubId, id)`
- **Descrição:** Retorna um jogador específico do clube.
- **Parâmetros:**
  - `clubId` (number, obrigatório)
  - `id` (string, obrigatório)

### Criar jogador
- **Função:** `createPlayer(clubId, player)`
- **Descrição:** Cria um novo jogador.
- **Parâmetros:**
  - `clubId` (number, obrigatório)
  - `player` (objeto Player, exceto id)
- **Exemplo de body:**
```json
{
  "name": "João Silva",
  "position": "Atacante",
  "age": 25,
  "number": 9,
  "status": "disponivel"
}
```

### Atualizar jogador
- **Função:** `updatePlayer(clubId, id, player)`
- **Descrição:** Atualiza dados de um jogador.
- **Parâmetros:**
  - `clubId` (number)
  - `id` (string)
  - `player` (objeto parcial)

### Deletar jogador
- **Função:** `deletePlayer(clubId, id)`
- **Descrição:** Remove um jogador do clube.
- **Parâmetros:**
  - `clubId` (number)
  - `id` (string)

---

## <a name="youth-players"></a>Youth Players

### Listar jogadores da base
- **Função:** `getYouthPlayers(clubId)`
- **Descrição:** Retorna todos os jogadores da base do clube.

### Criar jogador da base
- **Função:** `createYouthPlayer(clubId, player)`
- **Descrição:** Cria um novo jogador da base.

### Atualizar jogador da base
- **Função:** `updateYouthPlayer(clubId, id, player)`
- **Descrição:** Atualiza dados do jogador da base.

### Deletar jogador da base
- **Função:** `deleteYouthPlayer(clubId, id)`
- **Descrição:** Remove um jogador da base.

---

## <a name="staff"></a>Staff

### Listar staff
- **Função:** `getStaff(clubId)`
- **Descrição:** Retorna todos os membros do staff do clube.

### Criar staff
- **Função:** `createStaff(clubId, staff)`
- **Descrição:** Cria um novo membro do staff.

### Atualizar staff
- **Função:** `updateStaff(clubId, id, staff)`
- **Descrição:** Atualiza dados do staff.

### Deletar staff
- **Função:** `deleteStaff(clubId, id)`
- **Descrição:** Remove um membro do staff.

---

## <a name="trainings"></a>Trainings

### Listar treinos
- **Função:** `getTrainings(clubId)`
- **Descrição:** Retorna todos os treinos do clube.

### Criar treino
- **Função:** `createTraining(clubId, training)`
- **Descrição:** Cria um novo treino.

### Atualizar treino
- **Função:** `updateTraining(clubId, id, training)`
- **Descrição:** Atualiza dados do treino.

### Deletar treino
- **Função:** `deleteTraining(clubId, id)`
- **Descrição:** Remove um treino do clube.

### Finalizar treino
- **Função:** `finalizeTraining(clubId, id, summary, description)`
- **Descrição:** Marca um treino como finalizado, adicionando resumo e descrição.

---

## <a name="matches"></a>Matches

### Listar próximos jogos
- **Função:** `getUpcomingMatches(clubId, seasonId?)`
- **Descrição:** Retorna próximos jogos do clube, filtrando por temporada se informado.

### Criar próximo jogo
- **Função:** `createUpcomingMatch(clubId, match)`
- **Descrição:** Cria um novo jogo futuro.

### Deletar próximo jogo
- **Função:** `deleteUpcomingMatch(clubId, id)`
- **Descrição:** Remove um jogo futuro.

### Listar histórico de partidas
- **Função:** `getMatchHistory(clubId, seasonId?)`
- **Descrição:** Retorna partidas já realizadas.

### Criar histórico de partida
- **Função:** `createMatchHistory(clubId, match)`
- **Descrição:** Cria registro de uma partida realizada.

### Atualizar histórico de partida
- **Função:** `updateMatchHistory(clubId, id, match)`
- **Descrição:** Atualiza dados de uma partida realizada.

### Deletar histórico de partida
- **Função:** `deleteMatchHistory(clubId, id)`
- **Descrição:** Remove uma partida do histórico.

### Atualizar escalação/formation
- **Função:** `updateMatchEscalacao(clubId, matchId, escalacao, formation?)`
- **Descrição:** Atualiza a escalação e formação de um jogo.

---

## <a name="training-goals"></a>Training Goals

### Listar objetivos da semana
- **Função:** `getTrainingGoals(clubId)`
- **Descrição:** Retorna todos os objetivos semanais do clube.

### Criar objetivo semanal
- **Função:** `createTrainingGoal(clubId, goal)`
- **Descrição:** Cria um novo objetivo semanal.

### Atualizar objetivo semanal
- **Função:** `updateTrainingGoal(clubId, id, goal)`
- **Descrição:** Atualiza dados do objetivo semanal.

### Deletar objetivo semanal
- **Função:** `deleteTrainingGoal(clubId, id)`
- **Descrição:** Remove um objetivo semanal.

---

## <a name="agenda"></a>Agenda

### Listar eventos
- **Função:** `getAgendaEvents(clubId)`
- **Descrição:** Retorna todos os eventos da agenda do clube.

### Criar evento
- **Função:** `createAgendaEvent(clubId, event)`
- **Descrição:** Cria um novo evento na agenda.

### Atualizar evento
- **Função:** `updateAgendaEvent(clubId, id, event)`
- **Descrição:** Atualiza dados do evento.

### Deletar evento
- **Função:** `deleteAgendaEvent(clubId, id)`
- **Descrição:** Remove um evento da agenda.

---

## <a name="club-info"></a>Club Info

### Buscar informações do clube
- **Função:** `getClubInfo(clubId)`
- **Descrição:** Retorna informações gerais do clube.

### Atualizar informações do clube
- **Função:** `updateClubInfo(clubId, clubInfo)`
- **Descrição:** Atualiza dados do clube.

---

## <a name="tasks"></a>Tasks

### Listar tarefas
- **Função:** `getTasks(clubId)`
- **Descrição:** Retorna todas as tarefas do clube.

### Criar tarefa
- **Função:** `createTask(clubId, task)`
- **Descrição:** Cria uma nova tarefa.

### Atualizar tarefa
- **Função:** `updateTask(clubId, id, task)`
- **Descrição:** Atualiza dados da tarefa.

### Deletar tarefa
- **Função:** `deleteTask(clubId, id)`
- **Descrição:** Remove uma tarefa.

---

## <a name="club-members"></a>Club Members

### Listar membros do clube
- **Função:** `getClubMembers(clubId)`
- **Descrição:** Retorna todos os membros do clube.

### Adicionar usuário ao clube
- **Função:** `addUserToClub(userId, clubId, role)`
- **Descrição:** Adiciona um usuário ao clube com determinado papel.

### Remover usuário do clube
- **Função:** `removeUserFromClub(userId, clubId)`
- **Descrição:** Remove um usuário do clube.

### Buscar clubes do usuário
- **Função:** `getUserClubs(userId)`
- **Descrição:** Retorna todos os clubes aos quais o usuário pertence.

---

## <a name="medical-records"></a>Medical Records

### Listar registros médicos
- **Função:** `getMedicalRecords(clubId)`
- **Descrição:** Retorna todos os registros médicos do clube.

### Criar registro médico
- **Função:** `createMedicalRecord(clubId, record)`
- **Descrição:** Cria um novo registro médico.

### Atualizar registro médico
- **Função:** `updateMedicalRecord(clubId, id, record)`
- **Descrição:** Atualiza dados do registro médico.

### Deletar registro médico
- **Função:** `deleteMedicalRecord(clubId, id)`
- **Descrição:** Remove um registro médico.

---

## <a name="financeiro"></a>Financeiro

### Listar transações financeiras
- **Função:** `getFinancialTransactions(clubId)`
- **Descrição:** Retorna todas as transações financeiras do clube.

### Criar transação financeira
- **Função:** `createFinancialTransaction(clubId, transaction)`
- **Descrição:** Cria uma nova transação financeira.

### Atualizar transação financeira
- **Função:** `updateFinancialTransaction(clubId, id, transaction)`
- **Descrição:** Atualiza dados da transação financeira.

### Deletar transação financeira
- **Função:** `deleteFinancialTransaction(clubId, id)`
- **Descrição:** Remove uma transação financeira.

---

## <a name="sessions"></a>Sessions

### Listar sessões
- **Função:** `getSessions(clubId)`
- **Descrição:** Retorna todas as sessões do clube.

### Criar sessão
- **Função:** `createSession(clubId, session)`
- **Descrição:** Cria uma nova sessão.

---

## <a name="exercises"></a>Exercises

### Listar exercícios
- **Função:** `getExercises(clubId)`
- **Descrição:** Retorna todos os exercícios cadastrados do clube.

### Criar exercício
- **Função:** `createExercise(clubId, exercise)`
- **Descrição:** Cria um novo exercício.

---

## <a name="physical-progress"></a>Physical Progress

### Listar progresso físico
- **Função:** `getPhysicalProgress(clubId)`
- **Descrição:** Retorna todos os registros de progresso físico do clube.

### Criar progresso físico
- **Função:** `createPhysicalProgress(clubId, progress)`
- **Descrição:** Cria um novo registro de progresso físico.

---

## <a name="training-exercises"></a>Training Exercises

### Listar exercícios do treino
- **Função:** `getTrainingExercises(trainingId)`
- **Descrição:** Retorna todos os exercícios associados a um treino.

### Adicionar exercício ao treino
- **Função:** `addExerciseToTraining(trainingId, exerciseId, order?, notes?)`
- **Descrição:** Associa um exercício a um treino.

### Remover exercício do treino
- **Função:** `removeExerciseFromTraining(trainingExerciseId)`
- **Descrição:** Remove um exercício de um treino.

---

## <a name="salaries-contracts"></a>Salaries/Contracts

### Listar salários
- **Função:** `getSalaries(clubId)`
- **Descrição:** Retorna todos os salários do clube.

### Listar contratos
- **Função:** `getContracts(clubId)`
- **Descrição:** Retorna todos os contratos do clube.

---

## <a name="utilitarios"></a>Utilitários

### Buscar artilheiros
- **Função:** `getTopScorers(clubId, seasonId?)`
- **Descrição:** Retorna os maiores goleadores do clube na temporada.

### Inserir gols
- **Função:** `insertGols(clubId, matchId, gols)`
- **Descrição:** Insere gols em uma partida.

### Parse de escalação
- **Função:** `parseEscalacao(escalacao)`
- **Descrição:** Utilitário para converter formatos de escalação.

---

## Segurança e Autenticação
- Autenticação via Supabase Auth (JWT)
- Multi-clube: cada função exige `clubId`
- Permissões baseadas em usuário e clube

## Tratamento de Erros
- Erros retornam JSON com campo `error` e mensagem
- Códigos: 400 (bad request), 401 (unauthorized), 404 (not found), 500 (server error)

---

## Observações Gerais
- Todos os endpoints exigem o parâmetro clubId para isolamento multi-clube.
- Erros retornam JSON com campo error e mensagem.
- Para detalhes completos, consulte src/api/api.ts.

> Para exemplos de outros domínios (Agenda, Financeiro, Exercícios, etc), consulte as próximas seções.
