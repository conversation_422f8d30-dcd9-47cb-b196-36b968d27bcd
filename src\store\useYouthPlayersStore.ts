import { create } from "zustand";
import { YouthPlayer } from "../api/api";
import { getYouthPlayers, createYouthPlayer, updateYouthPlayer, deleteYouthPlayer } from "../api/api";

interface YouthPlayersState {
  youthPlayers: YouthPlayer[];
  loading: boolean;
  error: string | null;
  fetchYouthPlayers: (clubId: number) => Promise<void>;
  addYouthPlayer: (clubId: number, player: Omit<YouthPlayer, "id">) => Promise<void>;
  updateYouthPlayer: (clubId: number, id: string, player: Partial<YouthPlayer>) => Promise<void>;
  deleteYouthPlayer: (clubId: number, id: string) => Promise<void>;
}

export const useYouthPlayersStore = create<YouthPlayersState>((set) => ({
  youthPlayers: [],
  loading: false,
  error: null,

  fetchYouthPlayers: async (clubId: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const youthPlayers = await getYouthPlayers(clubId);
      set({ youthPlayers, loading: false });
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao buscar jogadores da base", loading: false });
    }
  },

  addYouthPlayer: async (clubId: number, player: Omit<YouthPlayer, "id">): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const newPlayer = await createYouthPlayer(clubId, player);
      set((state) => ({ youthPlayers: [...state.youthPlayers, newPlayer], loading: false }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao adicionar jogador da base", loading: false });
    }
  },

  updateYouthPlayer: async (clubId: number, id: string, player: Partial<YouthPlayer>): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const updated = await updateYouthPlayer(clubId, id, player);
      if (updated) {
        set((state) => ({ youthPlayers: state.youthPlayers.map(p => p.id === id ? updated : p), loading: false }));
      } else {
        set({ error: "Jogador não encontrado", loading: false });
      }
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao atualizar jogador da base", loading: false });
    }
  },

  deleteYouthPlayer: async (clubId: number, id: string): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const ok = await deleteYouthPlayer(clubId, id);
      if (ok) {
        set((state) => ({ youthPlayers: state.youthPlayers.filter(p => p.id !== id), loading: false }));
      } else {
        set({ error: "Jogador não encontrado", loading: false });
      }
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao deletar jogador da base", loading: false });
    }
  },
}));
