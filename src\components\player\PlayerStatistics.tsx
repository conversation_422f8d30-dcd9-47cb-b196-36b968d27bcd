import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useCurrentClubId } from "@/context/ClubContext";
import { Player, getPlayerAggregatedStatistics, getPlayerMatchesWithStats } from "@/api/api";
import { BarChart, Activity, Trophy, Timer, Target, Zap, Network, Shield } from "lucide-react";
import { useUser } from "@/context/UserContext";
import { useToast } from "@/hooks/use-toast";

interface PlayerStatisticsProps {
  player: Player;
}

export function PlayerStatistics({ player }: PlayerStatisticsProps) {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("overview");
  const [loading, setLoading] = useState(true);
  const [aggregatedStats, setAggregatedStats] = useState<any>(null);
  const [matchHistory, setMatchHistory] = useState<any[]>([]);

  // Get player stats from player object or initialize with default values
  const stats = player.stats || {
    games: 0,
    goals: 0,
    assists: 0,
    yellowCards: 0,
    redCards: 0,
    minutesPlayed: 0
  };

  // Fetch aggregated statistics
  useEffect(() => {
    const fetchPlayerData = async () => {
      if (!clubId || !player.id || !user?.id) return;

      try {
        setLoading(true);

        // Carregar estatísticas agregadas
        const statsData = await getPlayerAggregatedStatistics(clubId, player.id, user.id);
        setAggregatedStats(statsData);

        // Carregar histórico de partidas
        const matchesData = await getPlayerMatchesWithStats(clubId, player.id);
        setMatchHistory(matchesData);
      } catch (error) {
        console.error("Error fetching player statistics:", error);
        toast({
          title: "Erro",
          description: "Não foi possível carregar as estatísticas do jogador",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchPlayerData();
  }, [clubId, player.id, user?.id, toast]);

  // Skip rendering for inactive players
  if (player.status === "inativo") {
    return null;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Estatísticas do Jogador</h2>
      </div>

      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="performance">Desempenho</TabsTrigger>
          <TabsTrigger value="history">Histórico</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Jogos</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <Trophy className="h-5 w-5 text-team-blue mr-2" />
                  <span className="text-2xl font-bold">
                    {loading ? "..." : (aggregatedStats?.total_matches || stats.games || 0)}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Gols</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <BarChart className="h-5 w-5 text-team-blue mr-2" />
                  <span className="text-2xl font-bold">
                    {loading ? "..." : (aggregatedStats?.total_goals || stats.goals || 0)}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Assistências</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <Activity className="h-5 w-5 text-team-blue mr-2" />
                  <span className="text-2xl font-bold">
                    {loading ? "..." : (aggregatedStats?.total_assists || stats.assists || 0)}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Cartões Amarelos</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <div className="h-5 w-5 bg-yellow-400 rounded mr-2"></div>
                  <span className="text-2xl font-bold">
                    {loading ? "..." : (aggregatedStats?.total_yellow_cards || stats.yellowCards || 0)}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Cartões Vermelhos</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <div className="h-5 w-5 bg-red-600 rounded mr-2"></div>
                  <span className="text-2xl font-bold">
                    {loading ? "..." : (aggregatedStats?.total_red_cards || stats.redCards || 0)}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Minutos Jogados</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <Timer className="h-5 w-5 text-team-blue mr-2" />
                  <span className="text-2xl font-bold">
                    {loading ? "..." : (aggregatedStats?.total_minutes || stats.minutesPlayed || 0)}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance">
          <Card>
            <CardHeader>
              <CardTitle>Desempenho do Jogador</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8 text-muted-foreground">
                  Carregando estatísticas...
                </div>
              ) : aggregatedStats ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-semibold mb-4">Estatísticas Ofensivas</h3>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between items-center mb-1">
                          <span>Finalizações</span>
                          <span className="font-medium">{aggregatedStats.total_shots}</span>
                        </div>
                        <div className="h-2 bg-muted rounded-full overflow-hidden">
                          <div className="h-full bg-team-blue" style={{ width: `${Math.min(100, aggregatedStats.total_shots / 2)}%` }} />
                        </div>
                      </div>
                      <div>
                        <div className="flex justify-between items-center mb-1">
                          <span>Finalizações no Gol</span>
                          <span className="font-medium">{aggregatedStats.total_shots_on_target}</span>
                        </div>
                        <div className="h-2 bg-muted rounded-full overflow-hidden">
                          <div className="h-full bg-team-blue" style={{ width: `${Math.min(100, aggregatedStats.total_shots_on_target / 2)}%` }} />
                        </div>
                      </div>
                      <div>
                        <div className="flex justify-between items-center mb-1">
                          <span>Precisão de Finalização</span>
                          <span className="font-medium">{aggregatedStats.shot_accuracy.toFixed(1)}%</span>
                        </div>
                        <div className="h-2 bg-muted rounded-full overflow-hidden">
                          <div className="h-full bg-team-blue" style={{ width: `${Math.min(100, aggregatedStats.shot_accuracy)}%` }} />
                        </div>
                      </div>
                      <div>
                        <div className="flex justify-between items-center mb-1">
                          <span>Passes-Chave</span>
                          <span className="font-medium">{aggregatedStats.total_key_passes}</span>
                        </div>
                        <div className="h-2 bg-muted rounded-full overflow-hidden">
                          <div className="h-full bg-team-blue" style={{ width: `${Math.min(100, aggregatedStats.total_key_passes / 2)}%` }} />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-4">Estatísticas Defensivas</h3>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between items-center mb-1">
                          <span>Passes</span>
                          <span className="font-medium">{aggregatedStats.total_passes}</span>
                        </div>
                        <div className="h-2 bg-muted rounded-full overflow-hidden">
                          <div className="h-full bg-team-blue" style={{ width: `${Math.min(100, aggregatedStats.total_passes / 10)}%` }} />
                        </div>
                      </div>
                      <div>
                        <div className="flex justify-between items-center mb-1">
                          <span>Precisão de Passes</span>
                          <span className="font-medium">{aggregatedStats.pass_accuracy.toFixed(1)}%</span>
                        </div>
                        <div className="h-2 bg-muted rounded-full overflow-hidden">
                          <div className="h-full bg-team-blue" style={{ width: `${Math.min(100, aggregatedStats.pass_accuracy)}%` }} />
                        </div>
                      </div>
                      <div>
                        <div className="flex justify-between items-center mb-1">
                          <span>Desarmes</span>
                          <span className="font-medium">{aggregatedStats.total_tackles}</span>
                        </div>
                        <div className="h-2 bg-muted rounded-full overflow-hidden">
                          <div className="h-full bg-team-blue" style={{ width: `${Math.min(100, aggregatedStats.total_tackles / 2)}%` }} />
                        </div>
                      </div>
                      <div>
                        <div className="flex justify-between items-center mb-1">
                          <span>Interceptações</span>
                          <span className="font-medium">{aggregatedStats.total_interceptions}</span>
                        </div>
                        <div className="h-2 bg-muted rounded-full overflow-hidden">
                          <div className="h-full bg-team-blue" style={{ width: `${Math.min(100, aggregatedStats.total_interceptions / 2)}%` }} />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  Nenhuma estatística disponível para este jogador.
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>Histórico de Partidas</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8 text-muted-foreground">
                  Carregando histórico...
                </div>
              ) : matchHistory.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  Nenhum histórico de partidas disponível para este jogador.
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-muted">
                        <th className="border p-2 text-left">Data</th>
                        <th className="border p-2 text-left">Adversário</th>
                        <th className="border p-2 text-center">Min</th>
                        <th className="border p-2 text-center">Gols</th>
                        <th className="border p-2 text-center">Assist</th>
                        <th className="border p-2 text-center">Finalizações</th>
                        <th className="border p-2 text-center">No Gol</th>
                        <th className="border p-2 text-center">Passes</th>
                        <th className="border p-2 text-center">Precisão</th>
                        <th className="border p-2 text-center">CA</th>
                        <th className="border p-2 text-center">CV</th>
                      </tr>
                    </thead>
                    <tbody>
                      {matchHistory.map((match, index) => {
                        const passAccuracy = match.passes ?
                          ((match.passes_completed / match.passes) * 100).toFixed(1) : '0';

                        return (
                          <tr key={index} className="hover:bg-gray-50">
                            <td className="border p-2">{new Date(match.match_date).toLocaleDateString()}</td>
                            <td className="border p-2">{match.opponent_name}</td>
                            <td className="border p-2 text-center">{match.minutes_played || 0}</td>
                            <td className="border p-2 text-center">{match.goals || 0}</td>
                            <td className="border p-2 text-center">{match.assists || 0}</td>
                            <td className="border p-2 text-center">{match.shots || 0}</td>
                            <td className="border p-2 text-center">{match.shots_on_target || 0}</td>
                            <td className="border p-2 text-center">{match.passes_completed || 0}/{match.passes || 0}</td>
                            <td className="border p-2 text-center">{passAccuracy}%</td>
                            <td className="border p-2 text-center">{match.yellow_cards || 0}</td>
                            <td className="border p-2 text-center">{match.red_cards || 0}</td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
