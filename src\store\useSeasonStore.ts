import { create } from 'zustand';
import { getSeasons } from "@/api/seasonApi";

export type Season = {
  id: number;
  club_id: number;
  name: string;
  start_date?: string;
  end_date?: string;
};

interface SeasonStore {
  seasons: Season[];
  activeSeason: Season | null;
  loading: boolean;
  fetchSeasons: (clubId: number) => Promise<void>;
  setActiveSeason: (season: Season) => void;
}

export const useSeasonStore = create<SeasonStore>((set) => ({
  seasons: [],
  activeSeason: null,
  loading: false,
  fetchSeasons: async (clubId) => {
    set({ loading: true });
    try {
      const data = await getSeasons(clubId);
      set({ seasons: data });
      // Verifica se há uma temporada salva no localStorage
      const cachedId = localStorage.getItem("activeSeasonId");
      if (cachedId) {
        const found = data.find(s => String(s.id) === String(cachedId));
        if (found) set({ activeSeason: found });
      }
    } catch (e) {
      set({ seasons: [] });
    } finally {
      set({ loading: false });
    }
  },
  setActiveSeason: (season) => {
    set({ activeSeason: season });
    if (season?.id) {
      localStorage.setItem("activeSeasonId", String(season.id));
    } else {
      localStorage.removeItem("activeSeasonId");
    }
  },
}));
