import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState, useEffect } from "react";
import { toast } from "@/hooks/use-toast";

interface NovoObjetivoDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (goal: TrainingGoalInput) => Promise<void>;
  loading?: boolean;
  initialGoal?: TrainingGoalInput | null;
}

export interface TrainingGoalInput {
  name: string;
  type: string;
  current_value: number;
  target_value: number;
  description?: string;
}

const tipos = ["tático", "técnico", "físico", "outro"];

export function NovoObjetivoDialog({ open, onOpenChange, onSave, loading, initialGoal }: NovoObjetivoDialogProps) {
  const [name, setName] = useState(initialGoal?.name || "");
  const [type, setType] = useState(initialGoal?.type || "tático");
  const [currentValue, setCurrentValue] = useState(initialGoal?.current_value?.toString() || "0");
  const [targetValue, setTargetValue] = useState(initialGoal?.target_value?.toString() || "");
  const [description, setDescription] = useState(initialGoal?.description || "");
  const [error, setError] = useState("");

  useEffect(() => {
    if (open) {
      setName(initialGoal?.name || "");
      setType(initialGoal?.type || "tático");
      setCurrentValue(initialGoal?.current_value?.toString() || "0");
      setTargetValue(initialGoal?.target_value?.toString() || "");
      setDescription(initialGoal?.description || "");
      setError("");
    }
  }, [open, initialGoal]);

  const handleSave = async () => {
    if (!name.trim() || !type || !targetValue) {
      setError("Preencha todos os campos obrigatórios.");
      return;
    }
    if (isNaN(Number(currentValue)) || isNaN(Number(targetValue))) {
      setError("Valores devem ser numéricos.");
      return;
    }
    setError("");
    await onSave({
      name: name.trim(),
      type,
      current_value: Number(currentValue),
      target_value: Number(targetValue),
      description: description.trim(),
    });
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{initialGoal ? "Editar Objetivo" : "Novo Objetivo da Semana"}</DialogTitle>
        </DialogHeader>
        <div className="space-y-3">
          <Input placeholder="Nome do objetivo" value={name} onChange={e => setName(e.target.value)} />
          <select className="w-full border rounded p-2" value={type} onChange={e => setType(e.target.value)}>
            {tipos.map(t => <option key={t} value={t}>{t}</option>)}
          </select>
          <div className="flex gap-2">
            <Input placeholder="Valor atual" type="number" value={currentValue} onChange={e => setCurrentValue(e.target.value)} />
            <Input placeholder="Meta" type="number" value={targetValue} onChange={e => setTargetValue(e.target.value)} />
          </div>
          <Input placeholder="Descrição (opcional)" value={description} onChange={e => setDescription(e.target.value)} />
          {error && <div className="text-red-500 text-sm">{error}</div>}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={loading}>Cancelar</Button>
          <Button onClick={handleSave} isLoading={loading}>{initialGoal ? "Salvar" : "Adicionar"}</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
