-- =====================================================
-- REESTRUTURAÇÃO DO SISTEMA DE ALIMENTAÇÃO
-- Para suportar múltiplos tipos de refeição por sessão
-- =====================================================

-- 1. Adicionar campo meal_type_id à tabela meal_participants
-- Isso permite que cada participante seja associado a um tipo específico de refeição
ALTER TABLE meal_participants 
ADD COLUMN IF NOT EXISTS meal_type_id INTEGER REFERENCES meal_types(id);

-- 2. Criar índice para melhor performance
CREATE INDEX IF NOT EXISTS idx_meal_participants_meal_type_id 
ON meal_participants(meal_type_id);

-- 3. Atualizar dados existentes para manter compatibilidade
-- Preencher meal_type_id com base no meal_type_id da sessão
UPDATE meal_participants 
SET meal_type_id = (
  SELECT meal_type_id 
  FROM meal_sessions 
  WHERE meal_sessions.id = meal_participants.meal_session_id
)
WHERE meal_type_id IS NULL;

-- 4. Tornar o campo obrigatório após preencher os dados existentes
ALTER TABLE meal_participants 
ALTER COLUMN meal_type_id SET NOT NULL;

-- 5. Adicionar foreign key constraint
ALTER TABLE meal_participants 
ADD CONSTRAINT fk_meal_participants_meal_type_id 
FOREIGN KEY (meal_type_id) REFERENCES meal_types(id);

-- 6. Comentários explicativos
COMMENT ON COLUMN meal_participants.meal_type_id IS 'Tipo específico de refeição ao qual o participante está associado';

-- 7. Criar view para facilitar consultas
CREATE OR REPLACE VIEW meal_participants_detailed AS
SELECT 
  mp.*,
  ms.date,
  ms.time,
  ms.accommodation_id,
  mt.name as meal_type_name,
  mt.location as meal_type_location,
  mt.address as meal_type_address,
  a.name as accommodation_name
FROM meal_participants mp
JOIN meal_sessions ms ON mp.meal_session_id = ms.id
JOIN meal_types mt ON mp.meal_type_id = mt.id
JOIN accommodations a ON ms.accommodation_id = a.id;

-- 8. Função para buscar participantes por sessão e tipo de refeição
CREATE OR REPLACE FUNCTION get_meal_participants_by_type(
  p_club_id INTEGER,
  p_meal_session_id INTEGER,
  p_meal_type_id INTEGER DEFAULT NULL
)
RETURNS TABLE (
  id INTEGER,
  participant_id TEXT,
  participant_type TEXT,
  signed BOOLEAN,
  meal_type_id INTEGER,
  meal_type_name TEXT,
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    mp.id,
    mp.participant_id,
    mp.participant_type,
    mp.signed,
    mp.meal_type_id,
    mt.name as meal_type_name,
    mp.created_at
  FROM meal_participants mp
  JOIN meal_types mt ON mp.meal_type_id = mt.id
  WHERE mp.club_id = p_club_id
    AND mp.meal_session_id = p_meal_session_id
    AND (p_meal_type_id IS NULL OR mp.meal_type_id = p_meal_type_id)
  ORDER BY mt.name, mp.participant_type, mp.created_at;
END;
$$ LANGUAGE plpgsql;
