import { ReactNode } from "react";
import { usePermission } from "@/hooks/usePermission";

interface PermissionControlProps {
  children: ReactNode;
  permission?: string;
  permissions?: string[];
  requireAll?: boolean;
  fallback?: ReactNode;
}

/**
 * Componente para renderização condicional baseada em permissões
 * 
 * @param children Conteúdo a ser renderizado se o usuário tiver permissão
 * @param permission Permissão única necessária
 * @param permissions Lista de permissões (alternativa a permission)
 * @param requireAll Se true, o usuário precisa ter todas as permissões da lista. Se false, basta ter uma.
 * @param fallback Conteúdo alternativo a ser renderizado se o usuário não tiver permissão
 */
export function PermissionControl({ 
  children, 
  permission, 
  permissions = [], 
  requireAll = false,
  fallback = null
}: PermissionControlProps) {
  const { isLoaded, can, canAny, canAll } = usePermission();
  
  // Se as permissões ainda não foram carregadas, não renderizar nada
  if (!isLoaded) {
    return null;
  }
  
  // Verificar permissões
  let hasPermission = false;
  
  if (permission) {
    hasPermission = can(permission);
  } else if (permissions.length > 0) {
    hasPermission = requireAll ? canAll(permissions) : canAny(permissions);
  } else {
    // Se não houver permissões especificadas, permitir acesso
    hasPermission = true;
  }
  
  // Renderizar com base na permissão
  return hasPermission ? <>{children}</> : <>{fallback}</>;
}
