import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";

// Tipos
export type Accommodation = Database["public"]["Tables"]["accommodations"]["Row"];
export type HotelRoom = Database["public"]["Tables"]["hotel_rooms"]["Row"];
export type PlayerAccommodation = Database["public"]["Tables"]["player_accommodations"]["Row"] & {
  player_name?: string;
  accommodation_name?: string;
  player_nickname?: string;
  player_birthdate?: string;
  player_entry_date?: string;
  room_number?: string;
  // Add these properties to fix TypeScript errors
  players?: {
    name: string;
    nickname?: string;
    birthdate?: string;
    entry_date?: string;
  };
  accommodations?: {
    name: string;
    type?: string;
  };
  hotel_rooms?: {
    room_number: string;
  };
};

// Funções para gerenciar alojamentos
export async function getAccommodations(clubId: number): Promise<Accommodation[]> {
  const { data, error } = await supabase
    .from("accommodations")
    .select("*")
    .eq("club_id", clubId as any)
    .order("name");

  if (error) {
    console.error("Erro ao buscar alojamentos:", error);
    throw new Error(`Erro ao buscar alojamentos: ${error.message}`);
  }

  return (data || []) as unknown as Accommodation[];
}

// ... (outras funções permanecem as mesmas até getPlayerAccommodations)

export async function getPlayerAccommodations(clubId: number, playerId?: string): Promise<PlayerAccommodation[]> {
  let query = supabase
    .from("player_accommodations")
    .select(`
      *,
      players:player_id (name, nickname, birthdate, entry_date),
      accommodations:accommodation_id (name, type),
      hotel_rooms!hotel_room_id (room_number)
    `)
    .eq("club_id", clubId as any);

  if (playerId) {
    query = query.eq("player_id", playerId as any);
  }

  const { data, error } = await query;

  if (error) {
    console.error("Erro ao buscar alojamentos dos jogadores:", error);
    throw new Error(`Erro ao buscar alojamentos dos jogadores: ${error.message}`);
  }

  // Formatar os dados para incluir os nomes dos jogadores, alojamentos e número do quarto
  return (data || []).map(item => {
    // Garantir que item é do tipo esperado com as propriedades necessárias
    const typedItem = item as unknown as PlayerAccommodation & { hotel_rooms?: { room_number: string } };
    return {
      ...(typedItem as object),
      player_name: typedItem.players?.name,
      player_nickname: typedItem.players?.nickname,
      player_birthdate: typedItem.players?.birthdate,
      player_entry_date: typedItem.players?.entry_date,
      accommodation_name: typedItem.accommodations?.name,
      room_number: typedItem.hotel_rooms?.room_number || typedItem.room_number || ''
    };
  }) as PlayerAccommodation[];
}

// ... (resto das funções permanecem as mesmas)
