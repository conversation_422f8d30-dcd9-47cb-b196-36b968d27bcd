-- <PERSON><PERSON>t para corrigir a estrutura da tabela user_departments

-- 1. Adicionar coluna permissions à tabela user_departments se não existir
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_departments' AND column_name = 'permissions'
    ) THEN
        ALTER TABLE user_departments ADD COLUMN permissions JSONB DEFAULT '{}'::jsonb;
    END IF;
END $$;

-- 2. Adicionar coluna permissions à tabela departments se não existir
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'departments' AND column_name = 'permissions'
    ) THEN
        ALTER TABLE departments ADD COLUMN permissions JSONB DEFAULT '{}'::jsonb;
    END IF;
END $$;

-- 3. Verificar se as tabelas existem e criar se necessário
CREATE TABLE IF NOT EXISTS departments (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  permissions JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS user_departments (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL,
  user_id UUID NOT NULL,
  department_id INTEGER NOT NULL,
  role TEXT NOT NULL,
  permissions JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Mensagem de conclusão
DO $$
BEGIN
    RAISE NOTICE 'Estrutura das tabelas atualizada com sucesso!';
END $$;
