import { useEffect, useState } from "react";
import { useSeasonStore } from "@/store/useSeasonStore";
import { getSeasons, deleteSeason } from "@/api/seasonApi";
import { useCurrentClubId } from "@/context/ClubContext";
import { SeasonDialog } from "@/components/modals/SeasonDialog";
import { SeasonManagerDialog } from "@/components/modals/SeasonManagerDialog";
import { Pencil, Trash2, Plus, Settings } from "lucide-react";
import { toast } from "react-toastify";

export function SeasonSelector() {
  const clubId = useCurrentClubId();
  const { seasons, activeSeason, setActiveSeason, fetchSeasons } = useSeasonStore();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editDialog, setEditDialog] = useState<{ open: boolean; season: any | null }>({ open: false, season: null });
  const [deleteConfirm, setDeleteConfirm] = useState<{ open: boolean; season: any | null }>({ open: false, season: null });
  const [managerOpen, setManagerOpen] = useState(false);

  useEffect(() => {
    if (clubId) fetchSeasons(clubId);
  }, [clubId, fetchSeasons]);

  // Notifica o usuário se a temporada acabou
  useEffect(() => {
    if (activeSeason && activeSeason.end_date) {
      const today = new Date();
      const end = new Date(activeSeason.end_date);
      if (today >= end) {
        toast.info(
          <span>
            A temporada <b>{activeSeason.name}</b> acabou! <br />
            <button
              className="underline text-blue-600 hover:text-blue-800"
              onClick={() => window.location.href = '/estatisticas'}
            >Ver estatísticas totais</button>
          </span>,
          { autoClose: false, toastId: `season-ended-${activeSeason.id}` }
        );
      }
    }
  }, [activeSeason]);

  return (
    <div className="flex flex-col gap-3">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <span className="font-medium text-base text-white">Temporada</span>
          <select
            className="border rounded px-2 py-1 text-sm bg-white shadow-sm focus:ring-2 focus:ring-blue-400 focus:border-blue-400"
            value={activeSeason?.id || ''}
            onChange={e => {
              const selected = seasons.find(s => s.id === Number(e.target.value));
              if (selected) setActiveSeason(selected);
            }}
            style={{ minWidth: 140 }}
          >
            <option value="">Selecione...</option>
            {seasons.map(season => (
              <option key={season.id} value={season.id}>{season.name}</option>
            ))}
          </select>
          <button
            className="p-1 rounded-full border border-blue-200 bg-white hover:bg-blue-50 text-blue-600 ml-1"
            onClick={() => setManagerOpen(true)}
            title="Gerenciar temporadas"
            type="button"
            style={{ lineHeight: 0 }}
          >
            <Settings size={20} />
          </button>
        </div>
        <button
          className="flex items-center gap-1 px-2 py-1 rounded bg-blue-600 text-white hover:bg-blue-700 shadow-sm text-xs font-medium"
          onClick={() => setDialogOpen(true)}
          type="button"
        >
          <Plus size={16} /> Nova
        </button>
      </div>
      <SeasonDialog open={dialogOpen} onOpenChange={setDialogOpen} clubId={clubId} />
      {editDialog.open && (
        <SeasonDialog
          open={editDialog.open}
          onOpenChange={open => setEditDialog({ open, season: open ? editDialog.season : null })}
          clubId={clubId}
          season={{
            ...editDialog.season,
            club_id: (editDialog.season && 'club_id' in editDialog.season)
              ? editDialog.season.club_id
              : clubId
          }}
        />
      )}
      {deleteConfirm.open && (
        <div className="fixed inset-0 bg-black bg-opacity-30 z-50 flex items-center justify-center">
          <div className="bg-white rounded shadow p-6 w-full max-w-xs flex flex-col gap-3">
            <h2 className="font-semibold text-lg mb-2">Excluir Temporada</h2>
            <p>Tem certeza que deseja excluir a temporada <b>{deleteConfirm.season.name}</b>?</p>
            <div className="flex gap-2 mt-2">
              <button type="button" className="flex-1 border rounded py-1" onClick={() => setDeleteConfirm({ open: false, season: null })}>
                Cancelar
              </button>
              <button type="button" className="flex-1 bg-red-600 text-white rounded py-1"
                onClick={async () => {
                  try {
                    // Função de exclusão
                    if (deleteConfirm.season) {
                      const response = await fetch('/api/seasons/' + deleteConfirm.season.id, { method: 'DELETE' });

                      if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.message || 'Erro ao excluir temporada');
                      }

                      toast.success('Temporada excluída com sucesso');

                      // Se a temporada excluída for a ativa, limpar a seleção
                      if (activeSeason && activeSeason.id === deleteConfirm.season.id) {
                        setActiveSeason(null);
                      }

                      await fetchSeasons(clubId);
                    }
                  } catch (error: any) {
                    toast.error(error.message || 'Erro ao excluir temporada');
                    console.error('Erro ao excluir temporada:', error);
                  } finally {
                    setDeleteConfirm({ open: false, season: null });
                  }
                }}>
                Excluir
              </button>
            </div>
          </div>
        </div>
      )}
      <SeasonManagerDialog open={managerOpen} onOpenChange={setManagerOpen} clubId={clubId} />
    </div>
  );
}
