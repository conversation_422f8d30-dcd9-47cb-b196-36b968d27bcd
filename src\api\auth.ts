import { supabase } from './supabaseClient';
import { getUserInvitationByToken } from './permissions';
import { generateRandomPassword } from '@/services/brevoEmailService';
import { sendWelcomeWithCredentials } from './email';

// Obter a URL do site a partir das variáveis de ambiente
const SITE_URL = import.meta.env.VITE_SITE_URL || "http://localhost:3000";

/**
 * Cria um usuário no Supabase Auth sem enviar email de confirmação
 * @param email Email do usuário
 * @param password Senha do usuário
 * @returns Dados do usuário criado e o ID do usuário
 */
export async function createUserWithoutEmail(email: string, password: string): Promise<{ authData: any, userId: string }> {
  // Tentar criar o usuário com a API admin (não envia email)
  try {
    // Criar usuário diretamente usando a API do Supabase
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: email,
      password: password,
      email_confirm: true, // Confirmar email automaticamente
      user_metadata: {
        name: email.split('@')[0]
      }
    });

    if (authError) {
      console.error(`Erro ao criar usuário via API admin: ${authError.message}`, authError);
      throw authError;
    }

    console.log(`Usuário criado com sucesso via API admin: ${authData.user.id}`);
    return { authData, userId: authData.user.id };
  } catch (adminError) {
    console.error(`Erro ao criar usuário via API admin: ${adminError}`, adminError);

    // Fallback para a Edge Function
    console.log(`Tentando criar usuário via Edge Function para: ${email}`);

    try {
      // Importar a função diretamente para evitar dependência circular
      const { createUserDirectly } = await import('@/api/directAuth');

      const result = await createUserDirectly(
        email,
        email.split('@')[0], // Nome padrão baseado no email
        "Game Day Nexus", // Nome padrão do clube
        "user", // Papel padrão
        undefined, // clubId não fornecido
        password // Usar a senha fornecida
      );

      if (!result.success) {
        console.error(`Erro ao criar usuário via Edge Function: ${result.message}`);
        throw new Error(`Erro ao criar usuário: ${result.message}`);
      }

      if (!result.userId) {
        console.error("Erro ao criar usuário: userId não retornado pela Edge Function");
        throw new Error("Erro ao criar usuário: userId não retornado");
      }

      console.log(`Usuário criado com sucesso via Edge Function. ID: ${result.userId}`);

      // Criar um objeto authData similar ao retornado pelo Supabase Auth
      const authData = {
        user: {
          id: result.userId,
          email: email,
          user_metadata: {
            name: email.split('@')[0]
          }
        }
      };

      return { authData, userId: result.userId };
    } catch (edgeFunctionError) {
      console.error(`Erro ao criar usuário via Edge Function: ${edgeFunctionError}`, edgeFunctionError);
      throw new Error(`Erro ao criar usuário: ${edgeFunctionError.message || "Erro desconhecido"}`);
    }
  }
}

/**
 * Confirma o email de um usuário usando a função RPC do banco de dados
 * @param userId ID do usuário
 * @returns true se a operação foi bem-sucedida
 */
export async function confirmUserEmail(userId: string): Promise<boolean> {
  try {
    console.log(`Tentando confirmar email para o usuário: ${userId}`);

    // Primeiro tenta usar a função RPC direta
    const { error: directError } = await supabase.rpc('confirm_user_email_direct', { user_id: userId });

    if (!directError) {
      console.log(`Email confirmado com sucesso para o usuário: ${userId} (método direto)`);
      return true;
    }

    console.error("Erro ao confirmar email do usuário via RPC direta:", directError);

    // Tenta usar a função RPC original
    const { error } = await supabase.rpc('confirm_user_email', { user_id: userId });

    if (error) {
      console.error("Erro ao confirmar email do usuário via RPC original:", error);

      // Se falhar, tenta criar um usuário na tabela users
      try {
        console.log("Tentando método alternativo para confirmar email...");

        // Criar um usuário na tabela users se não existir
        const { error: userError } = await supabase
          .from("users")
          .upsert({
            id: userId,
            email: "", // Será preenchido pelo trigger
            name: "", // Será preenchido pelo trigger
            first_login: true
          })
          .select();

        if (userError) {
          console.error("Erro ao criar/atualizar usuário na tabela users:", userError);
          return false;
        }

        return true; // Assumimos que o usuário foi criado com sucesso
      } catch (directError) {
        console.error("Erro ao confirmar email diretamente:", directError);
        return false;
      }
    }

    console.log(`Email confirmado com sucesso para o usuário: ${userId} (método original)`);
    return true;
  } catch (error) {
    console.error("Erro ao confirmar email do usuário:", error);
    return false;
  }
}

/**
 * Cria um novo usuário no Supabase Auth e envia um email com as credenciais
 * @param email Email do usuário
 * @param password Senha do usuário (opcional, será gerada se não fornecida)
 * @param name Nome do usuário (opcional)
 * @param clubName Nome do clube (opcional)
 * @returns Dados do usuário criado
 */
export async function signUp(email: string, password?: string, name?: string, clubName?: string) {
  // Gerar senha aleatória se não fornecida
  const userPassword = password || generateRandomPassword(10);

  try {
    // Criar usuário no Supabase Auth sem enviar email de confirmação
    console.log(`Criando usuário para ${email} sem enviar email de confirmação`);
    const { authData, userId } = await createUserWithoutEmail(email, userPassword);

    // Tentar confirmar o email usando a função RPC
    try {
      const confirmed = await confirmUserEmail(userId);
      console.log(`Email ${confirmed ? 'confirmado' : 'não confirmado'} para o usuário ${userId}`);
    } catch (confirmError) {
      console.warn("Erro ao confirmar email do usuário:", confirmError);
    }

    // Criar entrada na tabela users se necessário
    try {
      const { error: userError } = await supabase
        .from("users")
        .upsert({
          id: userId,
          email: email,
          name: name || email.split('@')[0],
          first_login: true
        });

      if (userError) {
        console.error("Erro ao criar entrada na tabela users:", userError);
      }
    } catch (userError) {
      console.error("Erro ao criar entrada na tabela users:", userError);
    }

    // Enviar email com as credenciais em todos os casos
    if (name && clubName) {
      try {
        const emailSent = await sendWelcomeWithCredentials(email, name, userPassword, clubName);
        console.log(`Email de boas-vindas ${emailSent ? 'enviado com sucesso' : 'falhou ao enviar'} para ${email}`);
      } catch (emailError) {
        console.error('Erro ao enviar email de boas-vindas:', emailError);
        // Não lançamos erro aqui para não interromper o fluxo principal
      }
    } else {
      console.warn('Nome ou nome do clube não fornecidos, email de boas-vindas não enviado');
    }

    return authData;
  } catch (error) {
    console.error("Erro ao criar usuário:", error);
    throw error;
  }
}

export async function signIn(email: string, password: string) {
  const { data, error } = await supabase.auth.signInWithPassword({ email, password });
  if (error) throw error;
  return data;
}

export async function signOut() {
  try {
    // Fazer logout no Supabase
    const { error } = await supabase.auth.signOut();
    if (error) throw error;

    // Limpar todos os dados de autenticação do localStorage
    localStorage.removeItem("token");
    localStorage.removeItem("userId");
    localStorage.removeItem("clubId");
    localStorage.removeItem("selectedCategoryId");

    // Limpar qualquer outro dado de sessão
    sessionStorage.clear();

    return true;
  } catch (error) {
    console.error("Erro ao fazer logout:", error);
    throw error;
  }
}

export async function getCurrentUser() {
  const { data, error } = await supabase.auth.getUser();
  if (error) throw error;
  return data.user;
}

export function onAuthStateChange(callback: (event: string, session: any) => void) {
  return supabase.auth.onAuthStateChange(callback);
}

/**
 * Atualiza a senha do usuário atual
 * @param password Nova senha do usuário
 * @returns true se a senha foi atualizada com sucesso
 */
export async function updateUserPassword(password: string): Promise<boolean> {
  try {
    const { error } = await supabase.auth.updateUser({
      password: password
    });

    if (error) {
      console.error("Erro ao atualizar senha:", error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error("Erro ao atualizar senha:", error);
    throw error;
  }
}

/**
 * Aceita um convite e cria uma conta com a senha fornecida
 * @param token Token do convite
 * @param password Senha para a nova conta
 * @returns Dados do usuário criado
 */
export async function acceptInvitationWithPassword(token: string, password: string) {
  try {
    console.log(`Iniciando aceitação de convite com token: ${token}`);

    // Validar parâmetros
    if (!token || token.trim() === '') {
      console.error("Token de convite inválido ou vazio");
      throw new Error("Token de convite inválido");
    }

    if (!password || password.trim() === '') {
      console.error("Senha inválida ou vazia");
      throw new Error("Senha inválida");
    }

    // 1. Obter o convite diretamente do banco de dados
    console.log("Buscando convite no banco de dados...");

    const { data: invitationData, error: invitationError } = await supabase
      .from("user_invitations")
      .select("*")
      .eq("token", token)
      .limit(1);

    if (invitationError) {
      console.error(`Erro ao obter convite: ${invitationError.message}`, invitationError);
      throw new Error(`Erro ao obter convite: ${invitationError.message}`);
    }

    if (!invitationData || invitationData.length === 0) {
      console.error(`Nenhum convite encontrado com o token: ${token}`);
      throw new Error("Convite não encontrado");
    }

    const invitation = invitationData[0];
    console.log(`Convite encontrado para o email: ${invitation.email}, status: ${invitation.status}`);

    if (invitation.status !== "pending") {
      console.error(`Convite com status inválido: ${invitation.status}`);
      throw new Error(`Convite já ${invitation.status === "accepted" ? "aceito" : "expirado"}`);
    }

    // 2. Verificar se o convite expirou
    if (new Date(invitation.expires_at) < new Date()) {
      console.error(`Convite expirado. Data de expiração: ${invitation.expires_at}`);

      // Atualizar status do convite para expirado
      const { error: updateError } = await supabase
        .from("user_invitations")
        .update({ status: "expired" })
        .eq("id", invitation.id);

      if (updateError) {
        console.error(`Erro ao atualizar status do convite para expirado: ${updateError.message}`);
      }

      throw new Error("Convite expirado");
    }

    // Verificar se o usuário já existe
    const { data: existingUser, error: checkError } = await supabase
      .from("users")
      .select("id")
      .eq("email", invitation.email)
      .maybeSingle();

    if (checkError) {
      console.error("Erro ao verificar usuário existente:", checkError);
      // Não interromper o fluxo, apenas logar o erro
    }

    // Se o usuário já existe, apenas aceitar o convite
    if (existingUser) {
      console.log(`Usuário já existe com ID ${existingUser.id}, apenas aceitando o convite`);
      await acceptUserInvitation(token, existingUser.id);
      return { user: { id: existingUser.id } };
    }

    // 3. Criar usuário no Supabase Auth sem enviar email de confirmação
    console.log(`Criando novo usuário para o email: ${invitation.email}`);

    let authData, userId;

    try {
      // Criar usuário no Supabase Auth
      const result = await createUserWithoutEmail(invitation.email, invitation.custom_password || password);
      authData = result.authData;
      userId = result.userId;

      console.log(`Usuário criado com sucesso. ID: ${userId}`);
    } catch (createError: any) {
      console.error("Erro detalhado ao criar usuário:", createError);

      // Tentar método alternativo usando a Edge Function diretamente
      console.log("Tentando método alternativo via Edge Function direta...");

      try {
        const { createUserDirectly } = await import('@/api/directAuth');

        // Obter o nome do clube
        let clubName = "Game Day Nexus";
        try {
          const { data: clubData } = await supabase
            .from("club_info")
            .select("name")
            .eq("id", invitation.club_id)
            .single();

          if (clubData?.name) {
            clubName = clubData.name;
          }
        } catch (clubError) {
          console.warn("Erro ao obter nome do clube:", clubError);
        }

        const result = await createUserDirectly(
          invitation.email,
          invitation.name || invitation.email.split('@')[0],
          clubName,
          invitation.role,
          invitation.club_id,
          invitation.custom_password || password
        );

        if (!result.success || !result.userId) {
          console.error("Falha no método alternativo:", result.message);
          throw new Error(`Erro ao criar usuário: ${result.message}`);
        }

        console.log(`Usuário criado com sucesso via método alternativo. ID: ${result.userId}`);

        // Criar um objeto authData similar ao retornado pelo Supabase Auth
        const authData = {
          user: {
            id: result.userId,
            email: invitation.email,
            user_metadata: {
              name: invitation.name || invitation.email.split('@')[0]
            }
          }
        };

        return { authData, userId: result.userId };
      } catch (alternativeError) {
        console.error("Erro no método alternativo:", alternativeError);
        throw new Error(`Erro ao criar usuário: ${createError.message || "Erro desconhecido"}`);
      }
    }

    // Confirmar o email do usuário
    try {
      const confirmed = await confirmUserEmail(userId);
      console.log(`Email ${confirmed ? 'confirmado' : 'não confirmado'} para o usuário ${userId}`);
    } catch (confirmError) {
      console.warn("Erro ao confirmar email do usuário:", confirmError);
      // Não interromper o fluxo
    }

    // Criar entrada na tabela users se necessário
    try {
      const { error: userError } = await supabase
        .from("users")
        .upsert({
          id: userId,
          email: invitation.email,
          name: invitation.name || invitation.email.split('@')[0],
          first_login: true
        });

      if (userError) {
        console.error("Erro ao criar entrada na tabela users:", userError);
      } else {
        console.log("Entrada criada na tabela users com sucesso");
      }
    } catch (userError) {
      console.error("Erro ao criar entrada na tabela users:", userError);
      // Não interromper o fluxo
    }

    // Email já foi confirmado anteriormente

    // 4. Adicionar usuário ao clube
    console.log(`Adicionando usuário ${userId} ao clube ${invitation.club_id} com papel ${invitation.role}`);
    const { error: memberError } = await supabase
      .from("club_members")
      .insert({
        club_id: invitation.club_id,
        user_id: userId,
        role: invitation.role,
        status: "ativo",
        permissions: invitation.permissions || {}, // Adicionar permissões do convite
      });

    if (memberError) {
      console.error(`Erro ao adicionar usuário ao clube: ${memberError.message}`, memberError);
      throw new Error(`Erro ao adicionar usuário ao clube: ${memberError.message}`);
    }

    console.log(`Usuário adicionado ao clube com sucesso`);

    // 5. Se houver departamento, adicionar usuário ao departamento
    if (invitation.department_id) {
      console.log(`Adicionando usuário ${userId} ao departamento ${invitation.department_id}`);
      const { error: departmentError } = await supabase
        .from("user_departments")
        .insert({
          club_id: invitation.club_id,
          user_id: userId,
          department_id: invitation.department_id,
          role: invitation.role,
          permissions: invitation.permissions || {}, // Adicionar permissões do convite
        });

      if (departmentError) {
        console.error(`Erro ao adicionar usuário ao departamento: ${departmentError.message}`, departmentError);
        // Não interromper o fluxo, apenas logar o erro
      } else {
        console.log(`Usuário adicionado ao departamento com sucesso`);
      }
    }

    // 6. Atualizar status do convite
    console.log(`Atualizando status do convite ${invitation.id} para 'accepted'`);
    const { error: invitationError2 } = await supabase
      .from("user_invitations")
      .update({ status: "accepted" })
      .eq("id", invitation.id);

    if (invitationError2) {
      console.error(`Erro ao atualizar status do convite: ${invitationError2.message}`, invitationError2);
      throw new Error(`Erro ao atualizar convite: ${invitationError2.message}`);
    }

    console.log(`Status do convite atualizado com sucesso`);

    return authData;
  } catch (error: any) {
    console.error("Erro ao aceitar convite com senha:", error);
    throw new Error(error.message || "Erro ao aceitar convite");
  }
}
