import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/components/ui/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  FileText,
  Upload,
  ExternalLink,
  Loader2,
  Trash2,
  AlertCircle
} from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { Collaborator } from "@/api/api";
import { useUser } from "@/context/UserContext";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { formatDate } from "@/lib/utils";
import { v4 as uuidv4 } from "uuid";

// Document types
const COLLABORATOR_DOCUMENT_TYPES = [
  "identity",
  "cpf",
  "credential",
  "certificate",
  "other"
];

const COLLABORATOR_DOCUMENT_LABELS: Record<string, string> = {
  "identity": "Documento de Identidade",
  "cpf": "CPF",
  "credential": "Credencial Profissional",
  "certificate": "Certificado",
  "other": "Outro Documento"
};

// Tipo para documentos de colaborador
interface CollaboratorDocument {
  id: number;
  club_id: number;
  collaborator_id: number;
  document_type: string;
  file_url: string;
  status: string;
  uploaded_at: string;
  verified_at?: string;
  verified_by?: string;
  rejection_reason?: string;
  verifier_name?: string;
}

interface ColaboradorDocumentosDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  collaborator: Collaborator;
}

export function ColaboradorDocumentosDialog({
  open,
  onOpenChange,
  clubId,
  collaborator
}: ColaboradorDocumentosDialogProps) {
  const { user } = useUser();
  const [documents, setDocuments] = useState<CollaboratorDocument[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadingDocumentType, setUploadingDocumentType] = useState<string | null>(null);
  const [documentFile, setDocumentFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<CollaboratorDocument | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);

  // Carregar documentos quando o diálogo for aberto
  useEffect(() => {
    if (open && collaborator) {
      fetchDocuments();
    }
  }, [open, collaborator]);

  // Função para buscar documentos
  const fetchDocuments = async () => {
    try {
      setLoading(true);
      setError(null);

      // Buscar documentos diretamente do Supabase usando SQL
      const { data, error } = await supabase.rpc('get_collaborator_documents', {
        p_club_id: clubId,
        p_collaborator_id: collaborator.id
      });

      if (error) {
        throw new Error(`Erro ao buscar documentos: ${error.message}`);
      }

      // Converter os dados para o formato esperado
      const docs = Array.isArray(data) ? data.map(doc => ({
        ...doc,
        verifier_name: null // Simplificando para evitar problemas de tipagem
      })) : [];

      setDocuments(docs as CollaboratorDocument[]);
    } catch (err: any) {
      console.error("Erro ao buscar documentos:", err);
      setError(err.message || "Erro ao buscar documentos");
      toast({
        title: "Erro",
        description: err.message || "Erro ao buscar documentos",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Função para fazer upload de documento
  const handleUploadDocument = async () => {
    if (!uploadingDocumentType || !documentFile || !user) {
      toast({
        title: "Erro",
        description: "Selecione um tipo de documento e um arquivo",
        variant: "destructive",
      });
      return;
    }

    try {
      setUploading(true);

      // 1. Fazer upload do arquivo para o Storage
      const fileExt = documentFile.name.split(".").pop();
      const fileName = `${uploadingDocumentType}-${uuidv4()}.${fileExt}`;
      const filePath = `${clubId}/collaborators/${collaborator.id}/${fileName}`;

      // Upload do arquivo
      const { error: uploadError } = await supabase.storage
        .from("playerdocuments") // Usando o mesmo bucket dos documentos de jogadores
        .upload(filePath, documentFile, {
          cacheControl: "3600",
          upsert: true,
          contentType: documentFile.type,
        });

      if (uploadError) {
        throw new Error(`Erro ao fazer upload do documento: ${uploadError.message}`);
      }

      // Obter URL pública
      const { data: urlData } = supabase.storage
        .from("playerdocuments")
        .getPublicUrl(filePath);

      // 2. Registrar o documento no banco de dados usando SQL direto para evitar problemas de tipagem
      const { error: insertError } = await supabase.rpc('insert_collaborator_document', {
        p_club_id: clubId,
        p_collaborator_id: collaborator.id,
        p_document_type: uploadingDocumentType,
        p_file_url: urlData.publicUrl,
        p_status: 'pending'
      });

      if (insertError) {
        throw new Error(`Erro ao registrar documento: ${insertError.message}`);
      }

      // Recarregar documentos
      await fetchDocuments();

      toast({
        title: "Sucesso",
        description: "Documento enviado com sucesso",
      });

      // Limpar estado
      setUploadingDocumentType(null);
      setDocumentFile(null);
    } catch (err: any) {
      console.error("Erro ao enviar documento:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao enviar documento",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  // Função para iniciar o processo de exclusão de documento
  const handleDeleteDocument = (doc: CollaboratorDocument) => {
    setDocumentToDelete(doc);
    setDeleteDialogOpen(true);
  };

  // Função para confirmar a exclusão do documento
  const confirmDeleteDocument = async () => {
    if (!documentToDelete) return;

    try {
      setDeleting(true);

      // 1. Extrair o caminho do arquivo da URL
      const fileUrl = documentToDelete.file_url;
      const storageUrl = supabase.storage.from('playerdocuments').getPublicUrl('').data.publicUrl;
      const filePath = fileUrl.replace(storageUrl, '');

      // 2. Excluir o arquivo do Storage
      if (filePath) {
        const { error: storageError } = await supabase.storage
          .from('playerdocuments')
          .remove([filePath]);

        if (storageError) {
          console.error("Erro ao excluir arquivo do storage:", storageError);
          // Continuar mesmo com erro no storage, para pelo menos remover o registro do banco
        }
      }

      // 3. Excluir o registro do banco de dados usando SQL direto para evitar problemas de tipagem
      const { error: dbError } = await supabase.rpc('delete_collaborator_document', {
        p_document_id: documentToDelete.id
      });

      if (dbError) {
        throw new Error(`Erro ao excluir documento: ${dbError.message}`);
      }

      // 4. Atualizar a lista de documentos
      setDocuments(documents.filter(d => d.id !== documentToDelete.id));

      toast({
        title: "Sucesso",
        description: "Documento excluído com sucesso",
      });
    } catch (err: any) {
      console.error("Erro ao excluir documento:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao excluir documento",
        variant: "destructive",
      });
    } finally {
      setDeleting(false);
      setDeleteDialogOpen(false);
      setDocumentToDelete(null);
    }
  };

  // Função para renderizar o status do documento
  const renderDocumentStatus = (status: string) => {
    switch (status) {
      case "verified":
        return <Badge className="bg-green-100 text-green-800">Verificado</Badge>;
      case "rejected":
        return <Badge variant="destructive">Rejeitado</Badge>;
      default:
        return <Badge variant="outline">Pendente</Badge>;
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Documentos de {collaborator.full_name}</DialogTitle>
          </DialogHeader>

          {error && (
            <div className="bg-red-50 text-red-700 p-3 rounded-md text-sm mb-4">
              {error}
            </div>
          )}

          <div className="space-y-4">
            {/* Seção de upload */}
            <div className="bg-muted/30 p-4 rounded-md space-y-3">
              <h3 className="text-sm font-medium">Enviar novo documento</h3>
              <div className="grid grid-cols-2 gap-3">
                <Select
                  value={uploadingDocumentType || ""}
                  onValueChange={setUploadingDocumentType}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o tipo de documento" />
                  </SelectTrigger>
                  <SelectContent>
                    {COLLABORATOR_DOCUMENT_TYPES.map((type: string) => (
                      <SelectItem key={type} value={type}>
                        {COLLABORATOR_DOCUMENT_LABELS[type]}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <div className="flex items-center gap-2">
                  <input
                    type="file"
                    id="documentFile"
                    className="hidden"
                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                    onChange={(e) => {
                      if (e.target.files && e.target.files[0]) {
                        setDocumentFile(e.target.files[0]);
                      }
                    }}
                  />
                  <Button
                    variant="outline"
                    onClick={() => document.getElementById("documentFile")?.click()}
                    className="flex-1"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    {documentFile ? documentFile.name : "Selecionar arquivo"}
                  </Button>
                  <Button
                    onClick={handleUploadDocument}
                    disabled={!uploadingDocumentType || !documentFile || uploading}
                  >
                    {uploading ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <FileText className="h-4 w-4 mr-2" />
                    )}
                    Enviar
                  </Button>
                </div>
              </div>
            </div>

            {/* Lista de documentos */}
            {loading ? (
              <div className="text-center py-4">
                <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                <p className="text-sm text-muted-foreground mt-2">Carregando documentos...</p>
              </div>
            ) : documents.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="h-12 w-12 mx-auto mb-2 opacity-20" />
                <p>Nenhum documento encontrado</p>
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Tipo</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Data de Envio</TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {documents.map((doc) => (
                      <TableRow key={doc.id}>
                        <TableCell>
                          {COLLABORATOR_DOCUMENT_LABELS[doc.document_type as string] || doc.document_type}
                        </TableCell>
                        <TableCell>{renderDocumentStatus(doc.status)}</TableCell>
                        <TableCell>{formatDate(doc.uploaded_at)}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              asChild
                            >
                              <a href={doc.file_url} target="_blank" rel="noopener noreferrer">
                                <ExternalLink className="h-4 w-4" />
                              </a>
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="text-red-500 hover:text-red-700 hover:bg-red-50"
                              onClick={() => handleDeleteDocument(doc)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Fechar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo de confirmação para exclusão de documento */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Excluir documento</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir este documento? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleting}>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                confirmDeleteDocument();
              }}
              disabled={deleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {deleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Excluindo...
                </>
              ) : (
                'Excluir'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
