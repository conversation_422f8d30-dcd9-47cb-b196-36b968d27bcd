import { supabase } from "@/integrations/supabase/client";

export type DepartmentType = {
  id: number;
  club_id: number;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
};

export type JobFunction = {
  id: number;
  club_id: number;
  department_type_id: number;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
  department_name?: string; // For joins
};

/**
 * Get all department types for a club
 * @param clubId The club ID
 * @returns List of department types
 */
export async function getDepartmentTypes(clubId: number): Promise<DepartmentType[]> {
  try {
    const { data, error } = await supabase
      .from("department_types")
      .select("*")
      .eq("club_id", clubId)
      .order("name");

    if (error) {
      throw new Error(`Error fetching department types: ${error.message}`);
    }

    return data || [];
  } catch (error: any) {
    console.error("Error fetching department types:", error);
    throw new Error(error.message || "Error fetching department types");
  }
}

/**
 * Create a new department type
 * @param clubId The club ID
 * @param name The department type name
 * @param description Optional description
 * @returns The created department type
 */
export async function createDepartmentType(
  clubId: number,
  name: string,
  description?: string
): Promise<DepartmentType> {
  try {
    const { data, error } = await supabase
      .from("department_types")
      .insert({
        club_id: clubId,
        name,
        description,
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Error creating department type: ${error.message}`);
    }

    return data;
  } catch (error: any) {
    console.error("Error creating department type:", error);
    throw new Error(error.message || "Error creating department type");
  }
}

/**
 * Update a department type
 * @param clubId The club ID
 * @param id The department type ID
 * @param data The data to update
 * @returns The updated department type
 */
export async function updateDepartmentType(
  clubId: number,
  id: number,
  data: Partial<DepartmentType>
): Promise<DepartmentType> {
  try {
    const { data: updatedData, error } = await supabase
      .from("department_types")
      .update({
        ...data,
        updated_at: new Date().toISOString()
      })
      .eq("club_id", clubId)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      throw new Error(`Error updating department type: ${error.message}`);
    }

    return updatedData;
  } catch (error: any) {
    console.error("Error updating department type:", error);
    throw new Error(error.message || "Error updating department type");
  }
}

/**
 * Delete a department type
 * @param clubId The club ID
 * @param id The department type ID
 * @returns Success status
 */
export async function deleteDepartmentType(
  clubId: number,
  id: number
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from("department_types")
      .delete()
      .eq("club_id", clubId)
      .eq("id", id);

    if (error) {
      throw new Error(`Error deleting department type: ${error.message}`);
    }

    return true;
  } catch (error: any) {
    console.error("Error deleting department type:", error);
    throw new Error(error.message || "Error deleting department type");
  }
}

/**
 * Get all job functions for a department type
 * @param clubId The club ID
 * @param departmentTypeId The department type ID
 * @returns List of job functions
 */
export async function getJobFunctions(
  clubId: number,
  departmentTypeId?: number
): Promise<JobFunction[]> {
  try {
    let query = supabase
      .from("job_functions")
      .select(`
        *,
        department_types:department_type_id (
          id,
          name
        )
      `)
      .eq("club_id", clubId);

    if (departmentTypeId) {
      query = query.eq("department_type_id", departmentTypeId);
    }

    const { data, error } = await query.order("name");

    if (error) {
      throw new Error(`Error fetching job functions: ${error.message}`);
    }

    // Format the data to include department name
    return (data || []).map(item => ({
      ...item,
      department_name: item.department_types?.name || "Unknown Department"
    }));
  } catch (error: any) {
    console.error("Error fetching job functions:", error);
    throw new Error(error.message || "Error fetching job functions");
  }
}

/**
 * Create a new job function
 * @param clubId The club ID
 * @param departmentTypeId The department type ID
 * @param name The job function name
 * @param description Optional description
 * @returns The created job function
 */
export async function createJobFunction(
  clubId: number,
  departmentTypeId: number,
  name: string,
  description?: string
): Promise<JobFunction> {
  try {
    const { data, error } = await supabase
      .from("job_functions")
      .insert({
        club_id: clubId,
        department_type_id: departmentTypeId,
        name,
        description,
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Error creating job function: ${error.message}`);
    }

    return data;
  } catch (error: any) {
    console.error("Error creating job function:", error);
    throw new Error(error.message || "Error creating job function");
  }
}

/**
 * Update a job function
 * @param clubId The club ID
 * @param id The job function ID
 * @param data The data to update
 * @returns The updated job function
 */
export async function updateJobFunction(
  clubId: number,
  id: number,
  data: Partial<JobFunction>
): Promise<JobFunction> {
  try {
    const { data: updatedData, error } = await supabase
      .from("job_functions")
      .update({
        ...data,
        updated_at: new Date().toISOString()
      })
      .eq("club_id", clubId)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      throw new Error(`Error updating job function: ${error.message}`);
    }

    return updatedData;
  } catch (error: any) {
    console.error("Error updating job function:", error);
    throw new Error(error.message || "Error updating job function");
  }
}

/**
 * Delete a job function
 * @param clubId The club ID
 * @param id The job function ID
 * @returns Success status
 */
export async function deleteJobFunction(
  clubId: number,
  id: number
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from("job_functions")
      .delete()
      .eq("club_id", clubId)
      .eq("id", id);

    if (error) {
      throw new Error(`Error deleting job function: ${error.message}`);
    }

    return true;
  } catch (error: any) {
    console.error("Error deleting job function:", error);
    throw new Error(error.message || "Error deleting job function");
  }
}