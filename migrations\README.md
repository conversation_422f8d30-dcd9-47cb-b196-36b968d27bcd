# Migrações do Banco de Dados

Este diretório contém scripts SQL para migração e manutenção do banco de dados.

## Scripts Disponíveis

### 1. `permission_system.sql`

Este script implementa o sistema de permissões e logs de auditoria:

- Adiciona coluna de permissões à tabela `user_departments`
- Adiciona coluna de permissões à tabela `departments`
- Cria a tabela `audit_logs` para registro de ações
- Cria índices para melhorar a performance
- Implementa políticas de segurança RLS (Row Level Security)
- Atualiza permissões padrão para papéis existentes

### 2. `fix_user_permissions.sql`

Este script corrige as permissões de usuários existentes após a implementação do sistema de permissões:

- Atualiza a role de donos de clubes para 'president'
- Ad<PERSON><PERSON> todas as permissões para usuários com role 'president'
- Atualiza a role de membros de clubes para 'admin' se não tiverem uma role
- Adiciona permissões básicas para usuários com role 'admin'
- Garante que todos os usuários tenham pelo menos permissões básicas
- Atualiza a tabela de usuários para garantir roles consistentes
- Adiciona um log de auditoria para registrar a operação

## Como Executar

Para executar estes scripts, você pode usar o cliente SQL do Supabase ou qualquer outro cliente SQL que tenha acesso ao seu banco de dados.

### Usando o Cliente SQL do Supabase

1. Faça login no Supabase
2. Vá para a seção "SQL Editor"
3. Copie e cole o conteúdo do script que deseja executar
4. Clique em "Run" para executar o script

### Usando outro Cliente SQL

1. Conecte-se ao seu banco de dados usando as credenciais fornecidas pelo Supabase
2. Copie e cole o conteúdo do script que deseja executar
3. Execute o script

## Ordem de Execução

Para uma instalação limpa, execute os scripts na seguinte ordem:

1. `permission_system.sql` - Implementa o sistema de permissões
2. `fix_user_permissions.sql` - Corrige as permissões de usuários existentes

## Observações

- Faça backup do banco de dados antes de executar qualquer script de migração
- Teste os scripts em um ambiente de desenvolvimento antes de aplicá-los em produção
- Verifique os logs após a execução para garantir que não houve erros
