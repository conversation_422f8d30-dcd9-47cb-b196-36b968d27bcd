import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";
import { Loader2, Printer, Download, FileText, Code } from "lucide-react";
import { useAdministrativeDocumentsStore } from "@/store/useAdministrativeDocumentsStore";
import { AdministrativeDocument } from "@/api/api";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface DocumentoPreviewDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  document: AdministrativeDocument | null;
  clubId: number;
}

export function DocumentoPreviewDialog({
  open,
  onOpenChange,
  document,
  clubId
}: DocumentoPreviewDialogProps) {
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [viewMode, setViewMode] = useState<'pdf' | 'html'>('pdf');

  const { generatePDF } = useAdministrativeDocumentsStore();

  useEffect(() => {
    if (open && document) {
      generateDocumentPreview();
    } else {
      // Clean up URL when dialog closes
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
        setPdfUrl(null);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, document]);

  const generateDocumentPreview = async () => {
    if (!document) return;

    setLoading(true);

    try {
      const pdfBlob = await generatePDF(document);
      const url = URL.createObjectURL(pdfBlob);
      setPdfUrl(url);
    } catch (error) {
      toast({
        title: "Erro ao gerar PDF",
        description: "Ocorreu um erro ao gerar a visualização do documento.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePrint = () => {
    if (!pdfUrl) return;

    const printWindow = window.open(pdfUrl, '_blank');
    if (printWindow) {
      printWindow.addEventListener('load', () => {
        printWindow.print();
      });
    }
  };

  const handleDownload = () => {
    if (!pdfUrl || !document) return;

    // Create a temporary anchor element
    const link = document.createElement('a');
    link.href = pdfUrl;
    link.download = `${document.document_type === 'oficio' ? 'Oficio' : 'Memorando'}_${document.document_number}.pdf`;
    document.body.appendChild(link);
    link.click();

    // Clean up
    setTimeout(() => {
      document.body.removeChild(link);
    }, 100);
  };

  if (!document) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[90vw] sm:max-h-[90vh] w-[90vw] h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>
            {document.document_type === 'oficio' ? 'Ofício' : 'Memorando'} #{document.document_number} - {document.title}
          </DialogTitle>
        </DialogHeader>

        <div className="mb-4">
          <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'pdf' | 'html')}>
            <TabsList>
              <TabsTrigger value="pdf" className="flex items-center gap-1">
                <FileText className="h-4 w-4" />
                <span>Visualização PDF</span>
              </TabsTrigger>
              <TabsTrigger value="html" className="flex items-center gap-1">
                <Code className="h-4 w-4" />
                <span>Visualização HTML</span>
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        <div className="flex-1 h-[calc(90vh-160px)] overflow-auto border rounded-md">
          {loading && viewMode === 'pdf' ? (
            <div className="flex items-center justify-center h-full">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              <span className="ml-2 text-muted-foreground">Gerando visualização...</span>
            </div>
          ) : viewMode === 'pdf' && pdfUrl ? (
            <iframe
              src={pdfUrl}
              className="w-full h-full"
              title={`${document.document_type === 'oficio' ? 'Ofício' : 'Memorando'} #${document.document_number}`}
            />
          ) : viewMode === 'html' ? (
            <div className="p-8 bg-white min-h-full">
              <div className="max-w-3xl mx-auto">
                <h1 className="text-2xl font-bold mb-4 text-center">{document.title}</h1>
                <div
                  className="prose max-w-none"
                  dangerouslySetInnerHTML={{ __html: document.content }}
                />
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-full text-muted-foreground">
              Não foi possível gerar a visualização do documento.
            </div>
          )}
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Fechar
          </Button>
          {viewMode === 'pdf' && (
            <>
              <Button
                variant="outline"
                onClick={handleDownload}
                disabled={!pdfUrl || loading}
                className="gap-2"
              >
                <Download className="h-4 w-4" />
                Download
              </Button>
              <Button
                onClick={handlePrint}
                disabled={!pdfUrl || loading}
                className="gap-2"
              >
                <Printer className="h-4 w-4" />
                Imprimir
              </Button>
            </>
          )}
          {viewMode === 'html' && (
            <Button
              onClick={() => window.print()}
              className="gap-2"
            >
              <Printer className="h-4 w-4" />
              Imprimir HTML
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
