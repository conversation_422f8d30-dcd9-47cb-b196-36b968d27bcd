import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";

// Tipos
export type MealType = Database["public"]["Tables"]["meal_types"]["Row"];
export type MealSession = Database["public"]["Tables"]["meal_sessions"]["Row"];
export type MealParticipant = Database["public"]["Tables"]["meal_participants"]["Row"];

// Tipos estendidos para incluir dados relacionados
export type MealSessionWithDetails = MealSession & {
  meal_type_name?: string;
  accommodation_name?: string;
  participant_count?: number;
  meal_types?: {
    name: string;
    location?: string;
    address?: string;
  };
  accommodations?: {
    name: string;
  };
};

export type MealParticipantWithDetails = MealParticipant & {
  participant_name?: string;
  participant_nickname?: string;
  participant_role?: string;
  meal_type_id?: number;
  players?: {
    name: string;
    nickname?: string;
  };
  collaborators?: {
    full_name: string;
    role?: string;
  };
};

// ===== MEAL TYPES =====

export async function getMealTypes(clubId: number): Promise<MealType[]> {
  const { data, error } = await supabase
    .from("meal_types")
    .select("*")
    .eq("club_id", clubId as any)
    .order("name");

  if (error) {
    console.error("Erro ao buscar tipos de refeição:", error);
    throw new Error(`Erro ao buscar tipos de refeição: ${error.message}`);
  }

  return (data || []) as unknown as MealType[];
}

export async function createMealType(
  clubId: number,
  mealType: Omit<MealType, "id" | "club_id" | "created_at">
): Promise<MealType> {
  const { data, error } = await supabase
    .from("meal_types")
    .insert({
      ...mealType,
      club_id: clubId
    } as any)
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar tipo de refeição:", error);
    throw new Error(`Erro ao criar tipo de refeição: ${error.message}`);
  }

  return data as unknown as MealType;
}

export async function updateMealType(
  clubId: number,
  id: number,
  mealType: Partial<MealType>
): Promise<MealType> {
  const { data, error } = await supabase
    .from("meal_types")
    .update(mealType as any)
    .eq("club_id", clubId as any)
    .eq("id", id as any)
    .select()
    .single();

  if (error) {
    console.error(`Erro ao atualizar tipo de refeição ${id}:`, error);
    throw new Error(`Erro ao atualizar tipo de refeição: ${error.message}`);
  }

  return data as unknown as MealType;
}

export async function deleteMealType(clubId: number, id: number): Promise<boolean> {
  // Verificar se há sessões de refeição usando este tipo
  const { data: sessions, error: sessionsError } = await supabase
    .from("meal_sessions")
    .select("id")
    .eq("club_id", clubId as any)
    .eq("meal_type_id", id as any);

  if (sessionsError) {
    console.error("Erro ao verificar sessões de refeição:", sessionsError);
    throw new Error(`Erro ao verificar sessões de refeição: ${sessionsError.message}`);
  }

  if (sessions && sessions.length > 0) {
    throw new Error("Não é possível excluir este tipo de refeição pois há sessões associadas a ele.");
  }

  const { error } = await supabase
    .from("meal_types")
    .delete()
    .eq("club_id", clubId as any)
    .eq("id", id as any);

  if (error) {
    console.error(`Erro ao excluir tipo de refeição ${id}:`, error);
    throw new Error(`Erro ao excluir tipo de refeição: ${error.message}`);
  }

  return true;
}

// ===== MEAL SESSIONS =====

export async function getMealSessions(clubId: number, accommodationId?: number): Promise<MealSessionWithDetails[]> {
  let query = supabase
    .from("meal_sessions")
    .select(`
      *,
      meal_types:meal_type_id (name, location, address),
      accommodations:accommodation_id (name)
    `)
    .eq("club_id", clubId as any)
    .order("date", { ascending: false })
    .order("time", { ascending: false });

  if (accommodationId) {
    query = query.eq("accommodation_id", accommodationId as any);
  }

  const { data, error } = await query;

  if (error) {
    console.error("Erro ao buscar sessões de refeição:", error);
    throw new Error(`Erro ao buscar sessões de refeição: ${error.message}`);
  }

  // Buscar contagem de participantes para cada sessão
  const sessionsWithCount = await Promise.all(
    (data || []).map(async (session: any) => {
      const { data: participants, error: participantsError } = await supabase
        .from("meal_participants")
        .select("id")
        .eq("meal_session_id", session.id as any);

      if (participantsError) {
        console.error("Erro ao contar participantes:", participantsError);
      }

      return {
        ...session,
        meal_type_name: session.meal_types?.name,
        accommodation_name: session.accommodations?.name,
        participant_count: participants?.length || 0
      };
    })
  );

  return sessionsWithCount as MealSessionWithDetails[];
}

export async function createMealSession(
  clubId: number,
  mealSession: Omit<MealSession, "id" | "club_id" | "created_at">
): Promise<MealSession> {
  const { data, error } = await supabase
    .from("meal_sessions")
    .insert({
      ...mealSession,
      club_id: clubId
    } as any)
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar sessão de refeição:", error);
    throw new Error(`Erro ao criar sessão de refeição: ${error.message}`);
  }

  return data as unknown as MealSession;
}

export async function updateMealSession(
  clubId: number,
  id: number,
  mealSession: Partial<MealSession>
): Promise<MealSession> {
  const { data, error } = await supabase
    .from("meal_sessions")
    .update(mealSession as any)
    .eq("club_id", clubId as any)
    .eq("id", id as any)
    .select()
    .single();

  if (error) {
    console.error(`Erro ao atualizar sessão de refeição ${id}:`, error);
    throw new Error(`Erro ao atualizar sessão de refeição: ${error.message}`);
  }

  return data as unknown as MealSession;
}

export async function deleteMealSession(clubId: number, id: number): Promise<boolean> {
  // Primeiro, excluir todos os participantes da sessão
  const { error: participantsError } = await supabase
    .from("meal_participants")
    .delete()
    .eq("club_id", clubId as any)
    .eq("meal_session_id", id as any);

  if (participantsError) {
    console.error("Erro ao excluir participantes da sessão:", participantsError);
    throw new Error(`Erro ao excluir participantes da sessão: ${participantsError.message}`);
  }

  // Depois, excluir a sessão
  const { error } = await supabase
    .from("meal_sessions")
    .delete()
    .eq("club_id", clubId as any)
    .eq("id", id as any);

  if (error) {
    console.error(`Erro ao excluir sessão de refeição ${id}:`, error);
    throw new Error(`Erro ao excluir sessão de refeição: ${error.message}`);
  }

  return true;
}

// ===== MEAL PARTICIPANTS =====

export async function getMealParticipants(clubId: number, mealSessionId: number): Promise<MealParticipantWithDetails[]> {
  // Buscar participantes sem joins primeiro
  const { data: participants, error } = await supabase
    .from("meal_participants")
    .select("*")
    .eq("club_id", clubId as any)
    .eq("meal_session_id", mealSessionId as any)
    .order("participant_type")
    .order("created_at");

  if (error) {
    console.error("Erro ao buscar participantes da refeição:", error);
    throw new Error(`Erro ao buscar participantes da refeição: ${error.message}`);
  }

  if (!participants || participants.length === 0) {
    return [];
  }

  // Buscar dados dos jogadores e colaboradores separadamente
  const participantsData = participants as any[];
  const playerIds = participantsData
    .filter(p => p.participant_type === 'player')
    .map(p => p.participant_id);

  const collaboratorIds = participantsData
    .filter(p => p.participant_type === 'collaborator')
    .map(p => parseInt(p.participant_id))
    .filter(id => !isNaN(id));

  // Buscar dados dos jogadores
  let playersData: any[] = [];
  if (playerIds.length > 0) {
    const { data: players, error: playersError } = await supabase
      .from("players")
      .select("id, name, nickname")
      .eq("club_id", clubId as any)
      .in("id", playerIds as any);

    if (playersError) {
      console.error("Erro ao buscar dados dos jogadores:", playersError);
    } else {
      playersData = players || [];
    }
  }

  // Buscar dados dos colaboradores
  let collaboratorsData: any[] = [];
  if (collaboratorIds.length > 0) {
    const { data: collaborators, error: collaboratorsError } = await supabase
      .from("collaborators")
      .select("id, full_name, role")
      .eq("club_id", clubId as any)
      .in("id", collaboratorIds as any);

    if (collaboratorsError) {
      console.error("Erro ao buscar dados dos colaboradores:", collaboratorsError);
    } else {
      collaboratorsData = collaborators || [];
    }
  }

  // Combinar os dados
  return participantsData.map((participant: any) => {
    let participantData: any = {};

    if (participant.participant_type === 'player') {
      const player = playersData.find(p => p.id === participant.participant_id);
      participantData = {
        participant_name: player?.name || 'Jogador não encontrado',
        participant_nickname: player?.nickname,
        participant_role: undefined
      };
    } else if (participant.participant_type === 'collaborator') {
      const collaborator = collaboratorsData.find(c => c.id === parseInt(participant.participant_id));
      participantData = {
        participant_name: collaborator?.full_name || 'Colaborador não encontrado',
        participant_nickname: undefined,
        participant_role: collaborator?.role
      };
    }

    return {
      ...participant,
      ...participantData
    };
  }) as MealParticipantWithDetails[];
}

export async function addMealParticipant(
  clubId: number,
  mealSessionId: number,
  participantId: string,
  participantType: 'player' | 'collaborator',
  mealTypeId: number
): Promise<MealParticipant> {
  // Verificar se o participante já está na sessão para este tipo de refeição
  const { data: existing, error: existingError } = await supabase
    .from("meal_participants")
    .select("id")
    .eq("club_id", clubId as any)
    .eq("meal_session_id", mealSessionId as any)
    .eq("participant_id", participantId as any)
    .eq("participant_type", participantType as any)
    .eq("meal_type_id", mealTypeId as any);

  if (existingError) {
    console.error("Erro ao verificar participante existente:", existingError);
    throw new Error(`Erro ao verificar participante existente: ${existingError.message}`);
  }

  if (existing && existing.length > 0) {
    throw new Error("Este participante já está adicionado a este tipo de refeição.");
  }

  const { data, error } = await supabase
    .from("meal_participants")
    .insert({
      club_id: clubId,
      meal_session_id: mealSessionId,
      participant_id: participantId,
      participant_type: participantType,
      meal_type_id: mealTypeId,
      signed: false
    } as any)
    .select()
    .single();

  if (error) {
    console.error("Erro ao adicionar participante à refeição:", error);
    throw new Error(`Erro ao adicionar participante à refeição: ${error.message}`);
  }

  return data as unknown as MealParticipant;
}

export async function removeMealParticipant(clubId: number, participantId: number): Promise<boolean> {
  const { error } = await supabase
    .from("meal_participants")
    .delete()
    .eq("club_id", clubId as any)
    .eq("id", participantId as any);

  if (error) {
    console.error(`Erro ao remover participante ${participantId}:`, error);
    throw new Error(`Erro ao remover participante: ${error.message}`);
  }

  return true;
}

export async function updateMealParticipantSignature(
  clubId: number,
  participantId: number,
  signed: boolean
): Promise<MealParticipant> {
  const { data, error } = await supabase
    .from("meal_participants")
    .update({ signed } as any)
    .eq("club_id", clubId as any)
    .eq("id", participantId as any)
    .select()
    .single();

  if (error) {
    console.error(`Erro ao atualizar assinatura do participante ${participantId}:`, error);
    throw new Error(`Erro ao atualizar assinatura do participante: ${error.message}`);
  }

  return data as unknown as MealParticipant;
}

// Nova função para buscar participantes por tipo de refeição
export async function getMealParticipantsByType(
  clubId: number,
  mealSessionId: number,
  mealTypeId?: number
): Promise<MealParticipantWithDetails[]> {
  let query = supabase
    .from("meal_participants")
    .select("*")
    .eq("club_id", clubId as any)
    .eq("meal_session_id", mealSessionId as any)
    .order("participant_type")
    .order("created_at");

  if (mealTypeId) {
    query = query.eq("meal_type_id", mealTypeId as any);
  }

  const { data: participants, error } = await query;

  if (error) {
    console.error("Erro ao buscar participantes da refeição por tipo:", error);
    throw new Error(`Erro ao buscar participantes da refeição: ${error.message}`);
  }

  if (!participants || participants.length === 0) {
    return [];
  }

  // Buscar dados dos jogadores e colaboradores separadamente
  const participantsData = participants as any[];
  const playerIds = participantsData
    .filter(p => p.participant_type === 'player')
    .map(p => p.participant_id);

  const collaboratorIds = participantsData
    .filter(p => p.participant_type === 'collaborator')
    .map(p => parseInt(p.participant_id))
    .filter(id => !isNaN(id));

  // Buscar dados dos jogadores
  let playersData: any[] = [];
  if (playerIds.length > 0) {
    const { data: players, error: playersError } = await supabase
      .from("players")
      .select("id, name, nickname")
      .eq("club_id", clubId as any)
      .in("id", playerIds as any);

    if (playersError) {
      console.error("Erro ao buscar dados dos jogadores:", playersError);
    } else {
      playersData = players || [];
    }
  }

  // Buscar dados dos colaboradores
  let collaboratorsData: any[] = [];
  if (collaboratorIds.length > 0) {
    const { data: collaborators, error: collaboratorsError } = await supabase
      .from("collaborators")
      .select("id, full_name, role")
      .eq("club_id", clubId as any)
      .in("id", collaboratorIds as any);

    if (collaboratorsError) {
      console.error("Erro ao buscar dados dos colaboradores:", collaboratorsError);
    } else {
      collaboratorsData = collaborators || [];
    }
  }

  // Combinar os dados
  return participantsData.map((participant: any) => {
    let participantData: any = {};

    if (participant.participant_type === 'player') {
      const player = playersData.find(p => p.id === participant.participant_id);
      participantData = {
        participant_name: player?.name || 'Jogador não encontrado',
        participant_nickname: player?.nickname,
        participant_role: undefined
      };
    } else if (participant.participant_type === 'collaborator') {
      const collaborator = collaboratorsData.find(c => c.id === parseInt(participant.participant_id));
      participantData = {
        participant_name: collaborator?.full_name || 'Colaborador não encontrado',
        participant_nickname: undefined,
        participant_role: collaborator?.role
      };
    }

    return {
      ...participant,
      ...participantData
    };
  }) as MealParticipantWithDetails[];
}