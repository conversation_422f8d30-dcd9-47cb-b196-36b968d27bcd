import { hasPermission } from "./permissions";
import { supabase } from "@/integrations/supabase/client";

/**
 * Middleware para verificar permissões antes de executar operações na API
 *
 * @param clubId ID do clube
 * @param userId ID do usuário
 * @param permission Permissão necessária
 * @param operation Função a ser executada se o usuário tiver permissão
 * @returns Resultado da operação
 * @throws Error se o usuário não tiver permissão
 */
export async function withPermission<T>(
  clubId: number,
  userId: string,
  permission: string,
  operation: () => Promise<T>
): Promise<T> {
  // Verificar se o usuário tem a permissão necessária
  const hasAccess = await hasPermission(clubId, userId, permission);

  if (!hasAccess) {
    throw new Error(`Acesso negado: Você não tem permissão para realizar esta operação (${permission})`);
  }

  // Executar a operação
  return operation();
}

/**
 * Middleware para verificar múltiplas permissões antes de executar operações na API
 *
 * @param clubId ID do clube
 * @param userId ID do usuário
 * @param permissions Lista de permissões necessárias
 * @param requireAll Se true, o usuário precisa ter todas as permissões. Se false, basta ter uma.
 * @param operation Função a ser executada se o usuário tiver permissão
 * @returns Resultado da operação
 * @throws Error se o usuário não tiver permissão
 */
export async function withPermissions<T>(
  clubId: number,
  userId: string,
  permissions: string[],
  requireAll: boolean,
  operation: () => Promise<T>
): Promise<T> {
  // Verificar cada permissão
  const permissionChecks = await Promise.all(
    permissions.map(permission => hasPermission(clubId, userId, permission))
  );

  // Verificar se o usuário tem as permissões necessárias
  const hasAccess = requireAll
    ? permissionChecks.every(Boolean)
    : permissionChecks.some(Boolean);

  if (!hasAccess) {
    const permissionsText = permissions.join(", ");
    const requireText = requireAll ? "todas" : "pelo menos uma";
    throw new Error(`Acesso negado: Você não tem ${requireText} das permissões necessárias (${permissionsText})`);
  }

  // Executar a operação
  return operation();
}

/**
 * Middleware para registrar operações em um log de auditoria
 *
 * @param clubId ID do clube
 * @param userId ID do usuário
 * @param action Ação realizada
 * @param details Detalhes da ação
 * @param operation Função a ser executada
 * @returns Resultado da operação
 */
export async function withAuditLog<T>(
  clubId: number,
  userId: string,
  action: string,
  details: Record<string, any>,
  operation: () => Promise<T>
): Promise<T> {
  try {
    // Executar a operação
    const result = await operation();

    // Registrar a operação bem-sucedida
    await logAuditEvent(clubId, userId, action, details, true);

    return result;
  } catch (error) {
    // Registrar a operação com falha
    await logAuditEvent(clubId, userId, action, {
      ...details,
      error: error instanceof Error ? error.message : String(error)
    }, false);

    // Relançar o erro
    throw error;
  }
}

/**
 * Função para registrar eventos de auditoria
 *
 * @param clubId ID do clube
 * @param userId ID do usuário
 * @param action Ação realizada
 * @param details Detalhes da ação
 * @param success Se a operação foi bem-sucedida
 */
async function logAuditEvent(
  clubId: number,
  userId: string,
  action: string,
  details: Record<string, any>,
  success: boolean
): Promise<void> {
  try {
    // Registrar o evento no log de auditoria
    const { error } = await supabase.from("audit_logs").insert({
      club_id: clubId,
      user_id: userId,
      action,
      details,
      success,
      created_at: new Date()
    });

    if (error) {
      console.error("Erro ao registrar log de auditoria no banco de dados:", error);
    }

    // Também logamos no console para depuração
    console.log(`[AUDIT] ${new Date().toISOString()} - Club: ${clubId}, User: ${userId}, Action: ${action}, Success: ${success}`, details);
  } catch (error) {
    // Não queremos que falhas no log de auditoria afetem a operação principal
    console.error("Erro ao registrar log de auditoria:", error);
  }
}
