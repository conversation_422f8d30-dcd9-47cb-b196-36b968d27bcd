import { supabase } from "@/integrations/supabase/client";
import { Season } from "@/store/useSeasonStore";

export async function getSeasons(clubId: number): Promise<Season[]> {
  const { data, error } = await supabase
    .from("seasons")
    .select("*")
    .eq("club_id", clubId);
  if (error) throw new Error(error.message);
  return data as Season[];
}

export async function createSeason(clubId: number, name: string, start_date?: string, end_date?: string): Promise<Season> {
  const { data, error } = await supabase
    .from("seasons")
    .insert({ club_id: clubId, name, start_date, end_date })
    .select()
    .single();
  if (error) throw new Error(error.message);
  return data as Season;
}

// Busca estatísticas agregadas da temporada direto do Supabase
export async function getSeasonStats(seasonId: number, clubId?: number) {
  // Busca jogos da temporada
  const { data: matches, error: matchError } = await supabase
    .from("matches")
    .select("id, result, score_home, score_away")
    .eq("season_id", seasonId)
    .eq("club_id", clubId);
  if (matchError) throw new Error(matchError.message);
  const jogos = matches?.length || 0;
  let vitorias = 0, derrotas = 0, empates = 0, golsPro = 0, golsContra = 0;
  (matches || []).forEach((m: any) => {
    if (m.result === 'win') vitorias++;
    else if (m.result === 'draw') empates++;
    else if (m.result === 'loss') derrotas++;
    // Considerando que score_home é sempre do clube
    golsPro += m.score_home || 0;
    golsContra += m.score_away || 0;
  });

  // Busca jogadores do clube
  const { data: jogadores, error: jogadoresError } = await supabase
    .from("players")
    .select("id")
    .eq("club_id", clubId);
  if (jogadoresError) throw new Error(jogadoresError.message);

  // Busca lesões do clube (não existe season_id)
  const { data: lesoes, error: lesoesError } = await supabase
    .from("medical_records")
    .select("id")
    .eq("club_id", clubId);
  if (lesoesError) throw new Error(lesoesError.message);

  // Busca treinos do clube (não existe season_id)
  const { data: treinos, error: treinosError } = await supabase
    .from("trainings")
    .select("id")
    .eq("club_id", clubId);
  if (treinosError) throw new Error(treinosError.message);

  return {
    jogos,
    vitorias,
    derrotas,
    empates,
    golsPro,
    golsContra,
    jogadores: jogadores?.length || 0,
    lesoes: lesoes?.length || 0,
    treinos: treinos?.length || 0,
  };
}

/**
 * Deletes a season by ID
 * @param clubId The club ID
 * @param seasonId The season ID to delete
 * @returns True if the season was deleted successfully
 * @throws Error if the season cannot be deleted
 */
export async function deleteSeason(clubId: number, seasonId: number): Promise<boolean> {
  // Check if the season is being used in matches
  const { data: matchesData, error: matchesError } = await supabase
    .from("matches")
    .select("id")
    .eq("season_id", seasonId)
    .limit(1);

  if (matchesError) {
    throw new Error(`Erro ao verificar uso da temporada: ${matchesError.message}`);
  }

  if (matchesData && matchesData.length > 0) {
    throw new Error("Não é possível excluir uma temporada que está sendo usada em partidas");
  }

  // Check if the season is being used in competitions
  const { data: competitionsData, error: competitionsError } = await supabase
    .from("competitions")
    .select("id")
    .eq("season_id", seasonId)
    .limit(1);

  if (competitionsError) {
    throw new Error(`Erro ao verificar uso da temporada: ${competitionsError.message}`);
  }

  if (competitionsData && competitionsData.length > 0) {
    throw new Error("Não é possível excluir uma temporada que está sendo usada em competições");
  }

  // Delete the season
  const { error } = await supabase
    .from("seasons")
    .delete()
    .eq("id", seasonId)
    .eq("club_id", clubId);

  if (error) {
    throw new Error(`Erro ao excluir temporada: ${error.message}`);
  }

  return true;
}
