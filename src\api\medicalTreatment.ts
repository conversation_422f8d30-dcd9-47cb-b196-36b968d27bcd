import { supabase } from "@/integrations/supabase/client";
import { withPermission, withAuditLog } from "./middleware";
import { uploadSignature } from "./storage";

// Types
export type MedicalTreatmentEvolution = {
  id: number;
  club_id: number;
  record_id: number;
  date: string;
  description: string;
  procedures: string[];
  response: string;
  professional_id: number;
  status: TreatmentStatus;
  created_at: string;
  signature_url?: string;
};

export type TreatmentStatus = 
  | "Liberado" 
  | "Em tratamento" 
  | "Treina e trata";

export const TREATMENT_PERMISSIONS = {
  VIEW: "medical.treatment.view",
  CREATE: "medical.treatment.create",
  EDIT: "medical.treatment.edit",
  DELETE: "medical.treatment.delete",
};

/**
 * Get all treatment evolutions for a medical record
 * @param clubId The club ID
 * @param recordId The medical record ID
 * @returns Array of treatment evolutions
 */
export async function getTreatmentEvolutions(
  clubId: number,
  recordId: number
): Promise<MedicalTreatmentEvolution[]> {
  try {
    const { data, error } = await supabase
      .from("medical_treatment_evolution")
      .select("*")
      .eq("club_id", clubId)
      .eq("record_id", recordId)
      .order("date", { ascending: false });

    if (error) {
      throw new Error(`Error fetching treatment evolutions: ${error.message}`);
    }

    return data as MedicalTreatmentEvolution[];
  } catch (error: any) {
    console.error("Error in getTreatmentEvolutions:", error);
    throw new Error(error.message || "Failed to fetch treatment evolutions");
  }
}

/**
 * Create a new treatment evolution
 * @param clubId The club ID
 * @param userId The user ID creating the evolution
 * @param evolution The evolution data
 * @returns The created evolution
 */
export async function createTreatmentEvolution(
  clubId: number,
  userId: string,
  evolution: Omit<MedicalTreatmentEvolution, "id" | "club_id" | "created_at" | "signature_url">
): Promise<MedicalTreatmentEvolution> {
  return withPermission(
    clubId,
    userId,
    TREATMENT_PERMISSIONS.CREATE,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "medical.treatment.create",
        { 
          record_id: evolution.record_id,
          date: evolution.date,
          status: evolution.status
        },
        async () => {
          try {
            // Create the evolution
            const { data, error } = await supabase
              .from("medical_treatment_evolution")
              .insert({
                club_id: clubId,
                ...evolution
              })
              .select()
              .single();

            if (error) {
              throw new Error(`Error creating treatment evolution: ${error.message}`);
            }

            // Update the medical record with the latest status and evolution date
            await supabase
              .from("medical_records")
              .update({
                status: evolution.status,
                color_status: evolution.status,
                last_evolution_date: evolution.date,
                updated_at: new Date().toISOString()
              })
              .eq("club_id", clubId)
              .eq("id", evolution.record_id);

            return data as MedicalTreatmentEvolution;
          } catch (error: any) {
            console.error("Error in createTreatmentEvolution:", error);
            throw new Error(error.message || "Failed to create treatment evolution");
          }
        }
      );
    }
  );
}

/**
 * Update an existing treatment evolution
 * @param clubId The club ID
 * @param userId The user ID updating the evolution
 * @param id The evolution ID
 * @param evolution The updated evolution data
 * @returns The updated evolution
 */
export async function updateTreatmentEvolution(
  clubId: number,
  userId: string,
  id: number,
  evolution: Partial<Omit<MedicalTreatmentEvolution, "id" | "club_id" | "created_at">>
): Promise<MedicalTreatmentEvolution> {
  return withPermission(
    clubId,
    userId,
    TREATMENT_PERMISSIONS.EDIT,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "medical.treatment.update",
        { id, ...evolution },
        async () => {
          try {
            const { data, error } = await supabase
              .from("medical_treatment_evolution")
              .update(evolution)
              .eq("club_id", clubId)
              .eq("id", id)
              .select()
              .single();

            if (error) {
              throw new Error(`Error updating treatment evolution: ${error.message}`);
            }

            // If status is updated, update the medical record as well
            if (evolution.status) {
              const record = await supabase
                .from("medical_treatment_evolution")
                .select("record_id")
                .eq("id", id)
                .single();

              if (record.data) {
                await supabase
                  .from("medical_records")
                  .update({
                    status: evolution.status,
                    color_status: evolution.status,
                    updated_at: new Date().toISOString()
                  })
                  .eq("club_id", clubId)
                  .eq("id", record.data.record_id);
              }
            }

            return data as MedicalTreatmentEvolution;
          } catch (error: any) {
            console.error("Error in updateTreatmentEvolution:", error);
            throw new Error(error.message || "Failed to update treatment evolution");
          }
        }
      );
    }
  );
}

/**
 * Delete a treatment evolution
 * @param clubId The club ID
 * @param userId The user ID deleting the evolution
 * @param id The evolution ID
 * @returns True if successful
 */
export async function deleteTreatmentEvolution(
  clubId: number,
  userId: string,
  id: number
): Promise<boolean> {
  return withPermission(
    clubId,
    userId,
    TREATMENT_PERMISSIONS.DELETE,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "medical.treatment.delete",
        { id },
        async () => {
          try {
            // Get the record ID before deleting
            const { data: evolutionData } = await supabase
              .from("medical_treatment_evolution")
              .select("record_id")
              .eq("id", id)
              .single();

            const recordId = evolutionData?.record_id;

            // Delete the evolution
            const { error } = await supabase
              .from("medical_treatment_evolution")
              .delete()
              .eq("club_id", clubId)
              .eq("id", id);

            if (error) {
              throw new Error(`Error deleting treatment evolution: ${error.message}`);
            }

            // If we have a record ID, update the record with the latest evolution
            if (recordId) {
              const { data: latestEvolution } = await supabase
                .from("medical_treatment_evolution")
                .select("*")
                .eq("record_id", recordId)
                .order("date", { ascending: false })
                .limit(1)
                .single();

              if (latestEvolution) {
                await supabase
                  .from("medical_records")
                  .update({
                    status: latestEvolution.status,
                    color_status: latestEvolution.status,
                    last_evolution_date: latestEvolution.date,
                    updated_at: new Date().toISOString()
                  })
                  .eq("club_id", clubId)
                  .eq("id", recordId);
              }
            }

            return true;
          } catch (error: any) {
            console.error("Error in deleteTreatmentEvolution:", error);
            throw new Error(error.message || "Failed to delete treatment evolution");
          }
        }
      );
    }
  );
}

/**
 * Add a digital signature to a treatment evolution
 * @param clubId The club ID
 * @param userId The user ID adding the signature
 * @param evolutionId The evolution ID
 * @param signatureFile The signature file
 * @returns The URL of the uploaded signature
 */
export async function addTreatmentSignature(
  clubId: number,
  userId: string,
  evolutionId: number,
  signatureFile: File
): Promise<string> {
  return withPermission(
    clubId,
    userId,
    TREATMENT_PERMISSIONS.EDIT,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "medical.treatment.sign",
        { evolutionId },
        async () => {
          try {
            // Upload the signature
            const signatureUrl = await uploadSignature(clubId, `treatment_${evolutionId}`, signatureFile);

            // Update the evolution with the signature URL
            const { error } = await supabase
              .from("medical_treatment_evolution")
              .update({ signature_url: signatureUrl })
              .eq("club_id", clubId)
              .eq("id", evolutionId);

            if (error) {
              throw new Error(`Error updating treatment signature: ${error.message}`);
            }

            return signatureUrl;
          } catch (error: any) {
            console.error("Error in addTreatmentSignature:", error);
            throw new Error(error.message || "Failed to add treatment signature");
          }
        }
      );
    }
  );
}
