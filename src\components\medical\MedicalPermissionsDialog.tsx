import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { toast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Separator } from "@/components/ui/separator";
import { Loader2 } from "lucide-react";
import { MEDICAL_PERMISSIONS, MEDICAL_PROFESSIONAL_PERMISSIONS, AGENDA_PERMISSIONS } from "@/constants/permissions";

interface MedicalPermissionsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  professionalId: number;
  professionalName: string;
  userId?: string;
  clubId: number;
}

export function MedicalPermissionsDialog({
  open,
  onOpenChange,
  professionalId,
  professionalName,
  userId,
  clubId,
}: MedicalPermissionsDialogProps) {
  const [loading, setLoading] = useState(false);
  const [permissions, setPermissions] = useState<Record<string, boolean>>({});

  // Fetch current permissions
  useEffect(() => {
    if (open && userId) {
      fetchPermissions();
    }
  }, [open, userId]);

  const fetchPermissions = async () => {
    if (!userId) return;

    try {
      setLoading(true);

      const { data, error } = await supabase
        .from("club_members")
        .select("permissions")
        .eq("club_id", clubId)
        .eq("user_id", userId)
        .single();

      if (error) {
        console.error("Error fetching permissions:", error);
        toast({
          title: "Erro",
          description: "Não foi possível carregar as permissões",
          variant: "destructive",
        });
        return;
      }

      if (data && data.permissions) {
        setPermissions(data.permissions);
      }
    } catch (err) {
      console.error("Error in fetchPermissions:", err);
    } finally {
      setLoading(false);
    }
  };

  const handlePermissionChange = (permission: string, checked: boolean) => {
    setPermissions(prev => ({
      ...prev,
      [permission]: checked,
    }));
  };

  const savePermissions = async () => {
    if (!userId) return;

    try {
      setLoading(true);

      const { error } = await supabase
        .from("club_members")
        .update({ permissions })
        .eq("club_id", clubId)
        .eq("user_id", userId);

      if (error) {
        console.error("Error updating permissions:", error);
        toast({
          title: "Erro",
          description: "Não foi possível atualizar as permissões",
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Sucesso",
        description: "Permissões atualizadas com sucesso",
      });

      onOpenChange(false);
    } catch (err) {
      console.error("Error in savePermissions:", err);
    } finally {
      setLoading(false);
    }
  };

  // Group permissions for better organization
  const permissionGroups = [
    {
      title: "Departamento Médico",
      permissions: Object.values(MEDICAL_PERMISSIONS),
    },
    {
      title: "Profissionais Médicos",
      permissions: Object.values(MEDICAL_PROFESSIONAL_PERMISSIONS),
    },
    {
      title: "Agenda",
      permissions: Object.values(AGENDA_PERMISSIONS),
    },
  ];

  // Get permission label
  const getPermissionLabel = (permission: string) => {
    const labels: Record<string, string> = {
      "medical.view": "Visualizar registros médicos",
      "medical.create": "Criar registros médicos",
      "medical.edit": "Editar registros médicos",
      "medical.delete": "Excluir registros médicos",
      "medical_professionals.view": "Visualizar profissionais médicos",
      "medical_professionals.create": "Cadastrar profissionais médicos",
      "medical_professionals.edit": "Editar profissionais médicos",
      "medical_professionals.delete": "Excluir profissionais médicos",
      "medical_professionals.edit_own": "Editar próprio perfil médico",
      "agenda.view": "Visualizar agenda",
      "agenda.create": "Criar eventos na agenda",
      "agenda.edit": "Editar eventos na agenda",
      "agenda.delete": "Excluir eventos na agenda",
    };

    return labels[permission] || permission;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Gerenciar Permissões - {professionalName}</DialogTitle>
        </DialogHeader>

        <div className="py-4 max-h-[60vh] overflow-y-auto">
          {loading ? (
            <div className="flex justify-center items-center py-4">
              <Loader2 className="h-6 w-6 animate-spin text-gray-500 mr-2" />
              <span>Carregando permissões...</span>
            </div>
          ) : (
            <div className="space-y-6">
              {permissionGroups.map((group, index) => (
                <div key={index} className="space-y-3">
                  <h3 className="font-medium text-sm">{group.title}</h3>
                  <Separator />

                  <div className="space-y-2">
                    {group.permissions.map((permission) => (
                      <div key={permission} className="flex items-center space-x-2">
                        <Checkbox
                          id={permission}
                          checked={!!permissions[permission]}
                          onCheckedChange={(checked) =>
                            handlePermissionChange(permission, checked === true)
                          }
                        />
                        <Label htmlFor={permission} className="text-sm">
                          {getPermissionLabel(permission)}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={savePermissions} disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Salvar Permissões
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
