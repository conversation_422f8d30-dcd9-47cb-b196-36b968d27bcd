import { sendEmailWithBrevo } from "@/services/brevoEmailService";

/**
 * Função para testar o envio de email
 * @param email Email de destino
 * @returns Promise que resolve para true se o email foi enviado com sucesso
 */
export async function testEmailSending(email: string): Promise<boolean> {
  try {
    console.log(`Testando envio de email para ${email}`);
    
    const emailBody = `
      <h2>Teste de Email</h2>
      <p>Este é um email de teste enviado pelo sistema Game Day Nexus.</p>
      <p>Se você recebeu este email, significa que o sistema de envio de emails está funcionando corretamente.</p>
      <p>Atenciosamente,<br>Equipe Game Day Nexus</p>
    `;
    
    const result = await sendEmailWithBrevo({
      to: email,
      subject: "Teste de Email - Game Day Nexus",
      body: emailBody,
    });
    
    console.log(`Resultado do teste de email: ${result ? 'Sucesso' : 'Falha'}`);
    return result;
  } catch (error) {
    console.error("Erro ao testar envio de email:", error);
    return false;
  }
}
