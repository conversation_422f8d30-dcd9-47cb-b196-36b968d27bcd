import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "@/hooks/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { createMedicalProfessionalAccount } from "@/api/api";

interface MedicalProfessionalAccountFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  professionalId: number;
  professionalName: string;
  professionalEmail?: string;
}

export function MedicalProfessionalAccountForm({
  open,
  onOpenChange,
  professionalId,
  professionalName,
  professionalEmail = "",
}: MedicalProfessionalAccountFormProps) {
  const clubId = useCurrentClubId();
  const [email, setEmail] = useState(professionalEmail);
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [sendInvite, setSendInvite] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const handleSubmit = async () => {
    try {
      setError("");
      setLoading(true);

      // Validar campos
      if (!email) {
        throw new Error("O e-mail é obrigatório");
      }

      if (!sendInvite && (!password || !confirmPassword)) {
        throw new Error("A senha é obrigatória quando não envia convite");
      }

      if (!sendInvite && password !== confirmPassword) {
        throw new Error("As senhas não coincidem");
      }

      // Criar conta
      await createMedicalProfessionalAccount(
        clubId,
        professionalId,
        {
          email,
          password: sendInvite ? undefined : password,
          sendInvite,
        }
      );

      toast({
        title: "Sucesso",
        description: sendInvite
          ? "Conta criada e credenciais enviadas por email"
          : "Conta criada com sucesso",
      });

      onOpenChange(false);
    } catch (err: any) {
      console.error("Erro ao criar conta:", err);
      setError(err.message || "Erro ao criar conta");
      toast({
        title: "Erro",
        description: err.message || "Erro ao criar conta",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Criar Conta para {professionalName}</DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="email">E-mail</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="sendInvite"
              checked={sendInvite}
              onCheckedChange={(checked) => setSendInvite(checked as boolean)}
            />
            <Label
              htmlFor="sendInvite"
              className="text-sm font-normal cursor-pointer"
            >
              Gerar senha aleatória e enviar por e-mail (recomendado)
            </Label>
          </div>

          {!sendInvite && (
            <>
              <div className="space-y-2">
                <Label htmlFor="password">Senha</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Senha"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirmar Senha</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="Confirmar senha"
                  required
                />
              </div>
            </>
          )}

          {error && (
            <div className="text-red-500 text-sm mt-1">{error}</div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? "Criando..." : "Criar Conta"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
