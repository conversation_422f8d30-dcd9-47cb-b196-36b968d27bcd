import { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/use-toast";
import {
  Plus,
  ChevronLeft,
  Utensils,
  MapPin,
  Clock,
  Users,
  Edit,
  Trash2,
  FileText,
  Calendar
} from "lucide-react";
import { useCurrentClubId } from "@/context/ClubContext";
import { getAccommodations, Accommodation } from "@/api/accommodations";
import { getClubInfo, ClubInfo } from "@/api/api";
import {
  getMealTypes,
  getMealSessions,
  createMealType,
  createMealSession,
  updateMealType,
  updateMealSession,
  deleteMealType,
  deleteMealSession,
  MealType,
  MealSessionWithDetails
} from "@/api/meals";

export default function Alimentacao() {
  const navigate = useNavigate();
  const clubId = useCurrentClubId();

  // Estados principais
  const [accommodations, setAccommodations] = useState<Accommodation[]>([]);
  const [mealTypes, setMealTypes] = useState<MealType[]>([]);
  const [mealSessions, setMealSessions] = useState<MealSessionWithDetails[]>([]);
  const [selectedAccommodation, setSelectedAccommodation] = useState<Accommodation | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  // Estados dos diálogos
  const [isMealTypeDialogOpen, setIsMealTypeDialogOpen] = useState(false);
  const [isMealSessionDialogOpen, setIsMealSessionDialogOpen] = useState(false);
  const [isEditMealTypeDialogOpen, setIsEditMealTypeDialogOpen] = useState(false);
  const [isEditMealSessionDialogOpen, setIsEditMealSessionDialogOpen] = useState(false);

  // Estados dos formulários
  const [mealTypeForm, setMealTypeForm] = useState({
    name: "",
    location: "",
    address: ""
  });

  const [mealSessionForm, setMealSessionForm] = useState({
    meal_type_id: "",
    accommodation_id: "",
    date: new Date().toISOString().split("T")[0],
    time: "",
    notes: ""
  });

  const [editingMealType, setEditingMealType] = useState<MealType | null>(null);
  const [editingMealSession, setEditingMealSession] = useState<MealSessionWithDetails | null>(null);

  // Função para carregar dados
  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const [accommodationsData, mealTypesData] = await Promise.all([
        getAccommodations(clubId),
        getMealTypes(clubId)
      ]);

      setAccommodations(accommodationsData);
      setMealTypes(mealTypesData);

      if (accommodationsData.length > 0) {
        const firstAccommodation = accommodationsData[0];
        setSelectedAccommodation(firstAccommodation);

        // Carregar sessões de refeição para o primeiro alojamento
        const sessionsData = await getMealSessions(clubId, firstAccommodation.id);
        setMealSessions(sessionsData);
      }

      setLoading(false);
    } catch (error) {
      console.error("Erro ao carregar dados:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar os dados de alimentação.",
        variant: "destructive"
      });
      setLoading(false);
    }
  }, [clubId]);

  // Carregar dados quando o componente montar
  useEffect(() => {
    if (clubId) {
      fetchData();
    }
  }, [clubId, fetchData]);

  // Carregar sessões quando o alojamento selecionado mudar
  useEffect(() => {
    const loadSessions = async () => {
      if (selectedAccommodation) {
        try {
          const sessionsData = await getMealSessions(clubId, selectedAccommodation.id);
          setMealSessions(sessionsData);
        } catch (error) {
          console.error("Erro ao carregar sessões:", error);
        }
      }
    };

    loadSessions();
  }, [selectedAccommodation, clubId]);

  // Filtrar alojamentos com base no termo de pesquisa
  const filteredAccommodations = accommodations.filter(accommodation =>
    accommodation.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    accommodation.address?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handlers para tipos de refeição
  const handleCreateMealType = async () => {
    if (!mealTypeForm.name) {
      toast({
        title: "Erro",
        description: "O nome do tipo de refeição é obrigatório.",
        variant: "destructive"
      });
      return;
    }

    try {
      await createMealType(clubId, mealTypeForm);
      toast({
        title: "Sucesso",
        description: "Tipo de refeição criado com sucesso.",
      });
      setIsMealTypeDialogOpen(false);
      setMealTypeForm({ name: "", location: "", address: "" });
      fetchData();
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message || "Erro ao criar tipo de refeição.",
        variant: "destructive"
      });
    }
  };

  const handleEditMealType = (mealType: MealType) => {
    setEditingMealType(mealType);
    setMealTypeForm({
      name: mealType.name,
      location: mealType.location || "",
      address: mealType.address || ""
    });
    setIsEditMealTypeDialogOpen(true);
  };

  const handleUpdateMealType = async () => {
    if (!editingMealType || !mealTypeForm.name) {
      toast({
        title: "Erro",
        description: "O nome do tipo de refeição é obrigatório.",
        variant: "destructive"
      });
      return;
    }

    try {
      await updateMealType(clubId, editingMealType.id, mealTypeForm);
      toast({
        title: "Sucesso",
        description: "Tipo de refeição atualizado com sucesso.",
      });
      setIsEditMealTypeDialogOpen(false);
      setEditingMealType(null);
      setMealTypeForm({ name: "", location: "", address: "" });
      fetchData();
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message || "Erro ao atualizar tipo de refeição.",
        variant: "destructive"
      });
    }
  };

  const handleDeleteMealType = async (mealType: MealType) => {
    if (window.confirm(`Tem certeza que deseja excluir o tipo de refeição "${mealType.name}"?`)) {
      try {
        await deleteMealType(clubId, mealType.id);
        toast({
          title: "Sucesso",
          description: "Tipo de refeição excluído com sucesso.",
        });
        fetchData();
      } catch (error: any) {
        toast({
          title: "Erro",
          description: error.message || "Erro ao excluir tipo de refeição.",
          variant: "destructive"
        });
      }
    }
  };

  // Handlers para sessões de refeição
  const handleCreateMealSession = async () => {
    if (!mealSessionForm.meal_type_id || !mealSessionForm.accommodation_id || !mealSessionForm.date) {
      toast({
        title: "Erro",
        description: "Tipo de refeição, alojamento e data são obrigatórios.",
        variant: "destructive"
      });
      return;
    }

    try {
      await createMealSession(clubId, {
        meal_type_id: parseInt(mealSessionForm.meal_type_id),
        accommodation_id: parseInt(mealSessionForm.accommodation_id),
        date: mealSessionForm.date,
        time: mealSessionForm.time || null,
        notes: mealSessionForm.notes || null
      });

      toast({
        title: "Sucesso",
        description: "Sessão de alimentação criada com sucesso.",
      });

      setIsMealSessionDialogOpen(false);
      setMealSessionForm({
        meal_type_id: "",
        accommodation_id: "",
        date: new Date().toISOString().split("T")[0],
        time: "",
        notes: ""
      });

      // Recarregar sessões para o alojamento selecionado
      if (selectedAccommodation) {
        const sessionsData = await getMealSessions(clubId, selectedAccommodation.id);
        setMealSessions(sessionsData);
      }
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message || "Erro ao criar sessão de alimentação.",
        variant: "destructive"
      });
    }
  };

  const handleDeleteMealSession = async (session: MealSessionWithDetails) => {
    if (window.confirm(`Tem certeza que deseja excluir esta sessão de alimentação?`)) {
      try {
        await deleteMealSession(clubId, session.id);
        toast({
          title: "Sucesso",
          description: "Sessão de alimentação excluída com sucesso.",
        });

        // Recarregar sessões para o alojamento selecionado
        if (selectedAccommodation) {
          const sessionsData = await getMealSessions(clubId, selectedAccommodation.id);
          setMealSessions(sessionsData);
        }
      } catch (error: any) {
        toast({
          title: "Erro",
          description: error.message || "Erro ao excluir sessão de alimentação.",
          variant: "destructive"
        });
      }
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button variant="ghost" onClick={() => navigate(-1)} className="mr-2">
            <ChevronLeft className="h-4 w-4 mr-1" />
            Voltar
          </Button>
          <h1 className="text-2xl font-bold">Alimentação</h1>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setIsMealTypeDialogOpen(true)} variant="outline">
            <Utensils className="h-4 w-4 mr-1" />
            Novo Tipo de Refeição
          </Button>
          <Button onClick={() => setIsMealSessionDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-1" />
            Nova Alimentação
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Coluna da esquerda - Lista de alojamentos */}
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>Alojamentos</CardTitle>
            <div className="mt-2">
              <Input
                placeholder="Pesquisar alojamentos..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="mb-2"
              />
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <p>Carregando alojamentos...</p>
            ) : filteredAccommodations.length === 0 ? (
              <p>Nenhum alojamento encontrado.</p>
            ) : (
              <div className="space-y-2">
                {filteredAccommodations.map((accommodation) => (
                  <div
                    key={accommodation.id}
                    className={`p-3 rounded-md cursor-pointer hover:bg-gray-100 flex justify-between items-center ${
                      selectedAccommodation?.id === accommodation.id ? "bg-gray-100" : ""
                    }`}
                    onClick={() => setSelectedAccommodation(accommodation)}
                  >
                    <div>
                      <div className="font-medium">{accommodation.name}</div>
                      <div className="text-sm text-gray-500">
                        {accommodation.type === "hotel" ? "Hotel" : "Apartamento"}
                      </div>
                    </div>
                    <Badge variant="outline">
                      {accommodation.capacity || "?"} vagas
                    </Badge>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Coluna da direita - Detalhes do alojamento e sessões */}
        <Card className="md:col-span-2">
          {!selectedAccommodation ? (
            <CardContent className="pt-6">
              <p className="text-center text-gray-500">
                Selecione um alojamento para ver as sessões de alimentação
              </p>
            </CardContent>
          ) : (
            <>
              <CardHeader className="flex flex-row items-start justify-between">
                <div>
                  <CardTitle>{selectedAccommodation.name}</CardTitle>
                  <div className="text-sm text-gray-500 mt-1">
                    {selectedAccommodation.address && (
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 mr-1" />
                        {selectedAccommodation.address}
                      </div>
                    )}
                  </div>
                </div>
                <Button
                  onClick={() => {
                    setMealSessionForm({
                      ...mealSessionForm,
                      accommodation_id: selectedAccommodation.id.toString()
                    });
                    setIsMealSessionDialogOpen(true);
                  }}
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Nova Alimentação
                </Button>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="sessions">
                  <TabsList className="mb-4">
                    <TabsTrigger value="sessions">Sessões de Alimentação</TabsTrigger>
                    <TabsTrigger value="types">Tipos de Refeição</TabsTrigger>
                  </TabsList>

                  <TabsContent value="sessions">
                    {mealSessions.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        Nenhuma sessão de alimentação encontrada para este alojamento
                      </div>
                    ) : (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Data</TableHead>
                            <TableHead>Tipo de Refeição</TableHead>
                            <TableHead>Horário</TableHead>
                            <TableHead>Participantes</TableHead>
                            <TableHead>Ações</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {mealSessions.map((session) => (
                            <TableRow key={session.id}>
                              <TableCell>
                                {new Date(session.date).toLocaleDateString('pt-BR')}
                              </TableCell>
                              <TableCell>{session.meal_type_name}</TableCell>
                              <TableCell>
                                {session.time ? (
                                  <div className="flex items-center">
                                    <Clock className="h-4 w-4 mr-1" />
                                    {session.time}
                                  </div>
                                ) : (
                                  "-"
                                )}
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center">
                                  <Users className="h-4 w-4 mr-1" />
                                  {session.participant_count || 0}
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="flex gap-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => navigate(`/alimentacao/sessao/${session.id}`)}
                                  >
                                    <Users className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleDeleteMealSession(session)}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    )}
                  </TabsContent>

                  <TabsContent value="types">
                    {mealTypes.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        Nenhum tipo de refeição cadastrado
                      </div>
                    ) : (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Nome</TableHead>
                            <TableHead>Local</TableHead>
                            <TableHead>Endereço</TableHead>
                            <TableHead>Ações</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {mealTypes.map((mealType) => (
                            <TableRow key={mealType.id}>
                              <TableCell className="font-medium">{mealType.name}</TableCell>
                              <TableCell>{mealType.location || "-"}</TableCell>
                              <TableCell>{mealType.address || "-"}</TableCell>
                              <TableCell>
                                <div className="flex gap-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleEditMealType(mealType)}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleDeleteMealType(mealType)}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    )}
                  </TabsContent>
                </Tabs>
              </CardContent>
            </>
          )}
        </Card>
      </div>

      {/* Diálogo para criar tipo de refeição */}
      <Dialog open={isMealTypeDialogOpen} onOpenChange={setIsMealTypeDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Novo Tipo de Refeição</DialogTitle>
            <DialogDescription>
              Crie um novo tipo de refeição com local e endereço.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="meal-type-name">Nome *</Label>
              <Input
                id="meal-type-name"
                value={mealTypeForm.name}
                onChange={(e) => setMealTypeForm({ ...mealTypeForm, name: e.target.value })}
                placeholder="Ex: Café da Manhã, Almoço, Jantar"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="meal-type-location">Local</Label>
              <Input
                id="meal-type-location"
                value={mealTypeForm.location}
                onChange={(e) => setMealTypeForm({ ...mealTypeForm, location: e.target.value })}
                placeholder="Ex: Refeitório Principal, Restaurante"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="meal-type-address">Endereço</Label>
              <Input
                id="meal-type-address"
                value={mealTypeForm.address}
                onChange={(e) => setMealTypeForm({ ...mealTypeForm, address: e.target.value })}
                placeholder="Endereço completo do local"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsMealTypeDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleCreateMealType}>
              Criar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para editar tipo de refeição */}
      <Dialog open={isEditMealTypeDialogOpen} onOpenChange={setIsEditMealTypeDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar Tipo de Refeição</DialogTitle>
            <DialogDescription>
              Atualize as informações do tipo de refeição.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="edit-meal-type-name">Nome *</Label>
              <Input
                id="edit-meal-type-name"
                value={mealTypeForm.name}
                onChange={(e) => setMealTypeForm({ ...mealTypeForm, name: e.target.value })}
                placeholder="Ex: Café da Manhã, Almoço, Jantar"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-meal-type-location">Local</Label>
              <Input
                id="edit-meal-type-location"
                value={mealTypeForm.location}
                onChange={(e) => setMealTypeForm({ ...mealTypeForm, location: e.target.value })}
                placeholder="Ex: Refeitório Principal, Restaurante"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-meal-type-address">Endereço</Label>
              <Input
                id="edit-meal-type-address"
                value={mealTypeForm.address}
                onChange={(e) => setMealTypeForm({ ...mealTypeForm, address: e.target.value })}
                placeholder="Endereço completo do local"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditMealTypeDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleUpdateMealType}>
              Salvar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para criar sessão de alimentação */}
      <Dialog open={isMealSessionDialogOpen} onOpenChange={setIsMealSessionDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Nova Sessão de Alimentação</DialogTitle>
            <DialogDescription>
              Crie uma nova sessão de alimentação para um alojamento.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="meal-session-type">Tipo de Refeição *</Label>
              <Select
                value={mealSessionForm.meal_type_id}
                onValueChange={(value) => setMealSessionForm({ ...mealSessionForm, meal_type_id: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o tipo de refeição" />
                </SelectTrigger>
                <SelectContent>
                  {mealTypes.map((mealType) => (
                    <SelectItem key={mealType.id} value={mealType.id.toString()}>
                      {mealType.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="meal-session-accommodation">Alojamento *</Label>
              <Select
                value={mealSessionForm.accommodation_id}
                onValueChange={(value) => setMealSessionForm({ ...mealSessionForm, accommodation_id: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o alojamento" />
                </SelectTrigger>
                <SelectContent>
                  {accommodations.map((accommodation) => (
                    <SelectItem key={accommodation.id} value={accommodation.id.toString()}>
                      {accommodation.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="meal-session-date">Data *</Label>
                <Input
                  id="meal-session-date"
                  type="date"
                  value={mealSessionForm.date}
                  onChange={(e) => setMealSessionForm({ ...mealSessionForm, date: e.target.value })}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="meal-session-time">Horário</Label>
                <Input
                  id="meal-session-time"
                  type="time"
                  value={mealSessionForm.time}
                  onChange={(e) => setMealSessionForm({ ...mealSessionForm, time: e.target.value })}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="meal-session-notes">Observações</Label>
              <Textarea
                id="meal-session-notes"
                value={mealSessionForm.notes}
                onChange={(e) => setMealSessionForm({ ...mealSessionForm, notes: e.target.value })}
                placeholder="Observações sobre a sessão de alimentação"
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsMealSessionDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleCreateMealSession}>
              Criar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
