# Sistema de Alimentação - Game Day Nexus Platform

## Visão Geral

O Sistema de Alimentação é uma nova funcionalidade que permite gerenciar refeições por alojamento, com sistema de arrastar e soltar para adicionar participantes e geração de relatórios em PDF.

## Funcionalidades Implementadas

### 1. Estrutura do Banco de Dados

#### Tabelas Criadas:
- **meal_types**: Tipos de refeição (café da manhã, almoço, jantar, etc.)
- **meal_sessions**: Sessões de alimentação vinculadas a alojamentos
- **meal_participants**: Participantes das refeições (jogadores e colaboradores)

#### Campos Principais:
- **meal_types**: nome, local, endereço
- **meal_sessions**: tipo de refeição, alojamento, data, horário (opcional), observações
- **meal_participants**: participante, tipo (jogador/colaborador), assinatura

### 2. APIs Implementadas

#### Tipos de Refeição:
- `getMealTypes(clubId)` - Listar tipos de refeição
- `createMealType(clubId, mealType)` - Criar novo tipo
- `updateMealType(clubId, id, mealType)` - Atualizar tipo
- `deleteMealType(clubId, id)` - Excluir tipo

#### Sessões de Alimentação:
- `getMealSessions(clubId, accommodationId?)` - Listar sessões
- `createMealSession(clubId, mealSession)` - Criar nova sessão
- `updateMealSession(clubId, id, mealSession)` - Atualizar sessão
- `deleteMealSession(clubId, id)` - Excluir sessão

#### Participantes:
- `getMealParticipants(clubId, mealSessionId)` - Listar participantes
- `addMealParticipant(clubId, mealSessionId, participantId, type)` - Adicionar participante
- `removeMealParticipant(clubId, participantId)` - Remover participante
- `updateMealParticipantSignature(clubId, participantId, signed)` - Atualizar assinatura

### 3. Interface do Usuário

#### Página Principal (/alimentacao):
- Lista de alojamentos na lateral esquerda
- Detalhes do alojamento selecionado na direita
- Abas para "Sessões de Alimentação" e "Tipos de Refeição"
- Botões para criar novos tipos de refeição e sessões

#### Página de Sessão (/alimentacao/sessao/:sessionId):
- Informações detalhadas da sessão
- Sistema drag-and-drop para adicionar participantes
- Lista de usuários disponíveis do alojamento
- Lista de participantes da refeição
- Botão para gerar relatório PDF

### 4. Sistema Drag-and-Drop

#### Funcionalidades:
- Arrastar usuários da lista "Usuários do Alojamento"
- Soltar na área "Participantes da Refeição"
- Validação para evitar duplicatas
- Feedback visual durante o arraste
- Remoção de participantes com botão de lixeira

### 5. Geração de Relatórios

#### Relatório Individual:
- Informações da sessão (data, tipo, local, horário)
- Tabela com participantes separados por tipo
- Campos para assinatura dos participantes
- Campo para assinatura do responsável
- Logo e informações do clube

#### Relatório Consolidado:
- Múltiplas sessões em uma tabela
- Resumo geral com totais
- Formato otimizado para visão geral

### 6. Integração com Sistema Existente

#### Sidebar:
- Nova opção "Alimentação" com ícone de utensílios
- Permissão baseada em "accommodations.view"

#### Rotas:
- `/alimentacao` - Página principal
- `/alimentacao/sessao/:sessionId` - Detalhes da sessão

#### Dados Integrados:
- Alojamentos existentes
- Jogadores e colaboradores dos alojamentos
- Sistema de permissões do clube

## Como Usar

### 1. Criar Tipos de Refeição:
1. Acesse a página "Alimentação"
2. Clique em "Novo Tipo de Refeição"
3. Preencha nome, local e endereço
4. Salve o tipo de refeição

### 2. Criar Sessão de Alimentação:
1. Selecione um alojamento na lista
2. Clique em "Nova Alimentação"
3. Escolha o tipo de refeição e alojamento
4. Defina data e horário (opcional)
5. Adicione observações se necessário

### 3. Gerenciar Participantes:
1. Clique no ícone de usuários na sessão desejada
2. Arraste usuários da lista esquerda para a direita
3. Remova participantes com o botão de lixeira
4. Gere o relatório quando necessário

### 4. Gerar Relatórios:
1. Na página de detalhes da sessão
2. Clique em "Gerar Relatório"
3. O PDF será baixado automaticamente
4. Imprima para coleta de assinaturas

## Estrutura de Arquivos

```
src/
├── api/
│   └── meals.ts                    # APIs do sistema de alimentação
├── pages/
│   ├── Alimentacao.tsx            # Página principal
│   └── AlimentacaoSessao.tsx      # Página de detalhes da sessão
├── utils/
│   └── mealReportGenerator.ts     # Gerador de relatórios PDF
└── integrations/supabase/
    └── types.ts                   # Tipos do banco de dados
```

## Permissões

O sistema utiliza a permissão `accommodations.view` para controlar o acesso, seguindo o padrão do sistema de alojamentos.

## Considerações Técnicas

### Validações:
- Verificação de duplicatas ao adicionar participantes
- Validação de campos obrigatórios nos formulários
- Verificação de dependências antes de excluir tipos de refeição

### Performance:
- Carregamento otimizado de dados relacionados
- Queries eficientes com joins apropriados
- Cache de informações do clube para relatórios

### Responsividade:
- Interface adaptável para diferentes tamanhos de tela
- Sistema drag-and-drop funcional em dispositivos touch
- Layout otimizado para tablets e desktops

## Próximas Melhorias

### Funcionalidades Futuras:
- Sistema de notificações para refeições
- Integração com calendário de eventos
- Histórico de participação dos usuários
- Relatórios estatísticos de alimentação
- Sistema de cardápio/menu
- Controle de custos por refeição

### Melhorias Técnicas:
- Implementação de cache para melhor performance
- Otimização de queries do banco de dados
- Testes automatizados para as APIs
- Documentação da API com Swagger

## Suporte

Para dúvidas ou problemas com o sistema de alimentação, consulte:
1. Esta documentação
2. Código fonte comentado
3. Logs do sistema para debugging
4. Equipe de desenvolvimento
