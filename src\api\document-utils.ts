import { supabase } from "@/integrations/supabase/client";

/**
 * Verifica se um documento existe no Storage
 * @param url URL do documento
 * @returns true se o documento existe
 */
export async function checkDocumentExists(url: string): Promise<boolean> {
  try {
    if (!url) return false;

    // Verificar se a URL é válida
    if (!url.includes("/storage/v1/object/public/")) {
      console.error("URL inválida:", url);
      return false;
    }

    // Extrair o caminho do arquivo da URL
    const path = url.split("/storage/v1/object/public/")[1];
    if (!path) {
      console.error("Caminho inválido:", url);
      return false;
    }

    // Extrair o bucket e o caminho do arquivo
    const [bucket, ...rest] = path.split("/");
    const filePath = rest.join("/");

    // Extrair o diretório e o nome do arquivo
    const dirPath = filePath.split("/").slice(0, -1).join("/");
    const fileName = filePath.split("/").pop();

    if (!fileName) {
      console.error("Nome de arquivo inválido:", url);
      return false;
    }

    console.log("Verificando existência do arquivo:", {
      bucket,
      dirPath,
      fileName
    });

    // Verificar se o arquivo existe
    const { data, error } = await supabase.storage.from(bucket).list(dirPath);

    if (error) {
      console.error("Erro ao verificar documento:", error);
      return false;
    }

    // Verificar se o arquivo está na lista
    const exists = data.some((file) => file.name === fileName);

    if (exists) {
      console.log("Arquivo encontrado:", fileName);
    } else {
      console.log("Arquivo não encontrado:", fileName);
      if (data.length > 0) {
        console.log("Arquivos disponíveis:", data.map(f => f.name));
      } else {
        console.log("Nenhum arquivo encontrado no diretório:", dirPath);
      }
    }

    return exists;
  } catch (error) {
    console.error("Erro ao verificar documento:", error);
    return false;
  }
}

/**
 * Atualiza a URL de um documento no banco de dados
 * @param documentId ID do documento
 * @param newUrl Nova URL do documento
 * @returns true se a URL foi atualizada com sucesso
 */
export async function updateDocumentUrl(documentId: number, newUrl: string): Promise<boolean> {
  try {
    const { error } = await supabase
      .from("player_documents")
      .update({ file_url: newUrl })
      .eq("id", documentId);

    if (error) {
      console.error("Erro ao atualizar URL do documento:", error);
      return false;
    }

    return true;
  } catch (error) {
    console.error("Erro ao atualizar URL do documento:", error);
    return false;
  }
}

/**
 * Verifica e corrige a URL de um documento
 * @param documentId ID do documento
 * @param currentUrl URL atual do documento
 * @returns URL corrigida ou null se não foi possível corrigir
 */
export async function verifyAndFixDocumentUrl(documentId: number, currentUrl: string): Promise<string | null> {
  try {
    if (!currentUrl) return null;

    // Verificar se a URL é válida
    if (!currentUrl.includes("/storage/v1/object/public/")) {
      console.error("URL inválida:", currentUrl);
      return null;
    }

    // Verificar se o documento existe
    const exists = await checkDocumentExists(currentUrl);
    if (exists) {
      console.log("Documento existe:", currentUrl);
      return currentUrl; // URL está correta
    }

    console.log("Documento não existe, tentando corrigir:", currentUrl);

    // Se o documento não existe, tentar encontrar o arquivo no mesmo diretório
    const path = currentUrl.split("/storage/v1/object/public/")[1];
    if (!path) {
      console.error("Caminho inválido:", currentUrl);
      return null;
    }

    // Extrair o bucket e o caminho do arquivo
    const [bucket, ...rest] = path.split("/");
    const filePath = rest.join("/");
    const dirPath = filePath.split("/").slice(0, -1).join("/");

    console.log("Bucket:", bucket);
    console.log("Diretório:", dirPath);

    // Listar arquivos no diretório
    const { data, error } = await supabase.storage.from(bucket).list(dirPath);

    if (error) {
      console.error("Erro ao listar arquivos:", error);
      return null;
    }

    // Se não houver arquivos, não é possível corrigir
    if (!data || data.length === 0) {
      console.error("Nenhum arquivo encontrado no diretório:", dirPath);
      return null;
    }

    console.log("Arquivos encontrados:", data.map(f => f.name));

    // Extrair o tipo de documento da URL atual
    const currentFileName = filePath.split("/").pop() || "";
    const documentType = currentFileName.split("-")[0];

    console.log("Tipo de documento:", documentType);

    // Procurar por um arquivo do mesmo tipo
    const matchingFile = data.find((file) => file.name.startsWith(`${documentType}-`));
    if (!matchingFile) {
      console.error("Nenhum arquivo correspondente encontrado para o tipo:", documentType);
      return null;
    }

    console.log("Arquivo correspondente encontrado:", matchingFile.name);

    // Construir a nova URL
    const urlBase = currentUrl.split("/").slice(0, -1).join("/");
    const newUrl = `${urlBase}/${matchingFile.name}`;

    console.log("Nova URL:", newUrl);

    // Atualizar a URL no banco de dados
    const updated = await updateDocumentUrl(documentId, newUrl);
    if (!updated) {
      console.error("Erro ao atualizar URL no banco de dados");
      return null;
    }

    console.log("URL atualizada com sucesso no banco de dados");
    return newUrl;
  } catch (error) {
    console.error("Erro ao verificar e corrigir URL do documento:", error);
    return null;
  }
}
