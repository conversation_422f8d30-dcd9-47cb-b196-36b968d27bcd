import { useState, useRef } from "react";
import { useTheme } from "@/context/ThemeContext";
import { useClubInfoStore } from "@/store/useClubInfoStore";
import { uploadClubLogo } from "@/api/storage-simple";
import { Upload } from "lucide-react";

interface ClubEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: string;
}

export function ClubEditDialog({ open, onOpenChange, clubId }: ClubEditDialogProps) {
  const { clubInfo, updateClubInfo } = useClubInfoStore();
  const { logo, setLogo, clubName, setClubName } = useTheme();
  const [name, setName] = useState(clubName);
  const [logoUrl, setLogoUrl] = useState(logo || "");
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setUploading(true);
      // Criar URL de preview temporária
      const previewUrl = URL.createObjectURL(file);
      setLogoUrl(previewUrl);

      // Fazer upload do arquivo
      const imageUrl = await uploadClubLogo(clubId, file);

      // Atualizar URL com a URL real do servidor
      setLogoUrl(imageUrl);
    } catch (err) {
      alert("Erro ao fazer upload do logo: " + (err as Error).message);
      setLogoUrl(logo || ""); // Restaurar URL anterior em caso de erro
    } finally {
      setUploading(false);
    }
  };

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      setClubName(name);
      setLogo(logoUrl);
      await updateClubInfo(clubId, { name, logo_url: logoUrl });
      onOpenChange(false);
    } catch (err) {
      alert("Erro ao atualizar informações do clube: " + (err as Error).message);
    } finally {
      setLoading(false);
    }
  };

  if (!open) return null;
  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 z-50 flex items-center justify-center">
      <form className="bg-white rounded shadow p-6 w-full max-w-xs flex flex-col gap-3" onSubmit={handleSave}>
        <h2 className="font-semibold text-lg mb-2">Editar Clube</h2>
        <input
          className="border rounded px-2 py-1"
          placeholder="Nome do clube"
          value={name}
          onChange={e => setName(e.target.value)}
          required
        />
        <div className="flex flex-col items-center gap-2">
          {logoUrl && (
            <img src={logoUrl} alt="Logo preview" className="h-16 w-16 rounded mx-auto border object-contain bg-white" />
          )}

          <div className="flex w-full gap-2">
            <input
              className="border rounded px-2 py-1 flex-1"
              placeholder="URL do logo"
              value={logoUrl}
              onChange={e => setLogoUrl(e.target.value)}
            />
            <div className="relative">
              <input
                ref={fileInputRef}
                type="file"
                className="absolute inset-0 opacity-0 cursor-pointer"
                onChange={handleFileChange}
                accept="image/*"
                disabled={uploading}
              />
              <button
                type="button"
                className="bg-gray-100 border rounded px-2 py-1 flex items-center justify-center"
                disabled={uploading}
              >
                {uploading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
                ) : (
                  <Upload className="h-4 w-4" />
                )}
              </button>
            </div>
          </div>
        </div>
        <div className="flex gap-2 mt-2">
          <button type="button" className="flex-1 border rounded py-1" onClick={() => onOpenChange(false)} disabled={loading}>
            Cancelar
          </button>
          <button type="submit" className="flex-1 bg-blue-600 text-white rounded py-1" disabled={loading}>
            {loading ? "Salvando..." : "Salvar"}
          </button>
        </div>
      </form>
    </div>
  );
}
