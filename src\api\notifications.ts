import { supabase } from "@/integrations/supabase/client";
import { createUniqueNotification } from "./notificationHelpers";

// Tipos
export type Notification = {
  id: number;
  club_id: number;
  user_id: string;
  title: string;
  message?: string;
  description?: string; // Para compatibilidade com a estrutura existente
  type?: string;
  reference_id?: string;
  reference_type?: string;
  read: boolean;
  created_at: string;
  scheduled_for?: string;
  sent?: boolean;
  sent_at?: string;
};

/**
 * Busca as notificações do usuário
 * @param userId ID do usuário
 * @param clubId ID do clube
 * @param limit Limite de notificações a serem retornadas
 * @param onlyUnread Se true, retorna apenas notificações não lidas
 * @returns Lista de notificações
 */
export async function getMedicalNotifications(
  userId: string,
  clubId: number,
  limit: number = 10,
  onlyUnread: boolean = false
): Promise<Notification[]> {
  try {
    let query = supabase
      .from("notifications")
      .select("*")
      .eq("user_id", userId)
      .eq("club_id", clubId)
      .order("created_at", { ascending: false })
      .limit(limit);

    if (onlyUnread) {
      query = query.eq("read", false);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Erro ao buscar notificações: ${error.message}`);
    }

    return data || [];
  } catch (error: any) {
    console.error("Erro ao buscar notificações:", error);
    throw new Error(error.message || "Erro ao buscar notificações");
  }
}

/**
 * Marca uma notificação como lida
 * @param notificationId ID da notificação
 * @param userId ID do usuário
 * @returns Notificação atualizada
 */
export async function markMedicalNotificationAsRead(
  notificationId: number,
  userId: string
): Promise<Notification> {
  try {
    const { data, error } = await supabase
      .from("notifications")
      .update({ read: true })
      .eq("id", notificationId)
      .eq("user_id", userId)
      .select()
      .single();

    if (error) {
      throw new Error(`Erro ao marcar notificação como lida: ${error.message}`);
    }

    return data;
  } catch (error: any) {
    console.error("Erro ao marcar notificação como lida:", error);
    throw new Error(error.message || "Erro ao marcar notificação como lida");
  }
}

/**
 * Marca todas as notificações do usuário como lidas
 * @param userId ID do usuário
 * @param clubId ID do clube
 * @returns Número de notificações atualizadas
 */
export async function markAllMedicalNotificationsAsRead(
  userId: string,
  clubId: number
): Promise<number> {
  try {
    const { data, error } = await supabase
      .from("notifications")
      .update({ read: true })
      .eq("user_id", userId)
      .eq("club_id", clubId)
      .eq("read", false);

    if (error) {
      throw new Error(`Erro ao marcar notificações como lidas: ${error.message}`);
    }

    return data?.length || 0;
  } catch (error: any) {
    console.error("Erro ao marcar notificações como lidas:", error);
    throw new Error(error.message || "Erro ao marcar notificações como lidas");
  }
}

/**
 * Cria uma nova notificação
 * @param notification Dados da notificação
 * @returns Notificação criada
 */
export async function createMedicalNotification(
  notification: Omit<Notification, "id" | "created_at" | "read">
): Promise<Notification> {
  try {
    // Use the helper function to create a unique notification
    const result = await createUniqueNotification(notification);

    // If a similar notification already exists, return it
    if (!result) {
      // Return a dummy notification to avoid breaking existing code
      return {
        id: -1, // Dummy ID
        club_id: notification.club_id,
        user_id: notification.user_id,
        title: notification.title,
        message: notification.message,
        type: notification.type,
        reference_id: notification.reference_id,
        reference_type: notification.reference_type,
        read: false,
        created_at: new Date().toISOString(),
      };
    }

    return result;
  } catch (error: any) {
    console.error("Erro ao criar notificação:", error);
    throw new Error(error.message || "Erro ao criar notificação");
  }
}

/**
 * Exclui uma notificação
 * @param notificationId ID da notificação
 * @param userId ID do usuário
 * @returns true se a notificação foi excluída com sucesso
 */
export async function deleteMedicalNotification(
  notificationId: number,
  userId: string
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from("notifications")
      .delete()
      .eq("id", notificationId)
      .eq("user_id", userId);

    if (error) {
      throw new Error(`Erro ao excluir notificação: ${error.message}`);
    }

    return true;
  } catch (error: any) {
    console.error("Erro ao excluir notificação:", error);
    throw new Error(error.message || "Erro ao excluir notificação");
  }
}

/**
 * Conta o número de notificações não lidas do usuário
 * @param userId ID do usuário
 * @param clubId ID do clube
 * @returns Número de notificações não lidas
 */
export async function countUnreadMedicalNotifications(
  userId: string,
  clubId: number
): Promise<number> {
  try {
    const { count, error } = await supabase
      .from("notifications")
      .select("*", { count: "exact", head: true })
      .eq("user_id", userId)
      .eq("club_id", clubId)
      .eq("read", false);

    if (error) {
      throw new Error(`Erro ao contar notificações não lidas: ${error.message}`);
    }

    return count || 0;
  } catch (error: any) {
    console.error("Erro ao contar notificações não lidas:", error);
    throw new Error(error.message || "Erro ao contar notificações não lidas");
  }
}
